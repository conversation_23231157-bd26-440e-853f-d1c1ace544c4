{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "cryptommer/smsir": "*", "hekmatinasser/verta": "^8.5", "kucoin/kucoin-php-sdk": "~1.1.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "morilog/jalali": "^3.4", "pragmarx/google2fa": "^8.0", "predis/predis": "^2.2", "sdtech/bitgo-laravel": "^1.1", "shetabit/payment": "^5.8", "simplesoftwareio/simple-qrcode": "*", "spatie/image": "^3.6", "spatie/laravel-permission": "^6.7", "yajra/laravel-datatables": "^11.0", "yajra/laravel-datatables-oracle": "^11.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "PavloDotDev\\LaravelTronModule\\": "packages/pavlo-dot-dev/laravel-tron-module/src/"}, "files": ["app/Helper/helpers.php", "app/Helper/coreconstant.php", "app/Helper/corearray.php", "app/Helper/botHelpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}