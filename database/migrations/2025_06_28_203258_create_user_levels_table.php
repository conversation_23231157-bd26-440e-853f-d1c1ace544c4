<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_levels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('level_id');
            $table->unsignedBigInteger('previous_level_id')->nullable();
            $table->timestamp('upgraded_at');
            $table->decimal('total_purchases_at_upgrade', 15, 2)->default(0); // کل خریدها در زمان ارتقا
            $table->text('upgrade_reason')->nullable(); // دلیل ارتقا یا عدم ارتقا
            $table->boolean('is_automatic')->default(false); // آیا ارتقا خودکار بوده یا دستی
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('level_id')->references('id')->on('levels')->onDelete('cascade');
            $table->foreign('previous_level_id')->references('id')->on('levels')->onDelete('set null');

            // هر کاربر فقط یک رکورد فعال در هر زمان
            $table->unique('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_levels');
    }
};
