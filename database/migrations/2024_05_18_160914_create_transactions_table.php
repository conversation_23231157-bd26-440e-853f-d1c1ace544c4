<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['deposit', 'withdraw', 'gift', 'transfer', 'buy', 'sell', 'increase', 'decrease'])->default('deposit');
            $table->decimal('amount', 36, 18);
            $table->integer('price')->nullable();
            $table->unsignedBigInteger('wallet_id')->nullable();
            $table->foreign('wallet_id')->references('id')->on('wallets')->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('wallet_address')->nullable();
            $table->foreignId('currency_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreignId('registrar')->nullable()->constrained('users');
            $table->string('network')->nullable();
            $table->enum('status', ['pending', 'waiting', 'approved', 'declined', 'done'])->default('pending');
            $table->string('description')->default('');
            $table->json('details')->nullable();
            $table->decimal('site_profit', 36, 18)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
