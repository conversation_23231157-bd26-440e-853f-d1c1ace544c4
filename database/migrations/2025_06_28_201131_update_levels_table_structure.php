<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('levels', function (Blueprint $table) {
            $table->string('name')->nullable()->after('title'); // برنزی، نقره‌ای، طلایی، الماس
            $table->string('color')->nullable()->after('name'); // رنگ برای نمایش
            $table->decimal('daily_buy_limit', 15, 2)->nullable()->after('color'); // محدودیت خرید روزانه
            $table->decimal('daily_sell_limit', 15, 2)->nullable()->after('daily_buy_limit'); // محدودیت فروش روزانه
            $table->decimal('daily_withdrawal_limit', 15, 2)->nullable()->after('daily_sell_limit'); // محدودیت برداشت روزانه
            $table->decimal('total_purchase_requirement', 15, 2)->nullable()->after('daily_withdrawal_limit'); // حداقل خرید کل
            $table->integer('days_from_previous_level')->nullable()->after('total_purchase_requirement'); // روزهای لازم از سطح قبلی
            $table->text('restrictions')->nullable()->after('days_from_previous_level'); // محدودیت‌ها
            $table->text('features')->nullable()->after('restrictions'); // ویژگی‌ها
            $table->boolean('is_unlimited_buy')->default(false)->after('features'); // خرید نامحدود
            $table->boolean('is_unlimited_sell')->default(false)->after('is_unlimited_buy'); // فروش نامحدود
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('levels', function (Blueprint $table) {
            $table->dropColumn([
                'name', 'color', 'daily_buy_limit', 'daily_sell_limit', 
                'daily_withdrawal_limit', 'total_purchase_requirement', 
                'days_from_previous_level', 'restrictions', 'features',
                'is_unlimited_buy', 'is_unlimited_sell'
            ]);
        });
    }
};
