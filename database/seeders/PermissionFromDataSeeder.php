<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PermissionFromData;

class PermissionFromDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Dashboard
            ['group' => 'dashboard', 'action' => 'View', 'route' => 'admin.dashboard', 'description' => 'مشاهده داشبورد'],
            
            // User Management
            ['group' => 'users', 'action' => 'View', 'route' => 'admin.users.index', 'description' => 'مشاهده لیست کاربران'],
            ['group' => 'users', 'action' => 'Create', 'route' => 'admin.users.create', 'description' => 'ایجاد کاربر جدید'],
            ['group' => 'users', 'action' => 'Edit', 'route' => 'admin.users.edit', 'description' => 'ویرایش کاربر'],
            ['group' => 'users', 'action' => 'Delete', 'route' => 'admin.users.destroy', 'description' => 'حذف کاربر'],
            ['group' => 'users', 'action' => 'Show', 'route' => 'admin.users.show', 'description' => 'مشاهده جزئیات کاربر'],
            
            // Admin Management
            ['group' => 'admin-management', 'action' => 'View', 'route' => 'admin.admin-management.index', 'description' => 'مشاهده لیست ادمین‌ها'],
            ['group' => 'admin-management', 'action' => 'Create', 'route' => 'admin.admin-management.create', 'description' => 'ایجاد ادمین جدید'],
            ['group' => 'admin-management', 'action' => 'Edit', 'route' => 'admin.admin-management.edit', 'description' => 'ویرایش ادمین'],
            ['group' => 'admin-management', 'action' => 'Delete', 'route' => 'admin.admin-management.destroy', 'description' => 'حذف ادمین'],
            ['group' => 'admin-management', 'action' => 'Show', 'route' => 'admin.admin-management.show', 'description' => 'مشاهده جزئیات ادمین'],
            ['group' => 'admin-management', 'action' => 'Permissions', 'route' => 'admin.admin-management.permissions', 'description' => 'مدیریت دسترسی‌ها'],
            
            // Financial Management
            ['group' => 'financial', 'action' => 'View', 'route' => 'admin.accounting.settlement.index', 'description' => 'مشاهده تسویه حساب'],
            ['group' => 'financial', 'action' => 'View', 'route' => 'admin.deposit.index', 'description' => 'مشاهده واریزی‌ها'],
            ['group' => 'financial', 'action' => 'View', 'route' => 'admin.withdrawal.index', 'description' => 'مشاهده برداشت‌ها'],
            
            // Transaction Management
            ['group' => 'transactions', 'action' => 'View', 'route' => 'admin.transaction.index', 'description' => 'مشاهده تراکنش‌ها'],
            ['group' => 'transactions', 'action' => 'Edit', 'route' => 'admin.transaction.edit', 'description' => 'ویرایش تراکنش'],
            
            // Support Management
            ['group' => 'support', 'action' => 'View', 'route' => 'admin.support.index', 'description' => 'مشاهده تیکت‌ها'],
            ['group' => 'support', 'action' => 'Reply', 'route' => 'admin.support.reply', 'description' => 'پاسخ به تیکت'],
            
            // Settings
            ['group' => 'settings', 'action' => 'View', 'route' => 'admin.settings.index', 'description' => 'مشاهده تنظیمات'],
            ['group' => 'settings', 'action' => 'Edit', 'route' => 'admin.settings.edit', 'description' => 'ویرایش تنظیمات'],
            
            // Coin Management
            ['group' => 'coins', 'action' => 'View', 'route' => 'admin.coins.index', 'description' => 'مشاهده ارزها'],
            ['group' => 'coins', 'action' => 'Create', 'route' => 'admin.coins.create', 'description' => 'ایجاد ارز جدید'],
            ['group' => 'coins', 'action' => 'Edit', 'route' => 'admin.coins.edit', 'description' => 'ویرایش ارز'],
            ['group' => 'coins', 'action' => 'Delete', 'route' => 'admin.coins.destroy', 'description' => 'حذف ارز'],
            
            // Network Management
            ['group' => 'networks', 'action' => 'View', 'route' => 'admin.networks.index', 'description' => 'مشاهده شبکه‌ها'],
            ['group' => 'networks', 'action' => 'Create', 'route' => 'admin.networks.create', 'description' => 'ایجاد شبکه جدید'],
            ['group' => 'networks', 'action' => 'Edit', 'route' => 'admin.networks.edit', 'description' => 'ویرایش شبکه'],
            ['group' => 'networks', 'action' => 'Delete', 'route' => 'admin.networks.destroy', 'description' => 'حذف شبکه'],
            
            // Document Management
            ['group' => 'documents', 'action' => 'View', 'route' => 'admin.documents.index', 'description' => 'مشاهده مدارک'],
            ['group' => 'documents', 'action' => 'Approve', 'route' => 'admin.documents.approve', 'description' => 'تأیید مدارک'],
            ['group' => 'documents', 'action' => 'Reject', 'route' => 'admin.documents.reject', 'description' => 'رد مدارک'],
            
            // Reports
            ['group' => 'reports', 'action' => 'View', 'route' => 'admin.reports.index', 'description' => 'مشاهده گزارش‌ها'],
            ['group' => 'reports', 'action' => 'Export', 'route' => 'admin.reports.export', 'description' => 'خروجی گزارش‌ها'],
            
            // Blog Management
            ['group' => 'blog', 'action' => 'View', 'route' => 'admin.blog.posts.index', 'description' => 'مشاهده پست‌ها'],
            ['group' => 'blog', 'action' => 'Create', 'route' => 'admin.blog.posts.create', 'description' => 'ایجاد پست جدید'],
            ['group' => 'blog', 'action' => 'Edit', 'route' => 'admin.blog.posts.edit', 'description' => 'ویرایش پست'],
            ['group' => 'blog', 'action' => 'Delete', 'route' => 'admin.blog.posts.destroy', 'description' => 'حذف پست'],
            
            // SMS Templates
            ['group' => 'sms', 'action' => 'View', 'route' => 'admin.sms-templates.index', 'description' => 'مشاهده قالب‌های پیامک'],
            ['group' => 'sms', 'action' => 'Edit', 'route' => 'admin.sms-templates.edit', 'description' => 'ویرایش قالب پیامک'],
            
            // Referral Management
            ['group' => 'referrals', 'action' => 'View', 'route' => 'admin.referrals.index', 'description' => 'مشاهده زیرمجموعه‌ها'],
            ['group' => 'referrals', 'action' => 'Show', 'route' => 'admin.referrals.show', 'description' => 'جزئیات زیرمجموعه'],
        ];

        foreach ($permissions as $permission) {
            PermissionFromData::updateOrCreate(
                ['route' => $permission['route']],
                $permission
            );
        }
    }
}
