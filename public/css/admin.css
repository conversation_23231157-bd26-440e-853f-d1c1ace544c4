:root {
    --primary-color: #5a67d8;
    --secondary-color: #4c51bf;
    --dark-color: #1a202c;
    --light-color: #f7fafc;
    --success-color: #48bb78;
    --warning-color: #ecc94b;
    --danger-color: #e53e3e;
}

/* Main Layout */
body {
    font-family: 'Vazirmatn', sans-serif;
    background-color: #f0f2f5;
}

/* Sidebar Styles */
.sidebar {
    min-width: 280px;
    background: var(--dark-color);
    position: fixed;
    height: 100vh;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-brand {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
}

.sidebar-brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-brand-icon i {
    color: white;
    font-size: 1.5rem;
}

.sidebar-brand-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin-right: 1rem;
}

/* Navigation Items */
.nav-item {
    margin: 0.5rem 1rem;
}

.nav-link, 
.collapse-item {
    text-decoration: none !important;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.nav-link.active {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 4px 15px rgba(90, 103, 216, 0.3);
}

.nav-link i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 1.1rem;
}

/* Collapsible Menus */
.collapse-inner {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    margin: 0.5rem 0;
    padding: 0.5rem;
}

.collapse-item {
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none !important;
}

.collapse-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
    text-decoration: none;
}

.collapse-item i {
    margin-left: 0.75rem;
    font-size: 0.9rem;
}

/* Main Content Area */
.main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
}

/* Cards */
.card {
    background: white;
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

/* Stats Cards */
.stat-card {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1));
}

/* Buttons */
.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(90, 103, 216, 0.3);
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: var(--dark-color);
    color: white;
    font-weight: 500;
    border: none;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: rgba(90, 103, 216, 0.05);
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-success {
    background: var(--success-color);
    color: white;
}

.alert-warning {
    background: var(--warning-color);
    color: white;
}

.alert-danger {
    background: var(--danger-color);
    color: white;
}

/* Loading Animation */
.loading {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(90, 103, 216, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

/* Trading Statistics Cards */
.trading-stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.trading-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.trading-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.trading-stat-card:hover::before {
    opacity: 1;
}

.trading-stat-icon {
    transition: all 0.3s ease;
}

.trading-stat-card:hover .trading-stat-icon {
    transform: scale(1.1);
}

.trading-stat-detail {
    margin-top: 0.5rem;
}

.trading-stat-detail .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Gradient backgrounds for trading cards */
.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

/* Modern stat cards */
.stat-modern {
    background: white;
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-modern-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.stat-modern-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stat-modern-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

.stat-modern-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.05);
}

/* Quick action buttons */
.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    text-decoration: none;
    color: var(--dark-color);
    border-radius: 15px;
    transition: all 0.3s ease;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-action-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: var(--dark-color);
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.quick-action-btn:hover .quick-action-icon {
    transform: scale(1.1);
}

/* Dollar widget styles */
.dollar-widget {
    position: relative;
    overflow: hidden;
}

.dollar-bg-glow {
    background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        opacity: 0.3;
        transform: scale(1);
    }
    to {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

/* Bounce animation */
.bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
