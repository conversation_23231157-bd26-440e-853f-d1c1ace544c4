# راه‌اندازی سیستم نمایش موجودی کیف پول‌های سیستمی

این سند راهنمای راه‌اندازی سیستم نمایش موجودی فعلی کیف پول‌های سیستمی از شبکه‌های مختلف بلاک‌چین را ارائه می‌دهد.

## کلیدهای API مورد نیاز

برای دریافت موجودی از شبکه‌های مختلف، نیاز به کلیدهای API زیر دارید:

### 1. Etherscan API Key (برای شبکه اتریوم)
```env
ETHERSCAN_API_KEY=**********************************
```

**نحوه دریافت:**
1. به سایت [https://etherscan.io](https://etherscan.io) بروید
2. ثبت نام کنید یا وارد شوید
3. به بخش API Keys بروید
4. یک API Key جدید ایجاد کنید

### 2. BscScan API Key (برای شبکه BSC)
```env
BSCSCAN_API_KEY=**********************************
```

**نحوه دریافت:**
1. به سایت [https://bscscan.com](https://bscscan.com) بروید
2. ثبت نام کنید یا وارد شوید
3. به بخش API Keys بروید
4. یک API Key جدید ایجاد کنید

### 3. PolygonScan API Key (برای شبکه پلیگان)
```env
POLYGONSCAN_API_KEY=your_polygonscan_api_key_here
```

**نحوه دریافت:**
1. به سایت [https://polygonscan.com](https://polygonscan.com) بروید
2. ثبت نام کنید یا وارد شوید
3. به بخش API Keys بروید
4. یک API Key جدید ایجاد کنید

## شبکه‌های پشتیبانی شده

### 1. شبکه ترون (Tron)
- **منبع داده:** TronScan API
- **آدرس API:** `https://apilist.tronscanapi.com/api/account`
- **نیاز به API Key:** خیر
- **واحد نمایش:** TRX

### 2. شبکه اتریوم (Ethereum)
- **منبع داده:** Etherscan API
- **آدرس API:** `https://api.etherscan.io/api`
- **نیاز به API Key:** بله
- **واحد نمایش:** ETH

### 3. شبکه BSC (Binance Smart Chain)
- **منبع داده:** BscScan API
- **آدرس API:** `https://api.bscscan.com/api`
- **نیاز به API Key:** بله
- **واحد نمایش:** BNB

### 4. شبکه پلیگان (Polygon)
- **منبع داده:** PolygonScan API
- **آدرس API:** `https://api.polygonscan.com/api`
- **نیاز به API Key:** بله
- **واحد نمایش:** MATIC

### 5. سایر شبکه‌های EVM
- **منبع داده:** RPC مستقیم شبکه
- **نیاز به API Key:** خیر (اما نیاز به RPC URL دارد)
- **واحد نمایش:** ارز بومی شبکه

## نحوه کارکرد

### 1. دریافت موجودی ارز اصلی
سیستم بر اساس نوع شبکه، از API مناسب برای دریافت موجودی استفاده می‌کند:

```php
// مثال برای دریافت موجودی
$balance = BlockchainBalanceService::getBalance($address, $networkType, $networkId);
```

### 2. دریافت موجودی توکن‌ها
برای دریافت موجودی توکن‌های ERC20، TRC20 و سایر استانداردها:

```php
// مثال برای دریافت موجودی توکن
$tokenBalance = BlockchainBalanceService::getTokenBalance($address, $contractAddress, $networkType, $networkId);
```

## ویژگی‌های سیستم

### 1. نمایش Real-time
- موجودی‌ها به صورت Real-time از شبکه دریافت می‌شوند
- امکان بروزرسانی دستی موجودی‌ها
- نمایش وضعیت بارگذاری

### 2. مدیریت خطا
- مدیریت خطاهای شبکه
- نمایش پیام‌های خطا به کاربر
- ثبت خطاها در لاگ سیستم

### 3. کش کردن (اختیاری)
می‌توانید سیستم کش اضافه کنید تا از درخواست‌های مکرر جلوگیری شود:

```php
// مثال کش کردن موجودی برای 5 دقیقه
$balance = Cache::remember("wallet_balance_{$address}", 300, function() use ($address, $networkType, $networkId) {
    return BlockchainBalanceService::getBalance($address, $networkType, $networkId);
});
```

## تنظیمات اضافی

### 1. Timeout درخواست‌ها
تمام درخواست‌ها دارای timeout 10 ثانیه هستند. می‌توانید این مقدار را تغییر دهید:

```php
$response = Http::timeout(15)->get($url, $params);
```

### 2. Rate Limiting
برخی API ها محدودیت تعداد درخواست دارند. توصیه می‌شود:
- از کش استفاده کنید
- درخواست‌ها را محدود کنید
- API Key های متعدد استفاده کنید

## عیب‌یابی

### 1. خطای "Balance not found"
- بررسی کنید آدرس کیف پول صحیح باشد
- اطمینان حاصل کنید شبکه انتخاب شده درست باشد

### 2. خطای "API Key invalid"
- API Key را بررسی کنید
- اطمینان حاصل کنید API Key فعال باشد

### 3. خطای "Network timeout"
- اتصال اینترنت را بررسی کنید
- ممکن است API موقتاً در دسترس نباشد

## مثال استفاده

```php
// دریافت موجودی کیف پول ترون
$tronBalance = BlockchainBalanceService::getBalance('TRX...', 'tron');

// دریافت موجودی کیف پول اتریوم
$ethBalance = BlockchainBalanceService::getBalance('0x...', 'ethereum');

// دریافت موجودی توکن USDT روی ترون
$usdtBalance = BlockchainBalanceService::getTokenBalance('TRX...', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'tron');
```
