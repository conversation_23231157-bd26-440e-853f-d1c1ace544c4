@extends('admin.layouts.app')

@section('title', 'مدیریت ادمین‌ها')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item active">مدیریت ادمین‌ها</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">مدیریت ادمین‌ها</h1>
            <p class="text-muted">مدیریت کاربران ادمین و دسترسی‌های آن‌ها</p>
        </div>
        <a href="{{ route('admin.admin-management.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>افزودن ادمین جدید
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.admin-management.index') }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="جستجو بر اساس نام، ایمیل، تلفن یا کد ملی..." 
                                   value="{{ $search }}">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            @if($search)
                                <a href="{{ route('admin.admin-management.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>پاک کردن فیلتر
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Admins List -->
    <div class="card">
        <div class="card-header">
            <h6 class="m-0 font-weight-bold text-primary">
                لیست ادمین‌ها ({{ $admins->total() }} نفر)
            </h6>
        </div>
        <div class="card-body">
            @if($admins->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th width="60">#</th>
                                <th>ادمین</th>
                                <th>اطلاعات تماس</th>
                                <th>نقش</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th width="200">عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($admins as $admin)
                                <tr>
                                    <td>{{ $admin->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                {{ substr($admin->firstname, 0, 1) }}{{ substr($admin->lastname, 0, 1) }}
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $admin->firstname }} {{ $admin->lastname }}</div>
                                                @if($admin->super_admin)
                                                    <span class="badge bg-danger">سوپر ادمین</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <i class="fas fa-envelope text-muted me-1"></i>
                                            {{ $admin->email }}
                                        </div>
                                        <div>
                                            <i class="fas fa-phone text-muted me-1"></i>
                                            {{ $admin->phone }}
                                        </div>
                                    </td>
                                    <td>
                                        @foreach($admin->roles as $role)
                                            <span class="badge bg-{{ $role->color }}">{{ $role->name }}</span>
                                        @endforeach
                                    </td>
                                    <td>
                                        @if($admin->status === 'active')
                                            <span class="badge bg-success">فعال</span>
                                        @else
                                            <span class="badge bg-danger">غیرفعال</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ jdate($admin->created_at)->format('Y/m/d H:i') }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.admin-management.show', $admin->id) }}" 
                                               class="btn btn-sm btn-outline-info" title="مشاهده">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.admin-management.edit', $admin->id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="ویرایش">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.admin-management.permissions', $admin->id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="دسترسی‌ها">
                                                <i class="fas fa-key"></i>
                                            </a>
                                            @if(!$admin->super_admin)
                                                <form method="POST" action="{{ route('admin.admin-management.toggle-status', $admin->id) }}" 
                                                      style="display: inline;">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" class="btn btn-sm btn-outline-secondary" 
                                                            title="{{ $admin->status === 'active' ? 'غیرفعال کردن' : 'فعال کردن' }}">
                                                        <i class="fas fa-{{ $admin->status === 'active' ? 'ban' : 'check' }}"></i>
                                                    </button>
                                                </form>
                                                <form method="POST" action="{{ route('admin.admin-management.destroy', $admin->id) }}" 
                                                      style="display: inline;" 
                                                      onsubmit="return confirm('آیا از حذف این ادمین اطمینان دارید؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($admins->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $admins->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">هیچ ادمینی یافت نشد</h5>
                    <p class="text-muted">برای شروع، ادمین جدیدی اضافه کنید.</p>
                    <a href="{{ route('admin.admin-management.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>افزودن ادمین جدید
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.btn-group .btn {
    margin-left: 2px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}
</style>
@endsection
