@extends('admin.layouts.app')

@section('title', 'جزئیات ادمین')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.admin-management.index') }}">مدیریت ادمین‌ها</a></li>
        <li class="breadcrumb-item active">جزئیات ادمین</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">جزئیات ادمین</h1>
            <p class="text-muted">اطلاعات کامل {{ $admin->firstname }} {{ $admin->lastname }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.admin-management.permissions', $admin->id) }}" class="btn btn-info">
                <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
            </a>
            <a href="{{ route('admin.admin-management.edit', $admin->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>ویرایش
            </a>
            <a href="{{ route('admin.admin-management.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">اطلاعات شخصی</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">نام:</label>
                                <div class="fw-bold">{{ $admin->firstname }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">نام خانوادگی:</label>
                                <div class="fw-bold">{{ $admin->lastname }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">ایمیل:</label>
                                <div class="fw-bold">
                                    <i class="fas fa-envelope text-muted me-2"></i>
                                    {{ $admin->email }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">شماره تلفن:</label>
                                <div class="fw-bold">
                                    <i class="fas fa-phone text-muted me-2"></i>
                                    {{ $admin->phone }}
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($admin->national_id)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">کد ملی:</label>
                                    <div class="fw-bold">
                                        <i class="fas fa-id-card text-muted me-2"></i>
                                        {{ $admin->national_id }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Role and Permissions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">نقش و دسترسی‌ها</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">نقش:</label>
                                <div>
                                    @foreach($admin->roles as $role)
                                        <span class="badge bg-{{ $role->color }} fs-6">{{ $role->name }}</span>
                                    @endforeach
                                    @if($admin->super_admin)
                                        <span class="badge bg-danger fs-6">سوپر ادمین</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">وضعیت:</label>
                                <div>
                                    @if($admin->status === 'active')
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check me-1"></i>فعال
                                        </span>
                                    @else
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-times me-1"></i>غیرفعال
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">دسترسی‌ها:</span>
                        <a href="{{ route('admin.admin-management.permissions', $admin->id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-key me-1"></i>مدیریت دسترسی‌ها
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">اطلاعات سیستم</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">شناسه کاربری:</label>
                                <div class="fw-bold">#{{ $admin->id }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تاریخ ایجاد:</label>
                                <div class="fw-bold">
                                    <i class="fas fa-calendar text-muted me-2"></i>
                                    {{ jdate($admin->created_at)->format('Y/m/d H:i') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">آخرین بروزرسانی:</label>
                                <div class="fw-bold">
                                    <i class="fas fa-clock text-muted me-2"></i>
                                    {{ jdate($admin->updated_at)->format('Y/m/d H:i') }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">وضعیت تأیید:</label>
                                <div class="fw-bold">
                                    @if($admin->is_verified)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>تأیید شده
                                        </span>
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>در انتظار تأیید
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px; font-size: 24px;">
                        {{ substr($admin->firstname, 0, 1) }}{{ substr($admin->lastname, 0, 1) }}
                    </div>
                    <h5 class="card-title">{{ $admin->firstname }} {{ $admin->lastname }}</h5>
                    <p class="text-muted">
                        @foreach($admin->roles as $role)
                            {{ $role->name }}
                        @endforeach
                    </p>
                    
                    @if($admin->super_admin)
                        <span class="badge bg-danger mb-3">سوپر ادمین</span>
                    @endif

                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.admin-management.edit', $admin->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>ویرایش اطلاعات
                        </a>
                        <a href="{{ route('admin.admin-management.permissions', $admin->id) }}" class="btn btn-info">
                            <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
                        </a>
                        @if(!$admin->super_admin)
                            <form method="POST" action="{{ route('admin.admin-management.toggle-status', $admin->id) }}" 
                                  onsubmit="return confirm('آیا از تغییر وضعیت این ادمین اطمینان دارید؟')">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-outline-secondary">
                                    <i class="fas fa-{{ $admin->status === 'active' ? 'ban' : 'check' }} me-2"></i>
                                    {{ $admin->status === 'active' ? 'غیرفعال کردن' : 'فعال کردن' }}
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">آمار سریع</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">مدت عضویت:</span>
                        <span class="fw-bold">{{ $admin->created_at->diffForHumans() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">آخرین فعالیت:</span>
                        <span class="fw-bold">{{ $admin->updated_at->diffForHumans() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">وضعیت حساب:</span>
                        @if($admin->status === 'active')
                            <span class="badge bg-success">فعال</span>
                        @else
                            <span class="badge bg-danger">غیرفعال</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.form-label {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.card-header h6 {
    color: #5a5c69;
}

.badge.fs-6 {
    font-size: 0.875rem !important;
}
</style>
@endsection
