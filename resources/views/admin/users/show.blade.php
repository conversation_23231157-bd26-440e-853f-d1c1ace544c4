@extends('admin.layouts.app')

@push('styles')
<style>
body {
    background-color: #f8f9fa;
}
.user-profile-page .user-card {
    border-radius: 1rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.05);
    padding: 2rem;
    position: sticky;
    top: 20px;
    background-color: #fff;
}
.user-card .profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
    margin: 0 auto 1.5rem;
    display: block;
}
.user-card .user-name {
    font-size: 1.5rem;
    font-weight: 600;
}
.user-card .user-email {
    color: #6c757d;
}
.user-card .info-list {
    list-style: none;
    padding: 0;
    margin-top: 1.5rem;
}
.user-card .info-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}
.user-card .info-list li:last-child {
    border-bottom: none;
}
.user-card .info-list li strong {
    color: #495057;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    text-align: center;
}
.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
}
.stat-card .icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: inline-block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    color: #fff;
}
.stat-card .icon.bg-primary { background-color: var(--primary-color) !important; }
.stat-card .icon.bg-success { background-color: var(--success-color) !important; }
.stat-card .icon.bg-info { background-color: var(--info-color) !important; }

.stat-card h3 {
    font-weight: 700;
}
.stat-card p {
    color: #6c757d;
}

.data-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.05);
}
.data-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}
.data-card .card-header-tabs {
    margin: -1rem -1.5rem;
}
.data-card .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
}
.data-card .nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: transparent;
}
.data-card .card-body {
    padding: 1.5rem;
}
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}
.table th {
    font-weight: 600;
    color: #6c757d;
}
.wallet-address {
    font-family: monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}
.badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
}
.badge.bg-success-soft { background: rgba(16, 185, 129, 0.1); color: #10b981; }
.badge.bg-warning-soft { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.badge.bg-danger-soft { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
.badge.bg-info-soft { background-color: rgba(59, 130, 246, 0.1); color: #3B82F6; }

.timeline {
    position: relative;
    padding-left: 30px;
    border-left: 2px solid #e9ecef;
}
.timeline-item {
    margin-bottom: 2rem;
    position: relative;
}
.timeline-item:last-child {
    margin-bottom: 0;
}
.timeline-item .timeline-icon {
    position: absolute;
    left: -16px;
    top: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    background-color: var(--primary-color);
}
.timeline-item .timeline-content {
    padding-left: 1rem;
}
.timeline-item .timeline-content h6 {
    font-weight: 600;
}
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
    margin-top: 0;
}
</style>
@endpush

@section('content')
<div class="container-fluid user-profile-page">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm p-4 d-flex flex-md-row align-items-center justify-content-between flex-wrap gap-3">
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <img src="{{ $user->avatar_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->firstname . ' ' . $user->lastname) }}" class="rounded-circle border border-2" width="90" height="90" alt="{{ $user->firstname }}">
                    <div>
                        <h3 class="mb-1">{{ $user->firstname }} {{ $user->lastname }}
                            @if($user->status == 'approved')
                                <span class="badge bg-success-soft ms-2">فعال</span>
                            @elseif($user->status == 'pending')
                                <span class="badge bg-warning-soft ms-2">در انتظار تایید</span>
                            @else
                                <span class="badge bg-danger-soft ms-2">غیرفعال</span>
                            @endif
                        </h3>
                        <div class="d-flex flex-wrap gap-3 align-items-center small text-muted">
                            <span><i class="fas fa-envelope me-1"></i> {{ $user->email }}</span>
                            <span><i class="fas fa-phone me-1"></i> {{ $user->phone ?? '-' }}</span>
                            <span><i class="fas fa-id-card me-1"></i> {{ $user->national_id ?? '-' }}</span>
                            <span><i class="fas fa-layer-group me-1"></i> سطح: {{ $user->level }}</span>
                        </div>
                        <div class="d-flex flex-wrap gap-3 align-items-center small mt-2">
                            <span><i class="fas fa-clock me-1"></i> آخرین ورود: {{ optional($user->logins->sortByDesc('created_at')->first())->created_at ? jdate($user->logins->sortByDesc('created_at')->first()->created_at)->format('Y/m/d H:i') : '-' }}</span>
                            <span><i class="fas fa-globe me-1"></i> آی‌پی: {{ optional($user->logins->sortByDesc('created_at')->first())->ip_address ?? '-' }}</span>
                            <span><i class="fas fa-signal me-1"></i> وضعیت آنلاین: @if(isset($user->sessions) && $user->sessions->where('last_activity', '>=', now()->subMinutes(15)->timestamp)->count() > 0) <span class="text-success">آنلاین</span> @else <span class="text-danger">آفلاین</span> @endif</span>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-wrap gap-2">
                    <a href="#" class="btn btn-outline-warning"><i class="fas fa-key me-1"></i> ریست رمز عبور</a>
                    @if($user->status == 'approved')
                        <a href="#" class="btn btn-outline-danger"><i class="fas fa-user-slash me-1"></i> مسدودسازی</a>
                    @else
                        <a href="#" class="btn btn-outline-success"><i class="fas fa-user-check me-1"></i> فعال‌سازی</a>
                    @endif
                    <a href="#" class="btn btn-outline-info"><i class="fas fa-paper-plane me-1"></i> ارسال پیام</a>
                    <a href="#" class="btn btn-outline-secondary"><i class="fas fa-sign-in-alt me-1"></i> ورود به حساب کاربر</a>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-end mb-3">
        <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit"></i> ویرایش کاربر
        </a>
        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت به لیست
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-primary"><i class="fas fa-arrow-down"></i></div>
                        <h3 class="h5 mb-1 text-dark">{{ number_format($user->tomanDeposits->sum('amount')) }} تومان</h3>
                        <p class="text-muted mb-0">کل واریز تومانی</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-danger"><i class="fas fa-arrow-up"></i></div>
                        <h3 class="h5 mb-1 text-dark">{{ number_format($user->tomanWithdrawals->sum('amount')) }} تومان</h3>
                        <p class="text-muted mb-0">کل برداشت تومانی</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-info"><i class="fas fa-coins"></i></div>
                        <h3 class="h5 mb-1 text-dark">{{ number_format($user->depositTransactions->sum('amount'), 8) }}</h3>
                        <p class="text-muted mb-0">کل واریز رمز ارزی</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-warning"><i class="fas fa-coins"></i></div>
                        <h3 class="h5 mb-1 text-dark">{{ number_format($user->withdrawHistories->sum('amount'), 8) }}</h3>
                        <p class="text-muted mb-0">کل برداشت رمز ارزی</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-success"><i class="fas fa-ticket-alt"></i></div>
                        <h3 class="h5 mb-1 text-dark">{{ $user->tickets->count() }}</h3>
                        <p class="text-muted mb-0">تعداد تیکت‌ها</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon bg-dark"><i class="fas fa-wallet"></i></div>
                        <h3 class="h5 mb-1 text-dark">${{ number_format($totalBalanceUSD, 2) }}</h3>
                        <p class="text-muted mb-0">کل دارایی (دلار)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card p-3">
                <h6 class="mb-3">نمودار ماهانه واریز/برداشت تومانی</h6>
                <canvas id="tomanMonthlyChart" height="180"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card p-3">
                <h6 class="mb-3">نمودار ماهانه واریز/برداشت رمز ارزی</h6>
                <canvas id="cryptoMonthlyChart" height="180"></canvas>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column: User Profile Card -->
        <div class="col-lg-4 col-xl-3">
            <div class="card user-card">
                <div class="text-center">
                    <img src="{{ $user->avatar_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->firstname . ' ' . $user->lastname) }}"
                         class="profile-avatar" alt="{{ $user->firstname }}">
                    <h4 class="user-name mb-0">{{ $user->firstname }} {{ $user->lastname }}</h4>
                    <p class="user-email">{{ $user->email }}</p>
                    <span class="badge {{ $user->status == 'active' ? 'bg-success-soft' : 'bg-danger-soft' }} mb-3">
                        {{ $user->status == 'active' ? 'فعال' : 'غیرفعال' }}
                    </span>
                </div>

                <ul class="info-list">
                    <li><strong>موبایل:</strong> <span>{{ $user->phone ?? '-' }}</span></li>
                    <li><strong>کد ملی:</strong> <span>{{ $user->national_id ?? '-' }}</span></li>
                    <li><strong>جنسیت:</strong>
                        <span>
                            @if($user->gender == 'male') مرد
                            @elseif($user->gender == 'female') زن
                            @else نامشخص @endif
                        </span>
                    </li>
                    <li><strong>تاریخ عضویت:</strong> <span>{{ jdate($user->created_at)->format('Y/m/d') }}</span></li>
                    <li>
                        <strong>نقش‌ها:</strong>
                        <div>
                        @foreach($user->roles as $role)
                            <span class="badge bg-info-soft me-1">{{ $role->name }}</span>
                        @endforeach
                        </div>
                    </li>
                </ul>

                <hr>
                <h6 class="mb-3">وضعیت احراز هویت دو مرحله‌ای</h6>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold {{ $user->two_factor_enabled ? 'text-success' : 'text-danger' }}">
                         {{ $user->two_factor_enabled ? 'فعال' : 'غیرفعال' }}
                    </span>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="toggle2FA"
                               {{ $user->two_factor_enabled ? 'checked' : '' }}
                               data-user-id="{{ $user->id }}"
                               onchange="toggle2FAStatus(this)">
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Tabbed Content -->
        <div class="col-lg-8 col-xl-9">
            <!-- Stats Row -->
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="icon bg-primary"><i class="fas fa-wallet"></i></div>
                        <h3 class="h4 mb-1 text-dark">${{ number_format($totalBalanceUSD, 2) }}</h3>
                        <p class="text-muted mb-0">کل دارایی</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="icon bg-success"><i class="fas fa-exchange-alt"></i></div>
                        <h3 class="h4 mb-1 text-dark">{{ $user->transactions->count() }}</h3>
                        <p class="text-muted mb-0">تعداد تراکنش‌ها</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="icon bg-info"><i class="fas fa-ticket-alt"></i></div>
                        <h3 class="h4 mb-1 text-dark">{{ $user->tickets->count() }}</h3>
                        <p class="text-muted mb-0">تعداد تیکت‌ها</p>
                    </div>
                </div>
            </div>

            <div class="card data-card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="user-tabs" role="tablist">
                        <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#overview" role="tab">نمای کلی</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#wallets-assets" role="tab">کیف پول‌ها و دارایی‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#authentication" role="tab">احراز هویت</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#tickets" role="tab">تیکت‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#toman-withdrawals" role="tab">درخواست‌های برداشت تومانی</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#toman-deposits" role="tab">واریزهای تومانی</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#crypto-transfers" role="tab">واریز و برداشت رمز ارزی</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#user-messages" role="tab">پیامک‌ها و ایمیل‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#user-alerts" role="tab">هشدارها و اعلان‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#user-sessions" role="tab">نشست‌ها و دستگاه‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#all-transactions" role="tab">همه تراکنش‌ها</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#logs" role="tab">گزارشات</a></li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="user-tabs-content">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="section-title">نمودار تراکنش‌ها</h5>
                            <canvas id="transactionChart" height="120" class="mb-4"></canvas>
                            
                            <h5 class="section-title">آخرین تراکنش‌ها</h5>
                            @if($user->transactions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>تاریخ</th><th>نوع</th><th>مبلغ</th><th>ارز</th><th>وضعیت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->transactions->take(5) as $transaction)
                                                <tr>
                                                    <td>{{ jdate($transaction->created_at)->format('Y/m/d') }}</td>
                                                    <td>{{ $transaction->type }}</td>
                                                    <td>{{ number_format($transaction->amount) }}</td>
                                                    <td>{{ $transaction->currency?->name ?: 'نامشخص' }}</td>
                                                    <td>
                                                        @switch($transaction->status)
                                                            @case('done') <span class="badge bg-success-soft">انجام شده</span> @break
                                                            @case('pending') <span class="badge bg-warning-soft">در حال انجام</span> @break
                                                            @case('failed') <span class="badge bg-danger-soft">ناموفق</span> @break
                                                        @endswitch
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ تراکنشی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- Wallets & Assets Tab -->
                        <div class="tab-pane fade" id="wallets-assets" role="tabpanel">
                            <h5 class="section-title">کیف پول‌ها و دارایی‌ها</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card p-3 text-center">
                                        <div class="h6 mb-1">مجموع دارایی (دلار)</div>
                                        <div class="display-6 fw-bold">${{ number_format($totalBalanceUSD, 2) }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card p-3 text-center">
                                        <div class="h6 mb-1">مجموع دارایی (تومان)</div>
                                        <div class="display-6 fw-bold">{{ number_format($user->toman_balance) }} تومان</div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped mb-0">
                                    <thead>
                                        <tr>
                                            <th>ارز</th>
                                            <th>موجودی</th>
                                            <th>معادل دلار</th>
                                            <th>آدرس</th>
                                            <th>وضعیت</th>
                                            <th>عملیات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($coins as $coin)
                                            @php $wallet = $wallets->where('coin_id', $coin->id)->first(); @endphp
                                            <tr>
                                                <td>
                                                    @if($coin->coin_icon)
                                                        <img src="{{ asset('storage/'.$coin->coin_icon) }}" width="25" height="25" alt="{{ $coin->coin_type }}" class="me-2">
                                                    @endif
                                                    {{ $coin->coin_type }}
                                                </td>
                                                <td>{{ $wallet ? number_format($wallet->balance, 8) : '0.00' }}</td>
                                                <td>${{ $wallet ? number_format(get_coin_usd_value($wallet->balance, $coin->coin_type), 2) : '0.00' }}</td>
                                                <td><span class="wallet-address">{{ $wallet->address ?? '-' }}</span></td>
                                                <td>
                                                    @if($wallet)
                                                        <span class="badge bg-success-soft">فعال</span>
                                                    @else
                                                        <span class="badge bg-secondary">غیرفعال</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($wallet)
                                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#adjustBalanceModal{{ $wallet->id }}">
                                                            تنظیم موجودی
                                                        </button>
                                                    @else
                                                        <form action="{{ route('admin.users.CreateWalletbyCoin', $user->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            <input type="hidden" name="coin_type" value="{{ $coin->id }}">
                                                            <button type="submit" class="btn btn-sm btn-success">ایجاد کیف پول</button>
                                                        </form>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <hr>
                            <h5 class="section-title mt-4">تاریخچه آدرس‌های کیف پول</h5>
                            @if($user->walletAddressHistories->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>آدرس</th>
                                                <th>نوع ارز</th>
                                                <th>شبکه</th>
                                                <th>مو</th>
                                                <th>تاریخ ایجاد</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->walletAddressHistories as $wallet)
                                                <tr>
                                                    <td><span class="wallet-address">{{ $wallet->address }}</span></td>
                                                    <td>{{ $wallet->coin?->name ?: $wallet->coin_type }}</td>
                                                    <td>{{ $wallet->network?->name ?: '-' }}</td>
                                                    <td>{{ $wallet->memo ?: '-' }}</td>
                                                    <td>{{ jdate($wallet->created_at)->format('Y/m/d H:i') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ آدرس کیف پولی ثبت نشده است.</div>
                            @endif
                        </div>
                        
                        <!-- Authentication Tab -->
                        <div class="tab-pane fade" id="authentication" role="tabpanel">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card p-3 text-center">
                                        <div class="h6 mb-1">وضعیت کلی احراز هویت</div>
                                        @php
                                            $kycStatus = $user->documents->count() === 0 ? 'incomplete' : ($user->documents->every(fn($d) => $d->status === 'approved') ? 'approved' : ($user->documents->contains(fn($d) => $d->status === 'rejected') ? 'rejected' : 'pending'));
                                        @endphp
                                        @if($kycStatus === 'approved')
                                            <span class="badge bg-success-soft py-2 px-4 fs-5"><i class="fas fa-check-circle me-1"></i> تایید شده</span>
                                        @elseif($kycStatus === 'pending')
                                            <span class="badge bg-warning-soft py-2 px-4 fs-5"><i class="fas fa-hourglass-half me-1"></i> در انتظار بررسی</span>
                                        @elseif($kycStatus === 'rejected')
                                            <span class="badge bg-danger-soft py-2 px-4 fs-5"><i class="fas fa-times-circle me-1"></i> رد شده</span>
                                        @else
                                            <span class="badge bg-secondary py-2 px-4 fs-5"><i class="fas fa-exclamation-circle me-1"></i> ناقص</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card p-3 text-center">
                                        <div class="h6 mb-1">تعداد مدارک ارسال شده</div>
                                        <div class="display-6 fw-bold">{{ $user->documents->count() }}</div>
                                    </div>
                                </div>
                            </div>
                            @if($user->documents->where('status', 'rejected')->count() > 0)
                                <div class="alert alert-danger"><i class="fas fa-info-circle me-1"></i> برخی مدارک رد شده‌اند. لطفاً بررسی و پیگیری شود.</div>
                            @elseif($user->documents->count() === 0)
                                <div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-1"></i> هیچ مدرکی ارسال نشده است.</div>
                            @endif
                            <h5 class="section-title">مدارک احراز هویت</h5>
                            @if($user->documents->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>نوع مدرک</th>
                                                <th>تصویر</th>
                                                <th>تاریخ ارسال</th>
                                                <th>وضعیت</th>
                                                <th>عملیات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->documents as $document)
                                                <tr>
                                                    <td>
                                                        @php
                                                            $documentTypeText = '';
                                                            $documentTypeIcon = '';
                                                            switch($document->name) {
                                                                case 'national_card':
                                                                    $documentTypeText = 'کارت ملی';
                                                                    $documentTypeIcon = 'fa-id-card';
                                                                    break;
                                                                case 'birth_certificate':
                                                                    $documentTypeText = 'شناسنامه';
                                                                    $documentTypeIcon = 'fa-file-alt';
                                                                    break;
                                                                case 'selfie':
                                                                    $documentTypeText = 'عکس سلفی';
                                                                    $documentTypeIcon = 'fa-camera';
                                                                    break;
                                                                default:
                                                                    $documentTypeText = $document->name;
                                                                    $documentTypeIcon = 'fa-file';
                                                            }
                                                        @endphp
                                                        <div class="d-flex align-items-center">
                                                            <i class="fas {{ $documentTypeIcon }} text-primary me-2"></i>
                                                            <span>{{ $documentTypeText }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="#" class="view-document" data-bs-toggle="modal" data-bs-target="#documentModal" data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}">
                                                            <img src="{{ config('app.url') }}/storage/{{ $document->file->url }}" class="img-thumbnail" width="80" alt="مشاهده مدرک">
                                                        </a>
                                                    </td>
                                                    <td>{{ jdate($document->created_at)->format('Y/m/d') }}</td>
                                                    <td>
                                                        @php
                                                            $statusClass = '';
                                                            $statusText = '';
                                                            switch($document->status) {
                                                                case 'pending':
                                                                    $statusClass = 'bg-warning-soft';
                                                                    $statusText = 'در انتظار بررسی';
                                                                    break;
                                                                case 'approved':
                                                                    $statusClass = 'bg-success-soft';
                                                                    $statusText = 'تایید شده';
                                                                    break;
                                                                case 'rejected':
                                                                    $statusClass = 'bg-danger-soft';
                                                                    $statusText = 'رد شده';
                                                                    break;
                                                            }
                                                        @endphp
                                                        <div class="d-flex flex-column">
                                                            <span class="badge {{ $statusClass }} mb-1">{{ $statusText }}</span>
                                                            @if($document->status === 'rejected' && $document->description)
                                                                <small class="text-danger" title="{{ $document->description }}">
                                                                    <i class="fas fa-info-circle me-1"></i>
                                                                    دلیل رد
                                                                </small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-outline-primary view-document" data-bs-toggle="modal" data-bs-target="#documentModal" data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        @if($document->status === 'pending')
                                                            <a href="{{ route('admin.documents.approve', $document->id) }}" class="btn btn-sm btn-success ms-1">تایید</a>
                                                            <a href="{{ route('admin.documents.reject', $document->id) }}" class="btn btn-sm btn-danger ms-1">رد</a>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                            <hr>
                            <h5 class="section-title mt-4">کارت‌های بانکی</h5>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card p-3 text-center">
                                        <div class="h6 mb-1">تعداد کل کارت‌های بانکی</div>
                                        <div class="display-6 fw-bold">{{ $user->cards->count() }}</div>
                                    </div>
                                </div>
                            </div>
                            @if($user->cards->where('status', 'pending')->count() > 0)
                                <div class="alert alert-warning"><i class="fas fa-hourglass-half me-1"></i> برخی کارت‌ها در انتظار تایید هستند.</div>
                            @elseif($user->cards->where('status', 'rejected')->count() > 0)
                                <div class="alert alert-danger"><i class="fas fa-times-circle me-1"></i> برخی کارت‌ها رد شده‌اند.</div>
                            @endif
                            @if($user->cards->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>بانک</th>
                                                <th>شماره کارت</th>
                                                <th>شبا</th>
                                                <th>وضعیت</th>
                                                <th>عملیات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->cards as $card)
                                                <tr>
                                                    <td>
                                                        @if($card->bank?->logo)
                                                            <img src="{{ asset('images/banks/' . $card->bank->logo) }}" width="30" class="me-2" alt="{{ $card->bank->name }}">
                                                        @endif
                                                        {{ $card->bank?->name ?: 'نامشخص' }}
                                                    </td>
                                                    <td class="wallet-address">{{ $card->number }}</td>
                                                    <td>{{ $card->sheba ?? '-' }}</td>
                                                    <td>
                                                        @switch($card->status)
                                                            @case('active') <span class="badge bg-success-soft">فعال</span> @break
                                                            @case('pending') <span class="badge bg-warning-soft">در انتظار تایید</span> @break
                                                            @case('rejected') <span class="badge bg-danger-soft">رد شده</span> @break
                                                            @default <span class="badge bg-secondary">نامشخص</span> @break
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        @if($card->status === 'pending')
                                                            <a href="" class="btn btn-sm btn-success ms-1">تایید</a>
                                                            <a href="" class="btn btn-sm btn-danger ms-1">رد</a>
                                                        @elseif($card->status === 'active')
                                                            <a href="" class="btn btn-sm btn-warning ms-1">مسدودسازی</a>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ کارت بانکی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- Tickets Tab -->
                        <div class="tab-pane fade" id="tickets" role="tabpanel">
                             @if($user->tickets->count() > 0)
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>موضوع</th><th>دپارتمان</th><th>وضعیت</th><th>آخرین بروزرسانی</th><th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->tickets as $ticket)
                                                <tr>
                                                    <td>{{ $ticket->subject }}</td>
                                                    <td>{{ $ticket->unit?->name ?: 'نامشخص' }}</td>
                                                    <td>
                                                        @switch($ticket->status)
                                                            @case('open') <span class="badge bg-success-soft">باز</span> @break
                                                            @case('closed') <span class="badge bg-secondary">بسته</span> @break
                                                            @case('pending') <span class="badge bg-warning-soft">در انتظار پاسخ</span> @break
                                                        @endswitch
                                                    </td>
                                                    <td>{{ jdate($ticket->updated_at)->format('Y/m/d H:i') }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.tickets.show', $ticket->id) }}" class="btn btn-sm btn-info">مشاهده</a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ تیکتی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- Toman Withdrawals Tab -->
                        <div class="tab-pane fade" id="toman-withdrawals" role="tabpanel">
                            <h5 class="section-title">درخواست‌های برداشت تومانی</h5>
                            @if($user->tomanWithdrawals->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>شناسه</th>
                                                <th>تاریخ</th>
                                                <th>مبلغ (تومان)</th>
                                                <th>کارت بانکی</th>
                                                <th>وضعیت</th>
                                                <th>شماره پیگیری</th>
                                                <th>ادمین</th>
                                                <th>توضیحات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->tomanWithdrawals as $withdrawal)
                                                <tr>
                                                    <td>{{ $withdrawal->id }}</td>
                                                    <td>{{ jdate($withdrawal->created_at)->format('Y/m/d H:i') }}</td>
                                                    <td>{{ number_format($withdrawal->amount) }}</td>
                                                    <td>
                                                        {{ $withdrawal->card?->bank?->name ?? '-' }} - 
                                                        {{ $withdrawal->card?->number ? substr($withdrawal->card->number, 0, 4) . '...' . substr($withdrawal->card->number, -4) : '-' }}
                                                    </td>
                                                    <td>
                                                        @if($withdrawal->status == 'pending')
                                                            <span class="badge bg-warning-soft">در انتظار تایید</span>
                                                        @elseif($withdrawal->status == 'approved')
                                                            <span class="badge bg-success-soft">تایید شده</span>
                                                        @elseif($withdrawal->status == 'rejected')
                                                            <span class="badge bg-danger-soft">رد شده</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $withdrawal->tracking_number ?? '-' }}</td>
                                                    <td>{{ $withdrawal->admin?->email ?? '-' }}</td>
                                                    <td>
                                                        @if($withdrawal->status == 'rejected' && $withdrawal->reject_reason)
                                                            <span class="text-danger" title="{{ $withdrawal->reject_reason }}">
                                                                <i class="fas fa-info-circle"></i> دلیل رد
                                                            </span>
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ درخواست برداشت تومانی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- Toman Deposits Tab -->
                        <div class="tab-pane fade" id="toman-deposits" role="tabpanel">
                            <h5 class="section-title">واریزهای تومانی</h5>
                            @if($user->tomanDeposits->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>شناسه</th>
                                                <th>تاریخ</th>
                                                <th>مبلغ (تومان)</th>
                                                <th>کارت بانکی</th>
                                                <th>وضعیت</th>
                                                <th>توضیحات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->tomanDeposits as $deposit)
                                                <tr>
                                                    <td>{{ $deposit->id }}</td>
                                                    <td>{{ jdate($deposit->created_at)->format('Y/m/d H:i') }}</td>
                                                    <td>{{ number_format($deposit->amount) }}</td>
                                                    <td>{{ $deposit->card?->bank?->name ?? '-' }}</td>
                                                    <td>
                                                        @if($deposit->status == 'pending')
                                                            <span class="badge bg-warning-soft">در انتظار تایید</span>
                                                        @elseif($deposit->status == 'approved' || $deposit->status == 'done')
                                                            <span class="badge bg-success-soft">تایید شده</span>
                                                        @elseif($deposit->status == 'declined')
                                                            <span class="badge bg-danger-soft">رد شده</span>
                                                        @else
                                                            <span class="badge bg-secondary">{{ $deposit->status }}</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $deposit->description ?? '-' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ واریز تومانی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- Crypto Transfers Tab -->
                        <div class="tab-pane fade" id="crypto-transfers" role="tabpanel">
                            <h5 class="section-title">واریز و برداشت رمز ارزی</h5>
                            <h6>واریزها</h6>
                            @if($user->depositTransactions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>شناسه</th>
                                                <th>تاریخ</th>
                                                <th>مقدار</th>
                                                <th>ارز</th>
                                                <th>آدرس</th>
                                                <th>وضعیت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->depositTransactions as $deposit)
                                                <tr>
                                                    <td>{{ $deposit->id }}</td>
                                                    <td>{{ jdate($deposit->created_at)->format('Y/m/d H:i') }}</td>
                                                    <td>{{ number_format($deposit->amount, 8) }}</td>
                                                    <td>{{ $deposit->coin?->coin_type ?? '-' }}</td>
                                                    <td>{{ $deposit->receiverWallet?->address ?? '-' }}</td>
                                                    <td>
                                                        @if($deposit->status == 1)
                                                            <span class="badge bg-success-soft">تایید شده</span>
                                                        @elseif($deposit->status == 0)
                                                            <span class="badge bg-warning-soft">در انتظار تایید</span>
                                                        @else
                                                            <span class="badge bg-secondary">{{ $deposit->status }}</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ واریز رمز ارزی ثبت نشده است.</div>
                            @endif
                            <hr>
                            <h6>برداشت‌ها</h6>
                            @if($user->withdrawHistories->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>شناسه</th>
                                                <th>تاریخ</th>
                                                <th>مقدار</th>
                                                <th>ارز</th>
                                                <th>آدرس</th>
                                                <th>وضعیت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->withdrawHistories as $withdrawal)
                                                <tr>
                                                    <td>{{ $withdrawal->id }}</td>
                                                    <td>{{ jdate($withdrawal->created_at)->format('Y/m/d H:i') }}</td>
                                                    <td>{{ number_format($withdrawal->amount, 8) }}</td>
                                                    <td>{{ $withdrawal->coin?->coin_type ?? '-' }}</td>
                                                    <td>{{ $withdrawal->address ?? '-' }}</td>
                                                    <td>
                                                        @if($withdrawal->status == 1)
                                                            <span class="badge bg-success-soft">تایید شده</span>
                                                        @elseif($withdrawal->status == 0)
                                                            <span class="badge bg-warning-soft">در انتظار تایید</span>
                                                        @else
                                                            <span class="badge bg-secondary">{{ $withdrawal->status }}</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ برداشت رمز ارزی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- User Messages Tab -->
                        <div class="tab-pane fade" id="user-messages" role="tabpanel">
                            <h5 class="section-title">پیامک‌ها و ایمیل‌ها</h5>
                            <div class="alert alert-info">لیست پیامک‌ها و ایمیل‌های ارسالی به زودی اضافه می‌شود.</div>
                        </div>

                        <!-- User Alerts Tab -->
                        <div class="tab-pane fade" id="user-alerts" role="tabpanel">
                            <h5 class="section-title">هشدارها و اعلان‌ها</h5>
                            @if($user->alerts->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>عنوان</th>
                                                <th>متن</th>
                                                <th>تاریخ</th>
                                                <th>وضعیت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->alerts as $alert)
                                                <tr>
                                                    <td>{{ $alert->title ?? '-' }}</td>
                                                    <td>{{ $alert->message ?? '-' }}</td>
                                                    <td>{{ jdate($alert->created_at)->format('Y/m/d H:i') }}</td>
                                                    <td>
                                                        @if($alert->read)
                                                            <span class="badge bg-success-soft">خوانده شده</span>
                                                        @else
                                                            <span class="badge bg-warning-soft">خوانده نشده</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ هشدار یا اعلانی ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- User Sessions Tab -->
                        <div class="tab-pane fade" id="user-sessions" role="tabpanel">
                            <h5 class="section-title">نشست‌ها و دستگاه‌های فعال</h5>
                            @if($user->sessions->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>IP</th>
                                                <th>آخرین فعالیت</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->sessions as $session)
                                                <tr>
                                                    <td>{{ $session->ip_address ?? '-' }}</td>
                                                    <td>{{ $session->last_activity ? jdate($session->last_activity)->format('Y/m/d H:i') : '-' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ نشست فعالی ثبت نشده است.</div>
                            @endif
                            <hr>
                            <h6>تاریخچه ورود</h6>
                            @if($user->logins->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>IP</th>
                                                <th>تاریخ</th>
                                                <th>دستگاه</th>
                                                <th>سیستم عامل</th>
                                                <th>مرورگر</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->logins as $login)
                                                <tr>
                                                    <td>{{ $login->ip_address ?? '-' }}</td>
                                                    <td>{{ $login->created_at ? jdate($login->created_at)->format('Y/m/d H:i') : '-' }}</td>
                                                    <td>{{ $login->userAgent?->device ?? '-' }}</td>
                                                    <td>{{ $login->userAgent?->platform ?? '-' }}</td>
                                                    <td>{{ $login->userAgent?->browser ?? '-' }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">هیچ تاریخچه ورود ثبت نشده است.</div>
                            @endif
                        </div>

                        <!-- All Transactions Tab -->
                        <div class="tab-pane fade" id="all-transactions" role="tabpanel">
                            <h5 class="section-title">همه تراکنش‌ها</h5>
                            <form class="row g-2 mb-3" id="transaction-filter-form" onsubmit="return false;">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="transaction-search" placeholder="جستجو در توضیحات یا مبلغ...">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="transaction-type">
                                        <option value="">همه انواع</option>
                                        <option value="deposit">واریز</option>
                                        <option value="withdraw">برداشت</option>
                                        <option value="buy">خرید</option>
                                        <option value="sell">فروش</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="transaction-status">
                                        <option value="">همه وضعیت‌ها</option>
                                        <option value="pending">در انتظار</option>
                                        <option value="waiting">در صف</option>
                                        <option value="approved">تایید شده</option>
                                        <option value="declined">رد شده</option>
                                        <option value="done">انجام شده</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" id="transaction-date-from" placeholder="از تاریخ">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" id="transaction-date-to" placeholder="تا تاریخ">
                                </div>
                                <div class="col-md-1 d-grid">
                                    <button type="button" class="btn btn-primary" id="transaction-filter-btn"><i class="fas fa-search"></i></button>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table class="table table-striped mb-0" id="transactions-table">
                                    <thead>
                                        <tr>
                                            <th>شناسه</th>
                                            <th>تاریخ</th>
                                            <th>نوع</th>
                                            <th>مبلغ</th>
                                            <th>ارز</th>
                                            <th>وضعیت</th>
                                            <th>توضیحات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($user->transactions->sortByDesc('created_at') as $transaction)
                                            <tr>
                                                <td>{{ $transaction->id }}</td>
                                                <td>{{ jdate($transaction->created_at)->format('Y/m/d H:i') }}</td>
                                                <td>{{ $transaction->type }}</td>
                                                <td>{{ number_format($transaction->amount, 8) }}</td>
                                                <td>{{ $transaction->currency?->coin_type ?? '-' }}</td>
                                                <td>
                                                    @switch($transaction->status)
                                                        @case('done') <span class="badge bg-success-soft">انجام شده</span> @break
                                                        @case('approved') <span class="badge bg-success-soft">تایید شده</span> @break
                                                        @case('pending') <span class="badge bg-warning-soft">در انتظار</span> @break
                                                        @case('waiting') <span class="badge bg-info-soft">در صف</span> @break
                                                        @case('declined') <span class="badge bg-danger-soft">رد شده</span> @break
                                                        @default <span class="badge bg-secondary">{{ $transaction->status }}</span>
                                                    @endswitch
                                                </td>
                                                <td>{{ $transaction->description ?? '-' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Logs Tab -->
                        <div class="tab-pane fade" id="logs" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="section-title">تاریخچه ورود</h5>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>تاریخ</th><th>IP</th><th>وضعیت</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($user->logins()->latest()->take(10)->get() as $login)
                                                    <tr>
                                                        <td>{{ jdate($login->created_at)->format('Y/m/d H:i') }}</td>
                                                        <td>{{ $login->ip_address }}</td>
                                                        <td>
                                                            <span class="badge {{ $login->status ? 'bg-success-soft' : 'bg-danger-soft' }}">
                                                                {{ $login->status ? 'موفق' : 'ناموفق' }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr><td colspan="3" class="text-center">بدون سابقه</td></tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="section-title">فعالیت‌های اخیر</h5>
                                    @if($user->activities()->count() > 0)
                                    <div class="timeline">
                                        @foreach($user->activities()->latest()->take(5)->get() as $activity)
                                            <div class="timeline-item">
                                                <div class="timeline-icon bg-{{ $activity->type == 'login' ? 'primary' : 'success' }}">
                                                    <i class="fas fa-{{ $activity->type == 'login' ? 'sign-in-alt' : 'info-circle' }}"></i>
                                                </div>
                                                <div class="timeline-content">
                                                    <h6>{{ $activity->description }}</h6>
                                                    <small class="text-muted">{{ jdate($activity->created_at)->ago() }}</small>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    @else
                                        <div class="alert alert-info">هیچ فعالیتی ثبت نشده است.</div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Document View Modal -->
<div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentModalLabel">مشاهده مدرک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="document-image" src="" class="img-fluid" alt="مدارک">
            </div>
        </div>
    </div>
</div>

<!-- Create Wallet Modal -->
<div class="modal fade" id="createWalletModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ایجاد کیف پول جدید</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.users.create-wallet', $user->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نوع ارز</label>
                        <select name="coin_type" class="form-select" required>
                            <option value="">انتخاب کنید</option>
                            @foreach($coins as $coin)
                                <option value="{{ $coin->coin_type }}">{{ $coin->name }} ({{ $coin->coin_type }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">شبکه</label>
                        <select name="network_id" class="form-select" required>
                            <option value="">انتخاب کنید</option>
                            @foreach($networks as $network)
                                <option value="{{ $network->id }}">{{ $network->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-primary">ایجاد</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Balance Adjustment Modals -->
@foreach($wallets as $wallet)
    <div class="modal fade" id="adjustBalanceModal{{ $wallet->id }}" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تنظیم موجودی {{ $wallet->coin_type }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.wallets.adjust-balance', $wallet->id) }}" method="POST" class="adjust-balance-form">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">نوع عملیات</label>
                            <select name="operation" class="form-select" required>
                                <option value="add">افزایش</option>
                                <option value="subtract">کاهش</option>
                                <option value="set">تنظیم مقدار دقیق</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">مقدار</label>
                            <input type="number" step="0.00000001" name="amount" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">توضیحات</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-primary">اعمال تغییرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

@php
    $levels = \App\Models\Level::orderBy('priority')->get();
    $currentLevel = $levels->where('priority', $user->level)->first();
@endphp

<div class="card mt-4 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>مدیریت سطح کاربر</h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            @foreach($levels as $level)
                <div class="col-md-3">
                    <div class="card h-100 border-{{ $level->priority == $user->level ? 'primary' : 'light' }} shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-2">
                                <span class="badge rounded-pill" style="background: {{ $level->color }}; color: #fff;">
                                    {{ $level->name }}
                                </span>
                            </div>
                            <h6 class="fw-bold mb-1">{{ $level->title }}</h6>
                            <div class="small text-muted mb-2">{{ $level->features }}</div>
                            <div class="mb-2">
                                <span class="badge bg-info-soft">سقف خرید: {{ $level->formatted_daily_buy_limit }}</span>
                            </div>
                            @if($level->priority == $user->level)
                                <span class="badge bg-primary">سطح فعلی</span>
                            @elseif($level->priority > $user->level)
                                <button class="btn btn-success btn-sm w-100 upgrade-btn"
                                    data-level="{{ $level->id }}"
                                    data-name="{{ $level->name }}"
                                    data-type="upgrade"
                                    @if($level->priority != $user->level+1) disabled @endif
                                >
                                    ارتقا به {{ $level->name }}
                                </button>
                            @elseif($level->priority < $user->level)
                                <button class="btn btn-danger btn-sm w-100 downgrade-btn"
                                    data-level="{{ $level->id }}"
                                    data-name="{{ $level->name }}"
                                    data-type="downgrade"
                                >
                                    تنزل به {{ $level->name }}
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Toast for feedback -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999">
    <div id="levelToast" class="toast align-items-center text-white bg-success border-0" role="alert">
        <div class="d-flex">
            <div class="toast-body" id="levelToastMsg"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    </div>
</div>

<script>
document.querySelectorAll('.upgrade-btn, .downgrade-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        e.preventDefault();
        const levelId = this.dataset.level;
        const levelName = this.dataset.name;
        const type = this.dataset.type;
        const url = type === 'upgrade'
            ? '{{ route('admin.users.upgrade-level', $user->id) }}'
            : '{{ route('admin.users.downgrade-level', $user->id) }}';

        if(!confirm(`آیا مطمئن هستید که می‌خواهید کاربر را به سطح ${levelName} ${type === 'upgrade' ? 'ارتقا' : 'تنزل'} دهید؟`)) return;

        this.disabled = true;
        this.innerHTML = '<span class=\"spinner-border spinner-border-sm\"></span> لطفا صبر کنید...';

        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: new URLSearchParams({level_id: levelId})
        })
        .then(res => res.json())
        .then(data => {
            showLevelToast(data.message, data.success);
            if(data.success) setTimeout(() => location.reload(), 1200);
        })
        .catch(() => showLevelToast('خطا در ارتباط با سرور!', false))
        .finally(() => {
            this.disabled = false;
            this.innerHTML = (type === 'upgrade' ? 'ارتقا به ' : 'تنزل به ') + levelName;
        });
    });
});

function showLevelToast(msg, success) {
    const toastEl = document.getElementById('levelToast');
    const toastMsg = document.getElementById('levelToastMsg');
    toastEl.classList.remove('bg-success', 'bg-danger');
    toastEl.classList.add(success ? 'bg-success' : 'bg-danger');
    toastMsg.innerText = msg;
    const toast = new bootstrap.Toast(toastEl);
    toast.show();
}
</script>

@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Document modal
    const docModal = document.getElementById('documentModal');
    if(docModal) {
        docModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const docUrl = button.getAttribute('data-document-url');
            docModal.querySelector('#document-image').src = docUrl;
        });
    }

    // Balance adjustment forms
    document.querySelectorAll('.adjust-balance-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const btn = this.querySelector('button[type="submit"]');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ...';
            
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: { 'X-Requested-With': 'XMLHttpRequest', 'Accept': 'application/json' }
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message);
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    toastr.error(data.message || 'خطا در انجام عملیات');
                }
            })
            .catch(() => toastr.error('خطا در ارتباط با سرور'))
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = 'اعمال تغییرات';
            });
        });
    });

    // Transaction Chart
    const ctx = document.getElementById('transactionChart')?.getContext('2d');
    if (ctx) {
        const transactionData = @json($user->transactions()->selectRaw('DATE(created_at) as date, COUNT(*) as count')->groupBy('date')->orderBy('date')->get());
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: transactionData.map(item => new Date(item.date).toLocaleDateString('fa-IR')),
                datasets: [{
                    label: 'تعداد تراکنش‌ها',
                    data: transactionData.map(item => item.count),
                    borderColor: 'rgba(59, 130, 246, 0.8)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: { y: { beginAtZero: true, ticks: { stepSize: 1 } } },
                plugins: { legend: { display: false } }
            }
        });
    }

    // Monthly Toman Deposit/Withdrawal Chart
    const tomanMonthlyCtx = document.getElementById('tomanMonthlyChart')?.getContext('2d');
    if (tomanMonthlyCtx) {
        const tomanMonthlyData = {
labels: @json($user->tomanDeposits
    ->groupBy(fn($d) => jdate($d->created_at)->format('Y/m'))
    ->map(fn($g, $month) => $month)
    ->values()
    ->toArray(), JSON_UNESCAPED_UNICODE),
                datasets: [
                {
                    label: 'واریز تومانی',
data: @json(array_values(
    $user->tomanDeposits
        ->groupBy(fn($d) => jdate($d->created_at)->format('Y/m'))
        ->map(fn($g) => $g->sum('amount'))
        ->all()
), JSON_UNESCAPED_UNICODE),                    borderColor: 'rgba(16,185,129,0.8)',
                    backgroundColor: 'rgba(16,185,129,0.1)',
                    fill: true,
                    tension: 0.3
                },
                {
                    label: 'برداشت تومانی',
                    data: @json(array_values(
    $user->tomanWithdrawals
        ->groupBy(fn($d) => jdate($d->created_at)->format('Y/m'))
        ->map(fn($g) => $g->sum('amount'))
        ->all()
), JSON_UNESCAPED_UNICODE),   
                    borderColor: 'rgba(239,68,68,0.8)',
                    backgroundColor: 'rgba(239,68,68,0.1)',
                    fill: true,
                    tension: 0.3
                }
            ]
        };
        new Chart(tomanMonthlyCtx, {
            type: 'line',
            data: tomanMonthlyData,
            options: {
                responsive: true,
                plugins: { legend: { display: true } },
                scales: { y: { beginAtZero: true } }
            }
        });
    }

    // Monthly Crypto Deposit/Withdrawal Chart
    const cryptoMonthlyCtx = document.getElementById('cryptoMonthlyChart')?.getContext('2d');
    if (cryptoMonthlyCtx) {
        const cryptoDepositGroups = @json(collect($user->depositTransactions)->groupBy(fn($d) => jdate($d->created_at)->format('Y/m'))->map(fn($g) => $g->sum('amount'))->toArray(), JSON_UNESCAPED_UNICODE);
        const cryptoWithdrawGroups = @json(collect($user->withdrawHistories)->groupBy(fn($d) => jdate($d->created_at)->format('Y/m'))->map(fn($g) => $g->sum('amount'))->toArray(), JSON_UNESCAPED_UNICODE);
        const months = Array.from(new Set([...Object.keys(cryptoDepositGroups), ...Object.keys(cryptoWithdrawGroups)])).sort();
        const cryptoMonthlyData = {
            labels: months,
            datasets: [
                {
                    label: 'واریز رمز ارزی',
                    data: months.map(m => cryptoDepositGroups[m] || 0),
                    borderColor: 'rgba(59,130,246,0.8)',
                    backgroundColor: 'rgba(59,130,246,0.1)',
                    fill: true,
                    tension: 0.3
                },
                {
                    label: 'برداشت رمز ارزی',
                    data: months.map(m => cryptoWithdrawGroups[m] || 0),
                    borderColor: 'rgba(245,158,11,0.8)',
                    backgroundColor: 'rgba(245,158,11,0.1)',
                    fill: true,
                    tension: 0.3
                }
            ]
        };
        new Chart(cryptoMonthlyCtx, {
            type: 'line',
            data: cryptoMonthlyData,
            options: {
                responsive: true,
                plugins: { legend: { display: true } },
                scales: { y: { beginAtZero: true } }
            }
        });
    }

    // Transaction table filter
    function filterTransactions() {
        const search = document.getElementById('transaction-search').value.trim().toLowerCase();
        const type = document.getElementById('transaction-type').value;
        const status = document.getElementById('transaction-status').value;
        const dateFrom = document.getElementById('transaction-date-from').value;
        const dateTo = document.getElementById('transaction-date-to').value;
        const rows = document.querySelectorAll('#transactions-table tbody tr');
        rows.forEach(row => {
            const tType = row.children[2].textContent.trim();
            const tStatus = row.children[5].textContent.trim();
            const tDesc = row.children[6].textContent.trim().toLowerCase();
            const tAmount = row.children[3].textContent.trim();
            const tDate = row.children[1].textContent.trim();
            let show = true;
            if (search && !(tDesc.includes(search) || tAmount.includes(search))) show = false;
            if (type && tType !== type) show = false;
            if (status && !tStatus.includes(status)) show = false;
            if (dateFrom && tDate < dateFrom.replace(/-/g, '/')) show = false;
            if (dateTo && tDate > dateTo.replace(/-/g, '/')) show = false;
            row.style.display = show ? '' : 'none';
        });
    }
    document.getElementById('transaction-filter-btn').addEventListener('click', filterTransactions);
    ['transaction-search','transaction-type','transaction-status','transaction-date-from','transaction-date-to'].forEach(id => {
        document.getElementById(id).addEventListener('change', filterTransactions);
    });
});

// 2FA Toggle Function
function toggle2FAStatus(element) {
    const userId = element.dataset.userId;
    const enabled = element.checked;

    fetch(`/admin/users/${userId}/toggle-2fa`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ enabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            toastr.error(data.message);
            element.checked = !enabled;
        }
    })
    .catch(() => {
        toastr.error('خطا در ارتباط با سرور');
        element.checked = !enabled;
    });
}
</script>
@endpush
