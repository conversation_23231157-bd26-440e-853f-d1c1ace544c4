<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ورود به پنل مدیریت</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <style>
        :root {
            --primary-color: #5a67d8;
            --secondary-color: #4c51bf;
            --dark-color: #1a202c;
            --light-color: #f7fafc;
            --success-color: #48bb78;
            --warning-color: #ecc94b;
            --danger-color: #e53e3e;
        }

        body {
            font-family: 'Vazirmatn', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }

        .login-container {
            width: 100%;
            max-width: 1000px;
            margin: 2rem;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            background-color: white;
            display: flex;
            flex-direction: row-reverse;
        }

        .login-image {
            flex: 1;
            background: linear-gradient(135deg, rgba(90, 103, 216, 0.9) 0%, rgba(76, 81, 191, 0.9) 100%), url('https://images.unsplash.com/photo-1640340434855-6084b1f4901c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 2rem;
            position: relative;
        }

        .login-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.3;
        }

        .login-form {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            box-shadow: 0 10px 20px rgba(90, 103, 216, 0.3);
        }

        .brand-logo i {
            font-size: 2.5rem;
            color: white;
        }

        .login-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .login-subtitle {
            color: #718096;
            margin-bottom: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-floating > .form-control {
            padding: 1.5rem 1rem 0.5rem;
            height: 60px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .form-floating > .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(90, 103, 216, 0.25);
        }

        .form-floating > label {
            padding: 1rem 1rem 0;
            color: #718096;
        }

        .input-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 1rem;
            color: #718096;
            z-index: 2;
        }

        .btn-login {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            margin-top: 1rem;
            box-shadow: 0 10px 20px rgba(90, 103, 216, 0.3);
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(90, 103, 216, 0.4);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .alert-custom {
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: none;
            background-color: rgba(229, 62, 62, 0.1);
            border-right: 4px solid var(--danger-color);
        }

        .alert-custom ul {
            padding-right: 1rem;
            margin-bottom: 0;
        }

        .alert-custom li {
            color: var(--danger-color);
        }

        .captcha-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .captcha-image {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 0.5rem;
            min-width: 250px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .captcha-image:hover {
            box-shadow: 0 6px 20px rgba(90, 103, 216, 0.2);
            transform: translateY(-1px);
        }

        .captcha-image img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-input .form-control {
            height: 60px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            font-size: 1.2rem;
            text-align: center;
            font-weight: 600;
        }

        .captcha-input .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(90, 103, 216, 0.25);
        }

        .captcha-refresh {
            background: var(--primary-color);
            border: none;
            border-radius: 12px;
            width: 60px;
            height: 60px;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(90, 103, 216, 0.3);
        }

        .captcha-refresh:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(90, 103, 216, 0.4);
        }

        .captcha-refresh:active {
            transform: translateY(0);
        }

        .captcha-label {
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .attempts-warning {
            background-color: rgba(236, 201, 75, 0.1);
            border: 1px solid var(--warning-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #b7791f;
            font-size: 0.9rem;
            text-align: center;
        }

        .attempts-blocked {
            background-color: rgba(229, 62, 62, 0.1);
            border: 1px solid var(--danger-color);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: var(--danger-color);
            font-size: 0.9rem;
            text-align: center;
        }

        .login-image-content {
            text-align: center;
            z-index: 1;
        }

        .login-image-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .login-image-text {
            font-size: 1.1rem;
            max-width: 80%;
            margin: 0 auto 2rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 15s infinite ease-in-out;
        }

        .shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 150px;
            height: 150px;
            bottom: 15%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 70px;
            height: 70px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
            100% { transform: translateY(0) rotate(360deg); }
        }

        @media (max-width: 992px) {
            .login-container {
                flex-direction: column;
                max-width: 500px;
            }

            .login-image {
                min-height: 200px;
                padding: 1.5rem;
            }

            .login-form {
                padding: 2rem;
            }

            .login-image-title {
                font-size: 2rem;
            }

            .login-image-text {
                font-size: 1rem;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="brand-logo">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <h1 class="login-title">خوش آمدید</h1>
            <p class="login-subtitle">برای ورود به پنل مدیریت، اطلاعات خود را وارد کنید</p>

            @if($errors->any())
                <div class="alert alert-custom">
                    <ul>
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if($isBlocked)
                <div class="attempts-blocked">
                    <i class="fas fa-ban me-2"></i>
                    به دلیل تلاش‌های مکرر، دسترسی شما موقتاً مسدود شده است.
                    <br>
                    <strong>{{ $remainingTime }} ثانیه</strong> دیگر می‌توانید دوباره تلاش کنید.
                </div>
            @elseif($attempts > 0)
                <div class="attempts-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ 5 - $attempts }} تلاش دیگر باقی مانده است.
                </div>
            @endif

            <form action="{{ route('admin.login') }}" method="POST">
                @csrf
                <div class="form-floating mb-4">
                    <input type="email" class="form-control ps-5" id="email" name="email" value="{{ old('email') }}" placeholder="ایمیل" required>
                    <label for="email">ایمیل</label>
                    <i class="fas fa-envelope input-icon"></i>
                </div>

                <div class="form-floating mb-4">
                    <input type="password" class="form-control ps-5" id="password" name="password" placeholder="رمز عبور" required>
                    <label for="password">رمز عبور</label>
                    <i class="fas fa-lock input-icon"></i>
                </div>

                <div class="captcha-label">
                    <i class="fas fa-shield-alt me-2"></i>
                    کد امنیتی را وارد کنید
                </div>

                <div class="captcha-container">
                    <div class="captcha-image">
                        <img src="{{ $captcha['image'] }}" alt="CAPTCHA">
                    </div>
                  
                    <button type="button" class="captcha-refresh" onclick="refreshCaptcha()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="captcha-input">
                    <input type="text" class="form-control" name="captcha" placeholder="پاسخ را وارد کنید" required>
                </div>
                <button type="submit" class="btn btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    ورود به پنل مدیریت
                </button>
            </form>
        </div>

        <div class="login-image">
            <div class="floating-shapes">
                <div class="shape"></div>
                <div class="shape"></div>
                <div class="shape"></div>
            </div>
            <div class="login-image-content">
                <h2 class="login-image-title">Exchangim</h2>
                <p class="login-image-text">سیستم مدیریت صرافی آنلاین با امکانات پیشرفته و رابط کاربری حرفه‌ای</p>
                <i class="fas fa-chart-line fa-4x mb-3"></i>
            </div>
        </div>
    </div>

    <script>
        function refreshCaptcha() {
            fetch('{{ route("admin.captcha.refresh") }}')
                .then(response => response.json())
                .then(data => {
                    document.querySelector('.captcha-image').innerHTML = `<img src="${data.image}" alt="CAPTCHA">`;
                    document.querySelector('input[name="captcha"]').value = '';
                })
                .catch(error => {
                    console.error('Error refreshing captcha:', error);
                });
        }
    </script>
</body>
</html>