@extends('admin.layouts.app')

@section('title', 'مدیریت دسترسی‌های نقش')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">مدیریت نقش‌ها</a></li>
        <li class="breadcrumb-item active">مدیریت دسترسی‌ها</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">مدیریت دسترسی‌های نقش</h1>
            <p class="text-muted">تنظیم دسترسی‌های نقش {{ $role->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>ویرایش نقش
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">تنظیم دسترسی‌ها</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.roles.update-permissions', $role->id) }}">
                        @csrf
                        
                        @if($permissions->count() > 0)
                            @foreach($permissions as $group => $groupPermissions)
                                <div class="permission-group mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0 text-primary">
                                            <i class="fas fa-folder me-2"></i>{{ ucfirst($group) }}
                                        </h6>
                                        <div class="form-check">
                                            <input class="form-check-input group-toggle" type="checkbox" 
                                                   id="group_{{ $group }}" data-group="{{ $group }}">
                                            <label class="form-check-label" for="group_{{ $group }}">
                                                انتخاب همه
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        @foreach($groupPermissions as $permission)
                                            <div class="col-md-6 col-lg-4 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input permission-checkbox" 
                                                           type="checkbox" 
                                                           name="permissions[]" 
                                                           value="{{ $permission->id }}" 
                                                           id="permission_{{ $permission->id }}"
                                                           data-group="{{ $group }}"
                                                           {{ in_array($permission->id, $rolePermissions) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                        <strong>{{ $permission->action }}</strong>
                                                        <br>
                                                        <small class="text-muted">{{ $permission->description ?? $permission->route }}</small>
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <hr>
                            @endforeach
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                هیچ دسترسی‌ای تعریف نشده است. ابتدا دسترسی‌ها را در سیستم تعریف کنید.
                            </div>
                        @endif

                        @if($permissions->count() > 0)
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <button type="button" class="btn btn-outline-primary" id="select-all">
                                        <i class="fas fa-check-square me-2"></i>انتخاب همه
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="deselect-all">
                                        <i class="fas fa-square me-2"></i>لغو انتخاب همه
                                    </button>
                                </div>
                                <div>
                                    <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">انصراف</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>ذخیره دسترسی‌ها
                                    </button>
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">اطلاعات نقش</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="role-icon mx-auto mb-2">
                            <i class="fas fa-user-tag fa-2x text-{{ $role->color }}"></i>
                        </div>
                        <h6>{{ $role->name }}</h6>
                        @if($role->name === 'admin')
                            <span class="badge bg-warning">نقش اصلی</span>
                        @endif
                    </div>

                    <hr>

                    <div class="mb-2">
                        <strong>Guard:</strong> {{ $role->guard_name }}
                    </div>
                    <div class="mb-2">
                        <strong>تعداد کاربران:</strong>
                        <span class="badge bg-info">{{ $role->users->count() }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>تعداد دسترسی‌های فعلی:</strong>
                        <span class="badge bg-success" id="current-permissions-count">{{ count($rolePermissions) }}</span>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">راهنما</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>دسترسی‌ها بر اساس گروه‌بندی شده‌اند</li>
                            <li>می‌توانید گروه کامل را انتخاب کنید</li>
                            <li>تغییرات بلافاصله اعمال می‌شود</li>
                            <li>کاربران این نقش دسترسی جدید خواهند داشت</li>
                        </ul>
                    </div>

                    @if($role->name === 'admin')
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>توجه:</h6>
                            <p class="mb-0">
                                این نقش اصلی سیستم است. تغییر دسترسی‌ها با احتیاط انجام دهید.
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Permission Summary -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">خلاصه دسترسی‌ها</h6>
                </div>
                <div class="card-body">
                    <div id="permissions-summary">
                        @foreach($permissions as $group => $groupPermissions)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ ucfirst($group) }}:</span>
                                <span class="badge bg-secondary" id="summary-{{ $group }}">
                                    {{ $groupPermissions->whereIn('id', $rolePermissions)->count() }}/{{ $groupPermissions->count() }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.role-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: center;
}

.permission-group {
    background: #f8f9fc;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e3e6f0;
}

.form-check-label {
    font-size: 0.9rem;
    color: #5a5c69;
}

.group-toggle {
    transform: scale(1.1);
}

.permission-checkbox:checked + label {
    color: #5a67d8;
    font-weight: 500;
}
</style>
@endsection



@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select/Deselect all functionality
    document.getElementById('select-all').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateGroupToggles();
        updateSummary();
    });

    document.getElementById('deselect-all').addEventListener('click', function() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateGroupToggles();
        updateSummary();
    });

    // Group toggle functionality
    document.querySelectorAll('.group-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const group = this.dataset.group;
            const isChecked = this.checked;
            
            document.querySelectorAll(`.permission-checkbox[data-group="${group}"]`).forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateSummary();
        });
    });

    // Individual checkbox change
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateGroupToggles();
            updateSummary();
        });
    });

    // Update group toggles based on individual checkboxes
    function updateGroupToggles() {
        document.querySelectorAll('.group-toggle').forEach(toggle => {
            const group = toggle.dataset.group;
            const groupCheckboxes = document.querySelectorAll(`.permission-checkbox[data-group="${group}"]`);
            const checkedCount = document.querySelectorAll(`.permission-checkbox[data-group="${group}"]:checked`).length;
            
            if (checkedCount === 0) {
                toggle.checked = false;
                toggle.indeterminate = false;
            } else if (checkedCount === groupCheckboxes.length) {
                toggle.checked = true;
                toggle.indeterminate = false;
            } else {
                toggle.checked = false;
                toggle.indeterminate = true;
            }
        });
    }

    // Update summary
    function updateSummary() {
        const totalChecked = document.querySelectorAll('.permission-checkbox:checked').length;
        document.getElementById('current-permissions-count').textContent = totalChecked;

        // Update group summaries
        document.querySelectorAll('.group-toggle').forEach(toggle => {
            const group = toggle.dataset.group;
            const groupCheckboxes = document.querySelectorAll(`.permission-checkbox[data-group="${group}"]`);
            const checkedCount = document.querySelectorAll(`.permission-checkbox[data-group="${group}"]:checked`).length;
            const summaryElement = document.getElementById(`summary-${group}`);
            if (summaryElement) {
                summaryElement.textContent = `${checkedCount}/${groupCheckboxes.length}`;
            }
        });
    }

    // Initialize
    updateGroupToggles();
    updateSummary();
});
</script>
@endpush
