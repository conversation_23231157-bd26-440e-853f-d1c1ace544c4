@extends('admin.layouts.app')

@section('title', 'مدیریت نقش‌ها')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item active">مدیریت نقش‌ها</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">مدیریت نقش‌ها</h1>
            <p class="text-muted">مدیریت نقش‌ها و دسترسی‌های سیستم</p>
        </div>
        <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>افزودن نقش جدید
        </a>
    </div>

    <!-- Roles List -->
    <div class="row">
        @foreach($roles as $role)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-{{ $role->color }} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-user-tag me-2"></i>{{ $role->name }}
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-light" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.roles.show', $role->id) }}">
                                            <i class="fas fa-eye me-2"></i>مشاهده جزئیات
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.roles.edit', $role->id) }}">
                                            <i class="fas fa-edit me-2"></i>ویرایش
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('admin.roles.permissions', $role->id) }}">
                                            <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
                                        </a>
                                    </li>
                                    @if($role->name !== 'admin' && $role->users_count == 0)
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form method="POST" action="{{ route('admin.roles.destroy', $role->id) }}" 
                                                  style="display: inline;"
                                                  onsubmit="return confirm('آیا از حذف این نقش اطمینان دارید؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="fas fa-trash me-2"></i>حذف نقش
                                                </button>
                                            </form>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-{{ $role->color }}">{{ $role->users_count }}</h4>
                                    <small class="text-muted">کاربران</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-info">{{ $role->permissions->count() ?? 0 }}</h4>
                                <small class="text-muted">دسترسی‌ها</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">Guard: {{ $role->guard_name }}</small>
                            @if($role->name === 'admin')
                                <span class="badge bg-warning">نقش اصلی</span>
                            @endif
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('admin.roles.show', $role->id) }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye me-1"></i>مشاهده
                            </a>
                            <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-key me-1"></i>دسترسی‌ها
                            </a>
                            <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-edit me-1"></i>ویرایش
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if($roles->count() == 0)
        <div class="text-center py-5">
            <i class="fas fa-user-tag fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">هیچ نقشی یافت نشد</h5>
            <p class="text-muted">برای شروع، نقش جدیدی اضافه کنید.</p>
            <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>افزودن نقش جدید
            </a>
        </div>
    @endif
</div>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-bottom: none;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.text-danger:hover {
    background-color: #f8d7da;
    color: #721c24 !important;
}
</style>
@endsection
