@extends('admin.layouts.app')

@section('title', 'جزئیات نقش')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">مدیریت نقش‌ها</a></li>
        <li class="breadcrumb-item active">جزئیات نقش</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">جزئیات نقش</h1>
            <p class="text-muted">اطلاعات کامل نقش {{ $role->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-info">
                <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
            </a>
            <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>ویرایش
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Role Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">اطلاعات نقش</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">نام نقش:</label>
                                <div class="fw-bold">
                                    <span class="badge bg-{{ $role->color }} fs-6">{{ $role->name }}</span>
                                    @if($role->name === 'admin')
                                        <span class="badge bg-warning ms-2">نقش اصلی</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Guard Name:</label>
                                <div class="fw-bold">{{ $role->guard_name }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">شناسه:</label>
                                <div class="fw-bold">#{{ $role->id }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تعداد دسترسی‌ها:</label>
                                <div class="fw-bold">
                                    <span class="badge bg-success">{{ $role->permissions->count() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users with this role -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        کاربران دارای این نقش ({{ $users->total() }} نفر)
                    </h6>
                </div>
                <div class="card-body">
                    @if($users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th width="60">#</th>
                                        <th>کاربر</th>
                                        <th>اطلاعات تماس</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ اختصاص</th>
                                        <th width="120">عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($users as $userRole)
                                        @if($userRole->user)
                                            <tr>
                                                <td>{{ $userRole->user->id }}</td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-3">
                                                            {{ substr($userRole->user->firstname, 0, 1) }}{{ substr($userRole->user->lastname, 0, 1) }}
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold">{{ $userRole->user->firstname }} {{ $userRole->user->lastname }}</div>
                                                            @if($userRole->user->super_admin)
                                                                <span class="badge bg-danger">سوپر ادمین</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <i class="fas fa-envelope text-muted me-1"></i>
                                                        {{ $userRole->user->email }}
                                                    </div>
                                                    <div>
                                                        <i class="fas fa-phone text-muted me-1"></i>
                                                        {{ $userRole->user->phone }}
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($userRole->user->status === 'active')
                                                        <span class="badge bg-success">فعال</span>
                                                    @else
                                                        <span class="badge bg-danger">غیرفعال</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    {{ jdate($userRole->user->created_at)->format('Y/m/d') }}
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ route('admin.admin-management.show', $userRole->user->id) }}" 
                                                           class="btn btn-sm btn-outline-info" title="مشاهده">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="{{ route('admin.admin-management.edit', $userRole->user->id) }}" 
                                                           class="btn btn-sm btn-outline-warning" title="ویرایش">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($users->hasPages())
                            <div class="d-flex justify-content-center mt-4">
                                {{ $users->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ کاربری با این نقش یافت نشد</h5>
                            <p class="text-muted">هنوز کاربری به این نقش اختصاص داده نشده است.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Role Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="role-icon mx-auto mb-3">
                        <i class="fas fa-user-tag fa-3x text-{{ $role->color }}"></i>
                    </div>
                    <h5 class="card-title">{{ $role->name }}</h5>
                    <p class="text-muted">Guard: {{ $role->guard_name }}</p>
                    
                    @if($role->name === 'admin')
                        <span class="badge bg-warning mb-3">نقش اصلی</span>
                    @endif

                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>ویرایش نقش
                        </a>
                        <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-info">
                            <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
                        </a>
                        @if($role->name !== 'admin' && $users->total() == 0)
                            <form method="POST" action="{{ route('admin.roles.destroy', $role->id) }}" 
                                  onsubmit="return confirm('آیا از حذف این نقش اطمینان دارید؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-2"></i>حذف نقش
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">آمار سریع</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">تعداد کاربران:</span>
                        <span class="fw-bold">{{ $users->total() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">تعداد دسترسی‌ها:</span>
                        <span class="fw-bold">{{ $role->permissions->count() }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">نوع نقش:</span>
                        @if($role->name === 'admin')
                            <span class="badge bg-warning">اصلی</span>
                        @else
                            <span class="badge bg-info">سفارشی</span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Permissions Summary -->
            @if($role->permissions->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-success">خلاصه دسترسی‌ها</h6>
                    </div>
                    <div class="card-body">
                        @php
                            $permissionGroups = $role->permissions->groupBy('group');
                        @endphp
                        @foreach($permissionGroups as $group => $permissions)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted">{{ ucfirst($group) }}:</span>
                                <span class="badge bg-secondary">{{ $permissions->count() }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.role-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-label {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.card-header h6 {
    color: #5a5c69;
}

.badge.fs-6 {
    font-size: 0.875rem !important;
}

.btn-group .btn {
    margin-left: 2px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}
</style>
@endsection
