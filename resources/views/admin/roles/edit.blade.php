@extends('admin.layouts.app')

@section('title', 'ویرایش نقش')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">مدیریت نقش‌ها</a></li>
        <li class="breadcrumb-item active">ویرایش نقش</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">ویرایش نقش</h1>
            <p class="text-muted">ویرایش نقش {{ $role->name }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.roles.permissions', $role->id) }}" class="btn btn-info">
                <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
            </a>
            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">ویرایش اطلاعات نقش</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.roles.update', $role->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">نام نقش <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $role->name) }}" required
                                   {{ $role->name === 'admin' ? 'readonly' : '' }}>
                            @if($role->name === 'admin')
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    نام نقش اصلی قابل تغییر نیست
                                </div>
                            @else
                                <div class="form-text">نام نقش باید منحصر به فرد باشد و از حروف انگلیسی استفاده کنید.</div>
                            @endif
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="guard_name" class="form-label">Guard Name <span class="text-danger">*</span></label>
                            <select class="form-select @error('guard_name') is-invalid @enderror" 
                                    id="guard_name" name="guard_name" required
                                    {{ $role->name === 'admin' ? 'disabled' : '' }}>
                                <option value="">انتخاب کنید...</option>
                                <option value="web" {{ old('guard_name', $role->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                <option value="api" {{ old('guard_name', $role->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                            </select>
                            @if($role->name === 'admin')
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Guard Name نقش اصلی قابل تغییر نیست
                                </div>
                                <input type="hidden" name="guard_name" value="{{ $role->guard_name }}">
                            @else
                                <div class="form-text">معمولاً "web" برای پنل مدیریت استفاده می‌شود.</div>
                            @endif
                            @error('guard_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">انصراف</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>بروزرسانی نقش
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">اطلاعات نقش</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="role-icon mx-auto mb-2">
                            <i class="fas fa-user-tag fa-2x text-{{ $role->color }}"></i>
                        </div>
                        <h6>{{ $role->name }}</h6>
                        @if($role->name === 'admin')
                            <span class="badge bg-warning">نقش اصلی</span>
                        @endif
                    </div>

                    <hr>

                    <div class="mb-2">
                        <strong>شناسه:</strong> #{{ $role->id }}
                    </div>
                    <div class="mb-2">
                        <strong>Guard:</strong> {{ $role->guard_name }}
                    </div>
                    <div class="mb-2">
                        <strong>تعداد کاربران:</strong> 
                        <span class="badge bg-info">{{ $role->users->count() }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>تعداد دسترسی‌ها:</strong> 
                        <span class="badge bg-success">{{ $role->permissions->count() }}</span>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">راهنما</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>نقش‌های اصلی محدودیت‌هایی دارند</li>
                            <li>تغییر نام نقش بر دسترسی‌ها تأثیر نمی‌گذارد</li>
                            <li>Guard Name را با دقت انتخاب کنید</li>
                            <li>پس از ویرایش، دسترسی‌ها را بررسی کنید</li>
                        </ul>
                    </div>

                    @if($role->name === 'admin')
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>نقش اصلی:</h6>
                            <p class="mb-0">
                                این نقش اصلی سیستم است و برخی تنظیمات آن قابل تغییر نیست.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.role-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}

.alert h6 {
    font-weight: 600;
}
</style>
@endsection
