@extends('admin.layouts.app')

@section('title', 'افزودن نقش جدید')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">مدیریت نقش‌ها</a></li>
        <li class="breadcrumb-item active">افزودن نقش جدید</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">افزودن نقش جدید</h1>
            <p class="text-muted">ایجاد نقش جدید برای سیستم</p>
        </div>
        <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">اطلاعات نقش جدید</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.roles.store') }}">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">نام نقش <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required>
                            <div class="form-text">نام نقش باید منحصر به فرد باشد و از حروف انگلیسی استفاده کنید.</div>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="guard_name" class="form-label">Guard Name <span class="text-danger">*</span></label>
                            <select class="form-select @error('guard_name') is-invalid @enderror" 
                                    id="guard_name" name="guard_name" required>
                                <option value="">انتخاب کنید...</option>
                                <option value="web" {{ old('guard_name') == 'web' ? 'selected' : '' }}>Web</option>
                                <option value="api" {{ old('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                            </select>
                            <div class="form-text">معمولاً "web" برای پنل مدیریت استفاده می‌شود.</div>
                            @error('guard_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">انصراف</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>ذخیره نقش
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">راهنما</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>نام نقش باید منحصر به فرد باشد</li>
                            <li>از حروف انگلیسی و خط تیره استفاده کنید</li>
                            <li>پس از ایجاد، دسترسی‌ها را تنظیم کنید</li>
                            <li>Guard Name معمولاً "web" است</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>توجه:</h6>
                        <p class="mb-0">
                            نقش جدید بدون دسترسی ایجاد می‌شود. 
                            حتماً پس از ایجاد، دسترسی‌های مورد نیاز را تنظیم کنید.
                        </p>
                    </div>
                </div>
            </div>

            <!-- نمونه نقش‌ها -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">نمونه نقش‌ها</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-primary">admin</span>
                        <small class="text-muted ms-2">مدیر کل سیستم</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info">support</span>
                        <small class="text-muted ms-2">پشتیبانی</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning">moderator</span>
                        <small class="text-muted ms-2">مدیر محتوا</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary">operator</span>
                        <small class="text-muted ms-2">اپراتور</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}

.alert h6 {
    font-weight: 600;
}

.badge {
    font-size: 0.75rem;
}
</style>
@endsection
