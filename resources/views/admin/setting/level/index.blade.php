@extends('admin.layouts.app')

@section('title', 'مدیریت سطوح کاربری')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item active">مدیریت سطوح کاربری</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title h3">مدیریت سطوح کاربری</h1>
                    <p class="text-muted">تنظیم و مدیریت سطوح مختلف کاربران سیستم</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editLevelsModal">
                        <i class="fas fa-edit me-2"></i>ویرایش سطوح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- کارت‌های آماری -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-primary-subtle rounded-circle p-3">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="stat-label text-muted mb-1">کل سطوح</h6>
                            <h3 class="stat-value mb-0">{{ $levels->count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-success-subtle rounded-circle p-3">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="stat-label text-muted mb-1">سطوح فعال</h6>
                            <h3 class="stat-value mb-0">{{ $levels->where('active', true)->count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-warning-subtle rounded-circle p-3">
                                <i class="fas fa-infinity text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="stat-label text-muted mb-1">خرید نامحدود</h6>
                            <h3 class="stat-value mb-0">{{ $levels->where('is_unlimited_buy', true)->count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stat-icon bg-info-subtle rounded-circle p-3">
                                <i class="fas fa-exchange-alt text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="stat-label text-muted mb-1">فروش نامحدود</h6>
                            <h3 class="stat-value mb-0">{{ $levels->where('is_unlimited_sell', true)->count() }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نمایش سطوح -->
    <div class="row">
        @foreach($levels as $level)
        <div class="col-xl-6 col-lg-12 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="level-badge me-3" style="background-color: {{ $level->color }}; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px;">
                                {{ substr($level->name, 0, 1) }}
                            </div>
                            <div>
                                <h5 class="mb-1">{{ $level->name }}</h5>
                                <span class="badge bg-{{ $level->active ? 'success' : 'secondary' }}-subtle text-{{ $level->active ? 'success' : 'secondary' }}">
                                    {{ $level->active ? 'فعال' : 'غیرفعال' }}
                                </span>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.setting.level.show', $level->id) }}">
                                    <i class="fas fa-eye me-2"></i>مشاهده جزئیات
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="editLevel({{ $level->id }})">
                                    <i class="fas fa-edit me-2"></i>ویرایش
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon bg-primary-subtle rounded-circle p-2 me-2">
                                    <i class="fas fa-shopping-cart text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">خرید روزانه</small>
                                    <strong>{{ $level->formatted_daily_buy_limit }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon bg-success-subtle rounded-circle p-2 me-2">
                                    <i class="fas fa-cash-register text-success"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">فروش روزانه</small>
                                    <strong>{{ $level->formatted_daily_sell_limit }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon bg-warning-subtle rounded-circle p-2 me-2">
                                    <i class="fas fa-wallet text-warning"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">برداشت روزانه</small>
                                    <strong>{{ $level->formatted_daily_withdrawal_limit }}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon bg-info-subtle rounded-circle p-2 me-2">
                                    <i class="fas fa-clock text-info"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">زمان لازم</small>
                                    <strong>{{ $level->formatted_days_from_previous_level }}</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($level->total_purchase_requirement > 0)
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            <div>
                                <small class="text-muted d-block">حداقل خرید کل</small>
                                <strong>{{ $level->formatted_total_purchase_requirement }}</strong>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="mt-3">
                        <h6 class="text-muted mb-2">ویژگی‌ها:</h6>
                        <div class="features-text">
                            {!! nl2br(e($level->features)) !!}
                        </div>
                    </div>

                    @if($level->restrictions)
                    <div class="mt-3">
                        <h6 class="text-muted mb-2">محدودیت‌ها:</h6>
                        <div class="restrictions-text text-muted">
                            {!! nl2br(e($level->restrictions)) !!}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>

<!-- Modal ویرایش سطوح -->
<div class="modal fade" id="editLevelsModal" tabindex="-1" aria-labelledby="editLevelsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editLevelsModalLabel">ویرایش سطوح کاربری</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.setting.level.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        @foreach($levels as $level)
                        <div class="col-md-6 mb-4">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">{{ $level->name }}</h6>
                                </div>
                                <div class="card-body">
                                    <input type="hidden" name="levels[{{ $level->id }}][id]" value="{{ $level->id }}">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">خرید روزانه (تومان)</label>
                                        <input type="number" class="form-control" name="levels[{{ $level->id }}][daily_buy_limit]" 
                                               value="{{ $level->daily_buy_limit }}" placeholder="0 برای نامحدود">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">فروش روزانه (تومان)</label>
                                        <input type="number" class="form-control" name="levels[{{ $level->id }}][daily_sell_limit]" 
                                               value="{{ $level->daily_sell_limit }}" placeholder="0 برای نامحدود">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">برداشت روزانه (تومان)</label>
                                        <input type="number" class="form-control" name="levels[{{ $level->id }}][daily_withdrawal_limit]" 
                                               value="{{ $level->daily_withdrawal_limit }}" placeholder="0 برای نامحدود">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">حداقل خرید کل (تومان)</label>
                                        <input type="number" class="form-control" name="levels[{{ $level->id }}][total_purchase_requirement]" 
                                               value="{{ $level->total_purchase_requirement }}">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">روزهای لازم از سطح قبلی</label>
                                        <input type="number" class="form-control" name="levels[{{ $level->id }}][days_from_previous_level]" 
                                               value="{{ $level->days_from_previous_level }}">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">ویژگی‌ها</label>
                                        <textarea class="form-control" name="levels[{{ $level->id }}][features]" rows="3">{{ $level->features }}</textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">محدودیت‌ها</label>
                                        <textarea class="form-control" name="levels[{{ $level->id }}][restrictions]" rows="3">{{ $level->restrictions }}</textarea>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="levels[{{ $level->id }}][is_unlimited_buy]" value="1" 
                                               {{ $level->is_unlimited_buy ? 'checked' : '' }}>
                                        <label class="form-check-label">خرید نامحدود</label>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="levels[{{ $level->id }}][is_unlimited_sell]" value="1" 
                                               {{ $level->is_unlimited_sell ? 'checked' : '' }}>
                                        <label class="form-check-label">فروش نامحدود</label>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="levels[{{ $level->id }}][active]" value="1" 
                                               {{ $level->active ? 'checked' : '' }}>
                                        <label class="form-check-label">فعال</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-primary">ذخیره تغییرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 500;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.features-text, .restrictions-text {
    font-size: 0.875rem;
    line-height: 1.5;
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 6px;
    border-right: 3px solid #dee2e6;
}

.level-badge {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.modal-xl {
    max-width: 1200px;
}
</style>
@endsection

@section('scripts')
<script>
function editLevel(levelId) {
    // Scroll to the specific level in the modal
    const modal = document.getElementById('editLevelsModal');
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // You can add specific logic here to highlight the level being edited
    setTimeout(() => {
        const levelCard = modal.querySelector(`[name="levels[${levelId}][id]"]`).closest('.card');
        levelCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
        levelCard.style.border = '2px solid #007bff';
        setTimeout(() => {
            levelCard.style.border = '';
        }, 2000);
    }, 500);
}
</script>
@endsection 