@extends('admin.layouts.app')

@section('title', 'جزئیات سطح ' . $level->name)

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.setting.level.index') }}">مدیریت سطوح کاربری</a></li>
    <li class="breadcrumb-item active">جزئیات سطح {{ $level->name }}</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="level-badge me-3" style="background-color: {{ $level->color }}; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 24px;">
                        {{ substr($level->name, 0, 1) }}
                    </div>
                    <div>
                        <h1 class="page-title h3 mb-1">سطح {{ $level->name }}</h1>
                        <p class="text-muted mb-0">{{ $level->title }}</p>
                    </div>
                </div>
                <div>
                    <a href="{{ route('admin.setting.level.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right me-2"></i>بازگشت
                    </a>
                    <button type="button" class="btn btn-primary" onclick="editLevel({{ $level->id }})">
                        <i class="fas fa-edit me-2"></i>ویرایش
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- اطلاعات اصلی -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">اطلاعات سطح</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-primary-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-shopping-cart text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">خرید روزانه</h6>
                                    <p class="info-value mb-0">{{ $level->formatted_daily_buy_limit }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-success-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-cash-register text-success"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">فروش روزانه</h6>
                                    <p class="info-value mb-0">{{ $level->formatted_daily_sell_limit }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-warning-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-wallet text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">برداشت روزانه</h6>
                                    <p class="info-value mb-0">{{ $level->formatted_daily_withdrawal_limit }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-info-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-clock text-info"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">زمان لازم از سطح قبلی</h6>
                                    <p class="info-value mb-0">{{ $level->formatted_days_from_previous_level }}</p>
                                </div>
                            </div>
                        </div>
                        @if($level->total_purchase_requirement > 0)
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-purple-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-chart-line text-purple"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">حداقل خرید کل</h6>
                                    <p class="info-value mb-0">{{ $level->formatted_total_purchase_requirement }}</p>
                                </div>
                            </div>
                        </div>
                        @endif
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-icon bg-secondary-subtle rounded-circle p-3 me-3">
                                    <i class="fas fa-toggle-on text-secondary"></i>
                                </div>
                                <div>
                                    <h6 class="info-label">وضعیت</h6>
                                    <p class="info-value mb-0">
                                        <span class="badge bg-{{ $level->active ? 'success' : 'secondary' }}-subtle text-{{ $level->active ? 'success' : 'secondary' }}">
                                            {{ $level->active ? 'فعال' : 'غیرفعال' }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ویژگی‌ها -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">ویژگی‌ها</h5>
                </div>
                <div class="card-body">
                    <div class="features-content">
                        {!! nl2br(e($level->features)) !!}
                    </div>
                </div>
            </div>

            <!-- محدودیت‌ها -->
            @if($level->restrictions)
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">محدودیت‌ها</h5>
                </div>
                <div class="card-body">
                    <div class="restrictions-content">
                        {!! nl2br(e($level->restrictions)) !!}
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- کارت‌های جانبی -->
        <div class="col-lg-4">
            <!-- وضعیت سطح -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent border-0">
                    <h6 class="mb-0">وضعیت سطح</h6>
                </div>
                <div class="card-body">
                    <div class="status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-success-subtle rounded-circle p-2 me-3">
                            <i class="fas fa-check text-success"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">خرید نامحدود</small>
                            <strong>{{ $level->is_unlimited_buy ? 'بله' : 'خیر' }}</strong>
                        </div>
                    </div>
                    <div class="status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-info-subtle rounded-circle p-2 me-3">
                            <i class="fas fa-exchange-alt text-info"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">فروش نامحدود</small>
                            <strong>{{ $level->is_unlimited_sell ? 'بله' : 'خیر' }}</strong>
                        </div>
                    </div>
                    <div class="status-item d-flex align-items-center">
                        <div class="status-icon bg-warning-subtle rounded-circle p-2 me-3">
                            <i class="fas fa-toggle-on text-warning"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">وضعیت فعال</small>
                            <strong>{{ $level->active ? 'فعال' : 'غیرفعال' }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اطلاعات فنی -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent border-0">
                    <h6 class="mb-0">اطلاعات فنی</h6>
                </div>
                <div class="card-body">
                    <div class="tech-item d-flex justify-content-between mb-2">
                        <span class="text-muted">شناسه:</span>
                        <strong>{{ $level->id }}</strong>
                    </div>
                    <div class="tech-item d-flex justify-content-between mb-2">
                        <span class="text-muted">اولویت:</span>
                        <strong>{{ $level->priority }}</strong>
                    </div>
                    <div class="tech-item d-flex justify-content-between mb-2">
                        <span class="text-muted">رنگ:</span>
                        <div class="d-flex align-items-center">
                            <div class="color-preview me-2" style="background-color: {{ $level->color }}; width: 20px; height: 20px; border-radius: 4px;"></div>
                            <strong>{{ $level->color }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عملیات سریع -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent border-0">
                    <h6 class="mb-0">عملیات سریع</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="editLevel({{ $level->id }})">
                            <i class="fas fa-edit me-2"></i>ویرایش سطح
                        </button>
                        <a href="{{ route('admin.setting.level.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>مشاهده همه سطوح
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal ویرایش سطح -->
<div class="modal fade" id="editLevelModal" tabindex="-1" aria-labelledby="editLevelModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editLevelModalLabel">ویرایش سطح {{ $level->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.setting.level.update', $level->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">نام سطح</label>
                            <input type="text" class="form-control" name="name" value="{{ $level->name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عنوان</label>
                            <input type="text" class="form-control" name="title" value="{{ $level->title }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رنگ</label>
                            <input type="color" class="form-control" name="color" value="{{ $level->color }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اولویت</label>
                            <input type="number" class="form-control" name="priority" value="{{ $level->priority }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">خرید روزانه (تومان)</label>
                            <input type="number" class="form-control" name="daily_buy_limit" value="{{ $level->daily_buy_limit }}" placeholder="0 برای نامحدود">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">فروش روزانه (تومان)</label>
                            <input type="number" class="form-control" name="daily_sell_limit" value="{{ $level->daily_sell_limit }}" placeholder="0 برای نامحدود">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">برداشت روزانه (تومان)</label>
                            <input type="number" class="form-control" name="daily_withdrawal_limit" value="{{ $level->daily_withdrawal_limit }}" placeholder="0 برای نامحدود">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حداقل خرید کل (تومان)</label>
                            <input type="number" class="form-control" name="total_purchase_requirement" value="{{ $level->total_purchase_requirement }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">روزهای لازم از سطح قبلی</label>
                            <input type="number" class="form-control" name="days_from_previous_level" value="{{ $level->days_from_previous_level }}">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">ویژگی‌ها</label>
                            <textarea class="form-control" name="features" rows="4">{{ $level->features }}</textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">محدودیت‌ها</label>
                            <textarea class="form-control" name="restrictions" rows="4">{{ $level->restrictions }}</textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="is_unlimited_buy" value="1" {{ $level->is_unlimited_buy ? 'checked' : '' }}>
                                <label class="form-check-label">خرید نامحدود</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="is_unlimited_sell" value="1" {{ $level->is_unlimited_sell ? 'checked' : '' }}>
                                <label class="form-check-label">فروش نامحدود</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="active" value="1" {{ $level->active ? 'checked' : '' }}>
                                <label class="form-check-label">فعال</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-primary">ذخیره تغییرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.level-badge {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.info-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.info-item:hover {
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.info-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.status-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.status-icon {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.tech-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.tech-item:last-child {
    border-bottom: none;
}

.features-content, .restrictions-content {
    font-size: 0.95rem;
    line-height: 1.6;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-right: 4px solid #dee2e6;
}

.color-preview {
    border: 1px solid #dee2e6;
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}
</style>
@endsection

@section('scripts')
<script>
function editLevel(levelId) {
    const modal = document.getElementById('editLevelModal');
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}
</script>
@endsection 