@extends('admin.layouts.app')

@push('styles')
<style>
.wallet-address {
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    word-break: break-all;
}

.user-info {
    font-size: 0.9em;
}

.coin-badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

.network-badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

.search-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.table td {
    vertical-align: middle;
}

.copy-btn {
    border: none;
    background: transparent;
    color: #6c757d;
    transition: color 0.2s;
    padding: 2px 6px;
    margin-right: 5px;
}

.copy-btn:hover {
    color: #007bff;
}

.dropdown-menu {
    min-width: 200px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.dropdown-item i {
    width: 16px;
    margin-left: 8px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.operations-menu {
    position: absolute;
    z-index: 1000;
    min-width: 180px;
    margin-top: 5px;
    right: 0;
}

.operations-menu .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.operations-menu .btn {
    text-align: right;
    border: none;
    background: transparent;
    color: #495057;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.operations-menu .btn:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.operations-menu .btn i {
    width: 16px;
    margin-left: 8px;
}

.table td {
    position: relative;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $wallets->total() }}</h3>
                    <p>کل کیف پول‌ها</p>
                </div>
                <div class="icon">
                    <i class="fas fa-wallet"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $coinTypes->count() }}</h3>
                    <p>انواع ارز</p>
                </div>
                <div class="icon">
                    <i class="fas fa-coins"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $networks->count() }}</h3>
                    <p>شبکه‌های فعال</p>
                </div>
                <div class="icon">
                    <i class="fas fa-network-wired"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3>{{ $wallets->where('memo', 'like', '%BLOCKED%')->count() }}</h3>
                    <p>کیف پول‌های مسدود</p>
                </div>
                <div class="icon">
                    <i class="fas fa-ban"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">کیف پول‌های کاربران</h3>
                    <div class="card-tools">
                        <button class="btn btn-success mr-2" onclick="exportAllWallets()">
                            <i class="fas fa-download"></i> دانلود همه
                        </button>
                        <a href="{{ route('admin.system-wallets.index') }}" class="btn btn-secondary">
                            بازگشت به کیف پول‌های سیستمی
                        </a>
                    </div>
                </div>
                
                <!-- Search and Filter Form -->
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.system-wallets.user-wallets') }}" class="search-form">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">جستجو</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="search" 
                                           name="search" 
                                           value="{{ request('search') }}" 
                                           placeholder="نام، موبایل، کد ملی یا آدرس کیف پول">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="coin_type">نوع ارز</label>
                                    <select class="form-control" id="coin_type" name="coin_type">
                                        <option value="">همه ارزها</option>
                                        @foreach($coinTypes as $coinType)
                                            <option value="{{ $coinType }}" 
                                                    {{ request('coin_type') == $coinType ? 'selected' : '' }}>
                                                {{ $coinType }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="network_id">شبکه</label>
                                    <select class="form-control" id="network_id" name="network_id">
                                        <option value="">همه شبکه‌ها</option>
                                        @foreach($networks as $network)
                                            <option value="{{ $network->id }}" 
                                                    {{ request('network_id') == $network->id ? 'selected' : '' }}>
                                                {{ $network->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="d-flex">
                                        <button type="submit" class="btn btn-primary mr-2">جستجو</button>
                                        <a href="{{ route('admin.system-wallets.user-wallets') }}" class="btn btn-secondary">پاک کردن</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Results Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>کاربر</th>
                                    <th>آدرس کیف پول</th>
                                    <th>ارز</th>
                                    <th>شبکه</th>
                                    <th>Memo</th>
                                    <th>تاریخ ایجاد</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($wallets as $wallet)
                                    <tr class="{{ str_contains($wallet->memo ?? '', 'BLOCKED') ? 'table-danger' : '' }}">
                                        <td>
                                            <div class="user-info">
                                                <strong>{{ $wallet->user->firstname }} {{ $wallet->user->lastname }}</strong>
                                                @if(str_contains($wallet->memo ?? '', 'BLOCKED'))
                                                    <span class="badge badge-danger badge-sm mr-1">مسدود</span>
                                                @endif
                                                <br>
                                                <small class="text-muted">0{{ $wallet->user->phone }}</small><br>
                                                @if($wallet->user->national_id)
                                                    <small class="text-muted">کد ملی: {{ $wallet->user->national_id }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <button class="copy-btn" onclick="copyToClipboard('{{ $wallet->address }}')" title="کپی آدرس">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <span class="wallet-address">{{ $wallet->address }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-danger coin-badge">
                                                {{ $wallet->coin?->name ?: $wallet->coin_type }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($wallet->network)
                                                <span class="badge badge-info network-badge">
                                                    {{ $wallet->network->name }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($wallet->memo)
                                                <span class="text-muted">{{ $wallet->memo }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ jdate($wallet->created_at)->format('Y/m/d H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleOperations({{ $wallet->id }})">
                                                    <i class="fas fa-cog"></i> عملیات
                                                </button>
                                            </div>

                                            <!-- Operations Menu (Hidden by default) -->
                                            <div id="operations-{{ $wallet->id }}" class="operations-menu" style="display: none;">
                                                <div class="card shadow-sm">
                                                    <div class="card-body p-2">
                                                        <div class="d-flex flex-column">
                                                            <a href="{{ route('admin.users.show', $wallet->user_id) }}" class="btn btn-sm btn-outline-primary mb-1">
                                                                <i class="fas fa-user"></i> مشاهده کاربر
                                                            </a>
                                                            <button class="btn btn-sm btn-outline-info mb-1" onclick="viewTransactions({{ $wallet->user_id }}, '{{ $wallet->coin_type }}', '{{ $wallet->address }}')">
                                                                <i class="fas fa-exchange-alt"></i> مشاهده تراکنش‌ها
                                                            </button>
                                                            <button class="btn btn-sm btn-outline-success mb-1" onclick="checkBalance('{{ $wallet->address }}', '{{ $wallet->coin_type }}', {{ $wallet->network_id ?? 'null' }})">
                                                                <i class="fas fa-coins"></i> بررسی موجودی
                                                            </button>
                                                            <hr class="my-1">
                                                            <button class="btn btn-sm btn-outline-secondary mb-1" onclick="exportWalletInfo({{ $wallet->id }})">
                                                                <i class="fas fa-download"></i> دانلود اطلاعات
                                                            </button>
                                                            @if(!str_contains($wallet->memo ?? '', 'BLOCKED'))
                                                                <button class="btn btn-sm btn-outline-warning" onclick="blockWallet({{ $wallet->id }})">
                                                                    <i class="fas fa-ban"></i> مسدود کردن
                                                                </button>
                                                            @else
                                                                <button class="btn btn-sm btn-outline-success" onclick="unblockWallet({{ $wallet->id }})">
                                                                    <i class="fas fa-unlock"></i> رفع مسدودیت
                                                                </button>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="alert alert-info mb-0">
                                                هیچ کیف پولی یافت نشد.
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <small class="text-muted">
                                نمایش {{ $wallets->firstItem() ?? 0 }} تا {{ $wallets->lastItem() ?? 0 }} از {{ $wallets->total() }} نتیجه
                            </small>
                        </div>
                        <div>
                            {{ $wallets->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<!-- Transaction Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1" role="dialog" aria-labelledby="transactionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transactionModalLabel">تراکنش‌های کیف پول</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="transactionContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">در حال بارگذاری...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Balance Check Modal -->
<div class="modal fade" id="balanceModal" tabindex="-1" role="dialog" aria-labelledby="balanceModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="balanceModalLabel">بررسی موجودی</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="balanceContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">در حال بررسی موجودی...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        toastr.success('آدرس کپی شد');
    }, function(err) {
        console.error('خطا در کپی کردن: ', err);
        toastr.error('خطا در کپی کردن آدرس');
    });
}

function toggleOperations(walletId) {
    // Hide all other operation menus
    $('.operations-menu').not('#operations-' + walletId).hide();

    // Toggle current menu
    $('#operations-' + walletId).toggle();
}

// Close operations menu when clicking outside
$(document).on('click', function(e) {
    if (!$(e.target).closest('.btn-group, .operations-menu').length) {
        $('.operations-menu').hide();
    }
});

function viewTransactions(userId, coinType, address) {
    $('#transactionModal').modal('show');
    $('#transactionContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">در حال بارگذاری تراکنش‌ها...</p>
        </div>
    `);

    $.ajax({
        url: '{{ route("admin.system-wallets.user-wallets") }}',
        type: 'POST',
        data: {
            action: 'get_transactions',
            user_id: userId,
            coin_type: coinType,
            address: address,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                let transactionsHtml = `
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>نوع</th>
                                    <th>مقدار</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ</th>
                                    <th>توضیحات</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                if (response.data.length > 0) {
                    response.data.forEach(function(transaction) {
                        let statusBadge = transaction.status === 'done' ? 'success' :
                                        transaction.status === 'pending' ? 'warning' : 'danger';
                        let statusText = transaction.status === 'done' ? 'تکمیل شده' :
                                       transaction.status === 'pending' ? 'در انتظار' : 'رد شده';

                        transactionsHtml += `
                            <tr>
                                <td><span class="badge badge-${transaction.type === 'deposit' ? 'success' : 'info'}">${transaction.type === 'deposit' ? 'واریز' : 'برداشت'}</span></td>
                                <td>${parseFloat(transaction.amount).toFixed(8)} ${coinType}</td>
                                <td><span class="badge badge-${statusBadge}">${statusText}</span></td>
                                <td>${transaction.created_at}</td>
                                <td>${transaction.description || '-'}</td>
                            </tr>
                        `;
                    });
                } else {
                    transactionsHtml += `
                        <tr>
                            <td colspan="5" class="text-center">هیچ تراکنشی یافت نشد</td>
                        </tr>
                    `;
                }

                transactionsHtml += `
                            </tbody>
                        </table>
                    </div>
                `;

                $('#transactionContent').html(transactionsHtml);
            } else {
                $('#transactionContent').html(`
                    <div class="alert alert-danger">
                        خطا در بارگذاری تراکنش‌ها: ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#transactionContent').html(`
                <div class="alert alert-danger">
                    خطا در ارتباط با سرور
                </div>
            `);
        }
    });
}

function checkBalance(address, coinType, networkId) {
    $('#balanceModal').modal('show');
    $('#balanceContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">در حال بررسی موجودی...</p>
        </div>
    `);

    $.ajax({
        url: '{{ route("admin.system-wallets.user-wallets") }}',
        type: 'POST',
        data: {
            action: 'check_balance',
            address: address,
            coin_type: coinType,
            network_id: networkId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                $('#balanceContent').html(`
                    <div class="card">
                        <div class="card-body text-center">
                            <h4 class="text-primary">${response.balance} ${coinType}</h4>
                            <p class="text-muted">موجودی فعلی</p>
                            <hr>
                            <small class="text-muted">آدرس: ${address}</small>
                        </div>
                    </div>
                `);
            } else {
                $('#balanceContent').html(`
                    <div class="alert alert-warning">
                        ${response.message}
                    </div>
                `);
            }
        },
        error: function() {
            $('#balanceContent').html(`
                <div class="alert alert-danger">
                    خطا در بررسی موجودی
                </div>
            `);
        }
    });
}

function exportWalletInfo(walletId) {
    if (confirm('آیا می‌خواهید اطلاعات این کیف پول را دانلود کنید؟')) {
        window.location.href = '{{ route("admin.system-wallets.user-wallets") }}?action=export&wallet_id=' + walletId;
        toastr.success('دانلود شروع شد');
    }
}

function blockWallet(walletId) {
    if (confirm('آیا مطمئن هستید که می‌خواهید این آدرس کیف پول را مسدود کنید؟')) {
        $.ajax({
            url: '{{ route("admin.system-wallets.user-wallets") }}',
            type: 'POST',
            data: {
                action: 'block_wallet',
                wallet_id: walletId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('خطا در مسدود کردن آدرس');
            }
        });
    }
}

function unblockWallet(walletId) {
    if (confirm('آیا مطمئن هستید که می‌خواهید مسدودیت این آدرس کیف پول را رفع کنید؟')) {
        $.ajax({
            url: '{{ route("admin.system-wallets.user-wallets") }}',
            type: 'POST',
            data: {
                action: 'unblock_wallet',
                wallet_id: walletId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('خطا در رفع مسدودیت آدرس');
            }
        });
    }
}

$(document).ready(function() {
    // Auto-submit form on select change
    $('#coin_type, #network_id').on('change', function() {
        $(this).closest('form').submit();
    });

    // Initialize tooltips if needed
    $('[data-toggle="tooltip"]').tooltip();

    // Close operations menu when clicking on table
    $('.table').on('click', function(e) {
        if (!$(e.target).closest('.btn-group, .operations-menu').length) {
            $('.operations-menu').hide();
        }
    });
});

function exportAllWallets() {
    if (confirm('آیا می‌خواهید تمام کیف پول‌ها را دانلود کنید؟')) {
        let params = new URLSearchParams(window.location.search);
        params.set('action', 'export_all');
        window.location.href = '{{ route("admin.system-wallets.user-wallets") }}?' + params.toString();
        toastr.success('دانلود شروع شد');
    }
}
</script>
@endpush
