@extends('admin.layouts.app')
@section('title', 'لیست واریزهای شناسه‌دار در انتظار لیبل')

@section('content')
    <h1 style="font-size:1.5rem; font-weight:bold; margin-bottom:1.5rem;">لیست واریزهای شناسه‌دار در انتظار لیبل</h1>
    <div style="background:#f0f6ff; border:1.5px solid #3699FF; border-radius:8px; padding:1.2rem 1.5rem; margin-bottom:1.5rem; color:#222;">
        <b style="color:#3699FF; font-size:1.1em;">راهنمای عملیات تایید و رد تراکنش شناسه‌دار:</b>
        <ul style="margin-top:0.7rem; margin-bottom:0.7rem; padding-right:1.2rem;">
            <li><b>تایید تراکنش:</b> با زدن دکمه <span style="color:#27ae60; font-weight:bold;">تایید</span>، وضعیت تراکنش در سرویس جیبیت به <b>TO_BE_DECIDED</b> تغییر می‌کند و تراکنش به عنوان تاییدشده ثبت می‌شود.</li>
            <li><b>رد تراکنش:</b> با زدن دکمه <span style="color:#e74c3c; font-weight:bold;">رد</span>، وضعیت تراکنش به <b>Failed</b> تغییر می‌کند و این تراکنش دیگر در لیست تاییدها نمایش داده نخواهد شد.</li>
            <li><b>نکته:</b> هر تراکنش فقط یک بار قابل تایید یا رد است و پس از انجام عملیات، دیگر امکان تغییر وضعیت وجود ندارد.</li>
            <li>شرح کامل هر تراکنش را با دکمه <span style="color:#3699FF; font-weight:bold;">نمایش</span> می‌توانید مشاهده کنید.</li>
        </ul>
        <div style="font-size:0.98em; color:#888; margin-top:0.5rem;">
            این عملیات مطابق مستندات رسمی جیبیت انجام می‌شود و وضعیت نهایی تراکنش‌ها در سامانه جیبیت نیز قابل پیگیری است.
        </div>
    </div>
    <form method="get" class="mb-3" style="background:#f8f9fa; padding:1rem; border-radius:8px; margin-bottom:2rem;">
        <div style="margin-bottom: 10px;">
            <label style="font-weight:bold;">IBAN (ثابت):</label>
            <span style="direction:ltr; background:#f5f5f5; padding:3px 8px; border-radius:4px; font-family:monospace;">{{ $iban }}</span>
        </div>
        <input type="text" name="referenceNumber" value="{{ $referenceNumber }}" placeholder="Reference Number" style="padding:6px 10px; border-radius:4px; border:1px solid #ccc; margin-left:10px;" />
        <button type="submit" style="padding:6px 18px; border-radius:4px; background:#3699FF; color:#fff; border:none; font-weight:bold;">جستجو</button>
    </form>
    <div id="msg-box" style="display:none; margin-bottom:1rem;"></div>
    <div style="overflow-x:auto;">
        <table class="table" style="min-width:1000px; border-radius:8px; overflow:hidden; box-shadow:0 2px 8px #eee;">
            <thead style="background:#1b1b28; color:#fff; position:sticky; top:0;">
                <tr>
                    <th style="padding:10px;">#</th>
                    <th style="padding:10px;">referenceNumber</th>
                    <th style="padding:10px;">bankReferenceNumber</th>
                    <th style="padding:10px;">مبلغ واریز</th>
                    <th style="padding:10px;">تاریخ</th>
                    <th style="padding:10px;">شرح بانک</th>
                    <th style="padding:10px;">کپی</th>
                    <th style="padding:10px;">عملیات</th>
                </tr>
            </thead>
            <tbody>
                @forelse($elements as $i => $item)
                    <tr id="row-{{ $item['referenceNumber'] }}" style="background:{{ $i%2==0 ? '#f9fafd' : '#f1f3f9' }};">
                        <td style="text-align:center;">{{ $i+1 }}</td>
                        <td style="font-family:monospace; direction:ltr;">{{ $item['referenceNumber'] }}</td>
                        <td style="font-family:monospace; direction:ltr;">{{ $item['bankReferenceNumber'] }}</td>
                        <td style="color:#27ae60; font-weight:bold;">{{ number_format($item['creditAmount']) }} <span style="font-size:0.9em; color:#888;">ریال</span></td>
                        <td>{{ \Morilog\Jalali\Jalalian::fromDateTime($item['timestamp'])->format('Y/m/d H:i') }}</td>
                        <td style="max-width:120px; white-space:nowrap; overflow-x:auto;">
                            <button type="button" class="btn btn-sm btn-info" style="background:#3699FF; color:#fff; border:none; border-radius:4px; padding:2px 10px; font-size:0.95em;" onclick="showBankDesc({{ $i }})">نمایش</button>
                        </td>
                        <td>
                            <button onclick="navigator.clipboard.writeText('{{ $item['referenceNumber'] }}')" title="کپی referenceNumber" style="border:none; background:transparent; cursor:pointer; color:#3699FF; font-size:1.2em;">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-success approve-btn" data-ref="{{ $item['referenceNumber'] }}" style="padding:4px 14px; border-radius:4px; font-weight:bold; margin-left:4px; background:#27ae60; border:none; color:#fff;">تایید</button>
                            <button class="btn btn-danger reject-btn" data-ref="{{ $item['referenceNumber'] }}" style="padding:4px 14px; border-radius:4px; font-weight:bold; background:#e74c3c; border:none; color:#fff;">رد</button>
                        </td>
                    </tr>
                    <tr id="desc-row-{{ $i }}" style="display:none; background:#f0f6ff;">
                        <td colspan="8" style="padding:1rem 2rem; color:#222; font-size:1.05em;">
                            <b>شرح بانک:</b><br>
                            <span style="font-family:tahoma,monospace;">{{ $item['bankDescription'] }}</span>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="8" style="text-align:center; color:#888; padding:2rem;">هیچ واریزی در انتظار لیبل یافت نشد.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    <div style="margin-top:1.5rem;">
        <span style="font-weight:bold;">صفحه:</span> {{ $pagination['pageNumber'] }}
        <span style="margin-right:20px; font-weight:bold;">تعداد:</span> {{ $pagination['numberOfElements'] }}
    </div>

    <script>
        function showBankDesc(idx) {
            var row = document.getElementById('desc-row-' + idx);
            if(row.style.display === 'none') {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }

        function showMsg(msg, type = 'success') {
            var box = document.getElementById('msg-box');
            box.innerHTML = msg;
            box.style.display = 'block';
            box.style.background = type === 'success' ? '#eafaf1' : '#fdeaea';
            box.style.color = type === 'success' ? '#27ae60' : '#e74c3c';
            box.style.border = '1px solid ' + (type === 'success' ? '#27ae60' : '#e74c3c');
            box.style.padding = '10px 18px';
            box.style.borderRadius = '6px';
            box.style.fontWeight = 'bold';
            box.style.transition = 'all 0.5s';
            setTimeout(function(){ box.style.opacity = 0; }, 2500);
            setTimeout(function(){ box.style.display = 'none'; box.style.opacity = 1; }, 3000);
        }

        document.querySelectorAll('.approve-btn').forEach(function(btn){
            btn.addEventListener('click', function(){
                var ref = this.getAttribute('data-ref');
                var row = document.getElementById('row-' + ref);
                this.disabled = true;
                this.innerHTML = '...';
                fetch("{{ route('admin.jibit.approve_aug_transaction') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({referenceNumber: ref})
                })
                .then(res => res.json())
                .then(data => {
                    if(data.success){
                        row.style.background = '#eafaf1';
                        setTimeout(function(){ row.remove(); }, 700);
                        showMsg('تراکنش با موفقیت تایید شد.','success');
                    }else{
                        showMsg(data.message || 'خطا در تایید تراکنش','error');
                        btn.disabled = false;
                        btn.innerHTML = 'تایید';
                    }
                })
                .catch(()=>{
                    showMsg('خطا در ارتباط با سرور','error');
                    btn.disabled = false;
                    btn.innerHTML = 'تایید';
                });
            });
        });
        document.querySelectorAll('.reject-btn').forEach(function(btn){
            btn.addEventListener('click', function(){
                var ref = this.getAttribute('data-ref');
                var row = document.getElementById('row-' + ref);
                this.disabled = true;
                this.innerHTML = '...';
                fetch("{{ route('admin.jibit.reject_aug_transaction') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({referenceNumber: ref})
                })
                .then(res => res.json())
                .then(data => {
                    if(data.success){
                        row.style.background = '#fdeaea';
                        setTimeout(function(){ row.remove(); }, 700);
                        showMsg('تراکنش با موفقیت رد شد.','error');
                    }else{
                        showMsg(data.message || 'خطا در رد تراکنش','error');
                        btn.disabled = false;
                        btn.innerHTML = 'رد';
                    }
                })
                .catch(()=>{
                    showMsg('خطا در ارتباط با سرور','error');
                    btn.disabled = false;
                    btn.innerHTML = 'رد';
                });
            });
        });
    </script>
@endsection 