@extends('admin.layouts.app')
@section('title', 'اطلاعات حساب‌های بانکی جیبیت')

@section('content')
    <h1 style="font-size:1.5rem; font-weight:bold; margin-bottom:1.5rem;">اطلاعات حساب‌های بانکی جیبیت</h1>
    <div style="background:#f0f6ff; border:1.5px solid #3699FF; border-radius:8px; padding:1.2rem 1.5rem; margin-bottom:1.5rem; color:#222;">
        <b style="color:#3699FF; font-size:1.1em;">راهنما:</b>
        <ul style="margin-top:0.7rem; margin-bottom:0.7rem; padding-right:1.2rem;">
            <li>در این صفحه اطلاعات حساب‌های بانکی متصل به جیبیت را مشاهده می‌کنید.</li>
            <li>برای مشاهده جزئیات بیشتر هر حساب، روی دکمه <span style="color:#3699FF; font-weight:bold;">جزئیات</span> کلیک کنید.</li>
        </ul>
    </div>
    <div class="bank-accounts-grid">
        @forelse($accounts as $acc)
            <div class="bank-account-card">
                <div class="card-header">
                    <span class="bank-name">بانک {{ $acc['bank'] }}</span>
                    <span class="status {{ $acc['active'] ? 'active' : 'inactive' }}">
                        {{ $acc['active'] ? 'فعال' : 'غیرفعال' }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="info-row"><b>شماره حساب:</b> <span class="mono">{{ $acc['accountNumber'] }}</span></div>
                    <div class="info-row"><b>IBAN:</b> <span class="mono">{{ $acc['iban'] }}</span></div>
                    <div class="info-row"><b>صاحب حساب:</b> {{ $acc['ownerFirstName'] }} {{ $acc['ownerLastName'] }}</div>
                    <div class="info-row"><b>تاریخ ایجاد:</b> {{ \Morilog\Jalali\Jalalian::fromDateTime($acc['createdAt'])->format('Y/m/d H:i') }}</div>
                </div>
                <div class="card-footer">
                    <!-- حذف دکمه جزئیات -->
                </div>
                <!-- طراحی مدرن کارت حساب بانکی -->
                <div class="modern-details">
                    <div class="main-info">
                        <div class="bank-logo-circle">
                            <span class="material-icons" style="font-size:2.1em; color:#3699FF;">account_balance</span>
                        </div>
                        <div class="main-info-text">
                            <div class="bank-title">بانک {{ $acc['bank'] }}</div>
                            <div class="account-row"><span>شماره حساب:</span> <span class="mono">{{ $acc['accountNumber'] }}</span></div>
                            <div class="account-row"><span>IBAN:</span> <span class="mono">{{ $acc['iban'] }}</span></div>
                            <div class="account-row"><span>صاحب حساب:</span> {{ $acc['ownerFirstName'] }} {{ $acc['ownerLastName'] }}</div>
                        </div>
                        <div class="status-badge">
                            <span class="status-dot {{ $acc['active'] ? 'active' : 'inactive' }}"></span>
                            <span class="status-label">{{ $acc['active'] ? 'فعال' : 'غیرفعال' }}</span>
                        </div>
                    </div>
                    <div class="details-table">
                        <div class="details-row"><span>کد مشتری:</span><span class="mono">{{ $acc['cif'] }}</span></div>
                        <div class="details-row"><span>ClientCode:</span><span class="mono">{{ $acc['clientCode'] }}</span></div>
                        <div class="details-row"><span>MerchantId:</span><span class="mono">{{ $acc['merchantId'] }}</span></div>
                        <div class="details-row"><span>سیستم IBAN:</span><span class="mono">{{ $acc['systemAccountIban'] }}</span></div>
                        <div class="details-row"><span>توضیحات:</span><span>{{ $acc['description'] }}</span></div>
                        <div class="details-row"><span>تاریخ ایجاد:</span><span>{{ \Morilog\Jalali\Jalalian::fromDateTime($acc['createdAt'])->format('Y/m/d H:i') }}</span></div>
                        <div class="details-row"><span>تاریخ بروزرسانی:</span><span>{{ \Morilog\Jalali\Jalalian::fromDateTime($acc['updatedAt'])->format('Y/m/d H:i') }}</span></div>
                        <div class="details-row"><span>مانده دستی فعال:</span><span class="{{ $acc['manualBalanceActive'] ? 'active' : 'inactive' }}">{{ $acc['manualBalanceActive'] ? 'فعال' : 'غیرفعال' }}</span></div>
                        <div class="details-row"><span>مانده خودکار فعال:</span><span class="{{ $acc['autoBalanceActive'] ? 'active' : 'inactive' }}">{{ $acc['autoBalanceActive'] ? 'فعال' : 'غیرفعال' }}</span></div>
                        <div class="details-row"><span>تسویه فعال:</span><span class="{{ $acc['settlementActive'] ? 'active' : 'inactive' }}">{{ $acc['settlementActive'] ? 'فعال' : 'غیرفعال' }}</span></div>
                        <div class="details-row"><span>بیانیه افزوده فعال:</span><span class="{{ $acc['augmentedStatementActive'] ? 'active' : 'inactive' }}">{{ $acc['augmentedStatementActive'] ? 'فعال' : 'غیرفعال' }}</span></div>
                        <div class="details-row"><span>انتقال عادی فعال:</span><span class="{{ $acc['normalTransferActive'] ? 'active' : 'inactive' }}">{{ $acc['normalTransferActive'] ? 'فعال' : 'غیرفعال' }}</span></div>
                        <!-- اگر فیلد دیگری مهم است اضافه کن -->
                    </div>
                </div>
            </div>
        @empty
            <div style="text-align:center; color:#888; padding:2rem;">هیچ حسابی یافت نشد.</div>
        @endforelse
    </div>
    <style>
        .bank-accounts-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: flex-start;
        }
        .bank-account-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px #e3e8f0;
            width: 680px;
            min-width: 340px;
            max-width: 100%;
            padding: 0;
            margin-bottom: 1rem;
            display: flex;
            flex-direction: column;
            transition: box-shadow 0.2s;
            border: 1.5px solid #e3e8f0;
        }
        .bank-account-card:hover {
            box-shadow: 0 4px 24px #b3c6e0;
            border-color: #3699FF;
        }
        .card-header {
            background: #f0f6ff;
            border-radius: 12px 12px 0 0;
            padding: 1rem 1.2rem 0.7rem 1.2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1em;
            font-weight: bold;
        }
        .bank-name { color: #3699FF; }
        .status.active { color: #27ae60; }
        .status.inactive { color: #e74c3c; }
        .card-body {
            padding: 1rem 1.2rem 0.5rem 1.2rem;
            font-size: 1em;
        }
        .info-row { margin-bottom: 0.4rem; }
        .mono { font-family: monospace; direction: ltr; }
        .card-footer {
            padding: 0.7rem 1.2rem 0.7rem 1.2rem;
            border-top: 1px solid #f0f6ff;
            display: flex;
            justify-content: flex-end;
        }
        .details-btn {
            background: #3699FF;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 4px 16px;
            font-size: 0.98em;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.2s;
        }
        .details-btn:hover { background: #2176d2; }
        .modern-details {
            background: linear-gradient(120deg, #f0f6ff 60%, #e3e8f0 100%);
            border-radius: 14px;
            box-shadow: 0 4px 24px #dbeafe99;
            padding: 1.2rem 1.5rem 1.5rem 1.5rem;
            margin: 1rem 0 0.5rem 0;
        }
        .main-info {
            display: flex;
            align-items: center;
            gap: 1.2rem;
            margin-bottom: 1.1rem;
        }
        .bank-logo-circle {
            width: 54px;
            height: 54px;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 8px #b3c6e033;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .main-info-text {
            flex: 1;
        }
        .bank-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #3699FF;
            margin-bottom: 0.2em;
        }
        .account-row {
            font-size: 1em;
            margin-bottom: 0.1em;
            color: #333;
        }
        .status-badge {
            display: flex;
            align-items: center;
            gap: 0.4em;
        }
        .status-dot {
            width: 13px;
            height: 13px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 2px;
        }
        .status-dot.active { background: #27ae60; }
        .status-dot.inactive { background: #e74c3c; }
        .status-label {
            font-weight: bold;
            color: #444;
            font-size: 1em;
        }
        .details-table {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.3rem 1.2rem;
            background: #fff;
            border-radius: 10px;
            padding: 1.1rem 1.2rem 0.7rem 1.2rem;
            box-shadow: 0 1px 6px #e3e8f0;
        }
        .details-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.98em;
            color: #222;
            border-bottom: 1px solid #f0f6ff;
            padding: 4px 0;
        }
        .details-row:last-child { border-bottom: none; }
        .details-row span:first-child {
            color: #888;
            font-weight: 500;
            margin-left: 0.5em;
        }
        .details-row .active { color: #27ae60; font-weight: bold; }
        .details-row .inactive { color: #e74c3c; font-weight: bold; }
        @media (max-width: 900px) {
            .bank-accounts-grid { flex-direction: column; gap: 1rem; }
            .bank-account-card { width: 100%; min-width: 0; }
            .details-table { grid-template-columns: 1fr; }
            .main-info { flex-direction: column; align-items: flex-start; gap: 0.7rem; }
        }
    </style>
    <!-- آیکون متریال برای بانک -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <script>
        // حذف تابع toggleDetails چون دیگر نیازی نیست
    </script>
@endsection 