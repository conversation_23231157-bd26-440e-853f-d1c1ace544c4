@extends('admin.layouts.app')

@section('content')
    <h1>زیرمجموعه‌های {{ $user->firstname }} {{ $user->lastname }}</h1>
    <table class="table">
        <thead>
            <tr>
                <th>کاربر دعوت‌شده</th>
                <th>تاریخ عضویت</th>
                <th>تعداد پاداش‌ها</th>
                <th>مجموع پاداش (تومان)</th>
            </tr>
        </thead>
        <tbody>
            @foreach($referrals as $referral)
                <tr>
                    <td>{{ $referral->invitedUser->firstname ?? '-' }}</td>
                    <td>{{ jdate($referral->created_at)->format('Y/m/d') }}</td>
                    <td>{{ $referral->rewards->count() }}</td>
                    <td>{{ number_format($referral->rewards->sum('amount_toman')) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection 