@extends('admin.layouts.app')

@section('title', 'داشبورد')

@section('breadcrumb')
    <li class="breadcrumb-item active">داشبورد</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title h3">داشبورد مدیریت</h1>
            <p class="text-muted">خلاصه وضعیت سیستم و آمار کلی</p>
        </div>
    </div>

    <!-- کارت‌های آماری مدرن و خفن -->
    <div class="row mb-4">
        <!-- تعداد کاربران -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-modern shadow-sm border-0">
                <div class="card-body d-flex align-items-center">
                    <div class="stat-modern-icon me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <div class="stat-modern-value">{{ number_format($countUsers) }}</div>
                        <div class="stat-modern-label">تعداد کاربران</div>
                    </div>
                    <div class="ms-auto stat-modern-change text-success" data-bs-toggle="tooltip" title="رشد نسبت به دیروز">
                        <i class="fas fa-arrow-up"></i> 5%
                    </div>
                </div>
            </div>
        </div>
        <!-- مجموع دارایی تومانی کاربران -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-modern shadow-sm border-0">
                <div class="card-body d-flex align-items-center">
                    <div class="stat-modern-icon me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <div class="stat-modern-value">{{ number_format($TomanBalances ?? 0) }}</div>
                        <div class="stat-modern-label">مجموع دارایی تومانی</div>
                    </div>
                    <div class="ms-auto stat-modern-change text-danger" data-bs-toggle="tooltip" title="کاهش نسبت به دیروز">
                        <i class="fas fa-arrow-down"></i> 2%
                    </div>
                </div>
            </div>
        </div>
        <!-- تراکنش‌های موفق امروز -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-modern shadow-sm border-0">
                <div class="card-body d-flex align-items-center">
                    <div class="stat-modern-icon me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <div class="stat-modern-value">{{ number_format($data['successful_transactions'] ?? 0) }}</div>
                        <div class="stat-modern-label">تراکنش موفق امروز</div>
                    </div>
                    <div class="ms-auto stat-modern-change text-success" data-bs-toggle="tooltip" title="رشد نسبت به دیروز">
                        <i class="fas fa-arrow-up"></i> 3%
                    </div>
                </div>
            </div>
        </div>
        <!-- درآمد ماهانه -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-modern shadow-sm border-0">
                <div class="card-body d-flex align-items-center">
                    <div class="stat-modern-icon me-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <div class="stat-modern-value">{{ number_format($data['monthly_revenue'] ?? 0) }}</div>
                        <div class="stat-modern-label">درآمد ماهانه</div>
                    </div>
                    <div class="ms-auto stat-modern-change text-success" data-bs-toggle="tooltip" title="رشد نسبت به دیروز">
                        <i class="fas fa-arrow-up"></i> 7%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- کارت‌های آماری تکمیلی -->
    <div class="row mb-4">
        <!-- کاربران جدید امروز -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary-subtle rounded-3 p-3">
                                <i class="fas fa-user-plus text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">کاربران جدید</h6>
                            <h4 class="mb-0 text-primary">{{ $data['new_users_today'] ?? 0 }}</h4>
                            <small class="text-muted">امروز</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مجموع موجودی -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success-subtle rounded-3 p-3">
                                <i class="fas fa-wallet text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">مجموع واریزی امروز</h6>
                            <h4 class="mb-0 text-success">0</h4>
                            <small class="text-muted">تومان</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تراکنش‌های در انتظار -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning-subtle rounded-3 p-3">
                                <i class="fas fa-clock text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">در انتظار تایید</h6>
                            <h4 class="mb-0 text-warning">{{ $Toman_Withdrawals ?? 0 }}</h4>
                            <small class="text-muted">برداشت تومانی</small>
                    <a href="{{ route('admin.toman-withdrawal.pending') }}" class="btn btn-sm btn-outline-primary">عملیات</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نرخ رشد -->
        <!-- ویجت قیمت دلار سایت (جدید و خفن) -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-lg dollar-widget" style="background: linear-gradient(135deg, #232526 0%, #414345 100%); color: #fff; border-radius: 18px; overflow: hidden; position: relative;">
                <div class="card-body p-0">
                    <div class="d-flex flex-column align-items-center justify-content-center py-4 position-relative">
                        <div class="d-flex align-items-center gap-3 mb-3">
                            <div class="dollar-icon bg-white bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 56px; height: 56px;">
                                <i class="fas fa-dollar-sign fa-2x text-success"></i>
                            </div>
                            <div>
                                <div class="fw-bold" style="font-size: 1.15rem; letter-spacing: 1px;">قیمت دلار سایت</div>
                                <div class="d-flex align-items-center gap-2 mt-1">
                                    <span class="badge bg-success bg-opacity-75 px-3 py-2 fs-6">
                                        <i class="fas fa-arrow-down me-1"></i> خرید
                                    </span>
                                    <span class="badge bg-danger bg-opacity-75 px-3 py-2 fs-6">
                                        <i class="fas fa-arrow-up me-1"></i> فروش
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex flex-column align-items-center w-100">
                            <div class="d-flex justify-content-between w-100 px-3">
                                <div class="text-center">
                                    <div class="text-success fw-bold" style="font-size: 1.35rem;">
                                        {{ number_format($UsdPrice->buy_price) }}
                                    </div>
                                    <div class="small text-white-50">خرید (تومان)</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-danger fw-bold" style="font-size: 1.35rem;">
                                        {{ number_format($UsdPrice->sell_price) }}
                                    </div>
                                    <div class="small text-white-50">فروش (تومان)</div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center justify-content-center gap-2 mt-3">
                                <span class="badge bg-gradient bg-success bg-opacity-75 px-2 py-1">
                                    <i class="fas fa-arrow-up"></i> {{ $UsdPrice->change > 0 ? '+' : '' }}{{ $UsdPrice->change }}
                                </span>
                                <span class="small text-white-50">تغییر امروز</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dollar-bg-glow position-absolute top-0 start-0 w-100 h-100" style="pointer-events:none;"></div>
            </div>
        </div>
    </div>

    <!-- آمار روزانه خرید و فروش -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-white">
                            <i class="fas fa-chart-line me-2"></i>
                            آمار روزانه خرید و فروش - {{ $dailyTradingStats['date'] }}
                        </h5>
                        <div>
                            <button class="btn btn-light btn-sm" onclick="refreshTradingStats()" id="refresh-stats-btn">
                                <i class="fas fa-sync-alt me-1"></i>
                                به‌روزرسانی
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- مجموع خرید -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-primary-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-arrow-down text-primary fa-2x"></i>
                                </div>
                                <h4 class="text-primary mb-1">{{ $dailyTradingStats['formatted_total_buy'] }}</h4>
                                <p class="text-muted mb-2">مجموع خرید امروز</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-primary">{{ $dailyTradingStats['transaction_count']['buy'] }} تراکنش</span>
                                </div>
                            </div>
                        </div>

                        <!-- مجموع فروش -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-success-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-arrow-up text-success fa-2x"></i>
                                </div>
                                <h4 class="text-success mb-1">{{ $dailyTradingStats['formatted_total_sell'] }}</h4>
                                <p class="text-muted mb-2">مجموع فروش امروز</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-success">{{ $dailyTradingStats['transaction_count']['sell'] }} تراکنش</span>
                                </div>
                            </div>
                        </div>

                        <!-- جریان نقدی -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card {{ $dailyTradingStats['net_flow'] >= 0 ? 'bg-info-subtle' : 'bg-warning-subtle' }} rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-exchange-alt {{ $dailyTradingStats['net_flow'] >= 0 ? 'text-info' : 'text-warning' }} fa-2x"></i>
                                </div>
                                <h4 class="{{ $dailyTradingStats['net_flow'] >= 0 ? 'text-info' : 'text-warning' }} mb-1">{{ $dailyTradingStats['formatted_net_flow'] }}</h4>
                                <p class="text-muted mb-2">جریان نقدی خالص</p>
                                <div class="trading-stat-detail">
                                    <span class="badge {{ $dailyTradingStats['net_flow'] >= 0 ? 'bg-info' : 'bg-warning' }}">
                                        {{ $dailyTradingStats['net_flow'] >= 0 ? 'ورودی' : 'خروجی' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- سود -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card {{ $dailyTradingStats['profit'] >= 0 ? 'bg-success-subtle' : 'bg-danger-subtle' }} rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-chart-line {{ $dailyTradingStats['profit'] >= 0 ? 'text-success' : 'text-danger' }} fa-2x"></i>
                                </div>
                                <h4 class="{{ $dailyTradingStats['profit'] >= 0 ? 'text-success' : 'text-danger' }} mb-1">{{ $dailyTradingStats['formatted_profit'] }}</h4>
                                <p class="text-muted mb-2">سود امروز (تومان)</p>
                                <div class="trading-stat-detail">
                                    <span class="badge {{ $dailyTradingStats['profit'] >= 0 ? 'bg-success' : 'bg-danger' }} mb-1">
                                        {{ $dailyTradingStats['profit_percentage'] }}%
                                    </span>
                                    @if(isset($dailyTradingStats['profit_usd']))
                                    <div class="small text-muted mt-1">
                                        {{ number_format($dailyTradingStats['profit_usd'], 2) }} دلار
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آمار تفصیلی به تفکیک ارز -->
                    @if(count($dailyTradingStats['currency_stats']) > 0)
                    <div class="mt-4">
                        <h6 class="mb-3">آمار تفصیلی به تفکیک ارز</h6>
                        
                        <!-- نمایش قیمت فعلی دلار -->
                        <div class="alert alert-info mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>قیمت فعلی دلار (خرید):</strong> 
                                    <span class="badge bg-primary">{{ number_format($dailyTradingStats['current_usd_prices']['buy_price'] ?? 0) }} تومان</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>قیمت فعلی دلار (فروش):</strong> 
                                    <span class="badge bg-success">{{ number_format($dailyTradingStats['current_usd_prices']['sell_price'] ?? 0) }} تومان</span>
                                </div>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-info-circle me-1"></i>
                                سود دلاری بر اساس قیمت فعلی دلار محاسبه می‌شود
                            </small>
                        </div>
                        
                        <!-- نمایش قیمت دلار لحظه‌ای تراکنش‌ها -->
                        <div class="alert alert-info mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>میانگین قیمت دلار خرید:</strong> 
                                    <span class="badge bg-primary">{{ number_format($dailyTradingStats['currency_stats'][0]['average_buy_usd_rate'] ?? 0) }} تومان</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>میانگین قیمت دلار فروش:</strong> 
                                    <span class="badge bg-success">{{ number_format($dailyTradingStats['currency_stats'][0]['average_sell_usd_rate'] ?? 0) }} تومان</span>
                                </div>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                <i class="fas fa-info-circle me-1"></i>
                                قیمت دلار لحظه‌ای تراکنش‌ها (بر اساس جزئیات تراکنش)
                            </small>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ارز</th>
                                        <th class="text-center">حجم خرید</th>
                                        <th class="text-center">مبلغ خرید</th>
                                        <th class="text-center">میانگین قیمت خرید</th>
                                        <th class="text-center">میانگین قیمت دلار خرید</th>
                                        <th class="text-center">حجم فروش</th>
                                        <th class="text-center">مبلغ فروش</th>
                                        <th class="text-center">میانگین قیمت فروش</th>
                                        <th class="text-center">میانگین قیمت دلار فروش</th>
                                        <th class="text-center">سود/ضرر (تومان)</th>
                                        <th class="text-center">سود/ضرر (دلار)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($dailyTradingStats['currency_stats'] as $currency)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2">{{ $currency['symbol'] }}</span>
                                                {{ $currency['name'] }}
                                            </div>
                                        </td>
                                        <td class="text-center text-primary">{{ $currency['formatted_buy_volume'] }}</td>
                                        <td class="text-center text-primary">{{ $currency['formatted_buy_amount'] }}</td>
                                        <td class="text-center text-primary">{{ $currency['formatted_average_buy_price'] }}</td>
                                        <td class="text-center text-primary">{{ $currency['formatted_average_buy_usd_rate'] }}</td>
                                        <td class="text-center text-success">{{ $currency['formatted_sell_volume'] }}</td>
                                        <td class="text-center text-success">{{ $currency['formatted_sell_amount'] }}</td>
                                        <td class="text-center text-success">{{ $currency['formatted_average_sell_price'] }}</td>
                                        <td class="text-center text-success">{{ $currency['formatted_average_sell_usd_rate'] }}</td>
                                        <td class="text-center">
                                            <span class="badge {{ $currency['profit'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                                                {{ $currency['formatted_profit'] }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge {{ $currency['profit_usd'] >= 0 ? 'bg-success' : 'bg-danger' }}">
                                                {{ $currency['formatted_profit_usd'] }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @else
                    <div class="mt-4 text-center text-muted">
                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                        <p>هیچ تراکنشی برای امروز ثبت نشده است</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- آمار روزانه سواپ/تبدیل ارز -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-white">
                            <i class="fas fa-exchange-alt me-2"></i>
                            آمار روزانه سواپ/تبدیل ارز - {{ $dailyTradingStats['date'] }}
                        </h5>
                        <div>
                            <button class="btn btn-light btn-sm" onclick="refreshTradingStats()" id="refresh-swap-stats-btn">
                                <i class="fas fa-sync-alt me-1"></i>
                                به‌روزرسانی
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- تعداد سواپ‌ها -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-info-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-exchange-alt text-info fa-2x"></i>
                                </div>
                                <h4 class="text-info mb-1">{{ $dailyTradingStats['swap_stats']['formatted_total_swaps'] }}</h4>
                                <p class="text-muted mb-2">تعداد سواپ امروز</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-info">{{ $dailyTradingStats['transaction_count']['swap'] }} تراکنش</span>
                                </div>
                            </div>
                        </div>

                        <!-- حجم کل سواپ -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-warning-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-chart-pie text-warning fa-2x"></i>
                                </div>
                                <h4 class="text-warning mb-1">{{ $dailyTradingStats['swap_stats']['formatted_total_swap_volume'] }}</h4>
                                <p class="text-muted mb-2">حجم کل سواپ</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-warning">ارز دیجیتال</span>
                                </div>
                            </div>
                        </div>

                        <!-- کارمزد کل سواپ -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-success-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-coins text-success fa-2x"></i>
                                </div>
                                <h4 class="text-success mb-1">{{ $dailyTradingStats['swap_stats']['formatted_total_swap_fees'] }}</h4>
                                <p class="text-muted mb-2">کارمزد کل سواپ</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-success">درآمد سایت</span>
                                </div>
                            </div>
                        </div>

                        <!-- ارزش دلاری سواپ -->
                        <div class="col-xl-3 col-md-6">
                            <div class="trading-stat-card bg-primary-subtle rounded-3 p-4 text-center">
                                <div class="trading-stat-icon mb-3">
                                    <i class="fas fa-dollar-sign text-primary fa-2x"></i>
                                </div>
                                <h4 class="text-primary mb-1">{{ $dailyTradingStats['swap_stats']['formatted_total_swap_usd_value'] }}</h4>
                                <p class="text-muted mb-2">ارزش دلاری سواپ</p>
                                <div class="trading-stat-detail">
                                    <span class="badge bg-primary">USD</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آمار تفصیلی جفت ارزها -->
                    @if(count($dailyTradingStats['swap_stats']['swap_pairs']) > 0)
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line text-info me-2"></i>
                                آمار تفصیلی جفت ارزها
                            </h6>
                            <div class="d-flex gap-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    {{ count($dailyTradingStats['swap_stats']['swap_pairs']) }} جفت ارز
                                </span>
                                <span class="badge bg-success">
                                    <i class="fas fa-coins me-1"></i>
                                    {{ $dailyTradingStats['swap_stats']['formatted_total_swap_fees'] }} کارمزد
                                </span>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover swap-stats-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 25%;">
                                            <i class="fas fa-exchange-alt me-2"></i>
                                            جفت ارز
                                        </th>
                                        <th class="text-center" style="width: 15%;">
                                            <i class="fas fa-hashtag me-2"></i>
                                            تعداد سواپ
                                        </th>
                                        <th class="text-center" style="width: 20%;">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            حجم کل
                                        </th>
                                        <th class="text-center" style="width: 20%;">
                                            <i class="fas fa-coins me-2"></i>
                                            کارمزد کل
                                        </th>
                                        <th class="text-center" style="width: 20%;">
                                            <i class="fas fa-dollar-sign me-2"></i>
                                            ارزش دلاری
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($dailyTradingStats['swap_stats']['swap_pairs'] as $index => $pair)
                                    <tr class="swap-row" data-pair="{{ $pair['from_currency'] }}_{{ $pair['to_currency'] }}">
                                        <td>
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="currency-pair-display">
                                                    <div class="currency-badge from-currency">
                                                        <i class="fas fa-coins me-1"></i>
                                                        <span class="currency-symbol">{{ $pair['from_currency'] }}</span>
                                                    </div>
                                                    <div class="swap-arrow">
                                                        <i class="fas fa-arrow-right"></i>
                                                    </div>
                                                    <div class="currency-badge to-currency">
                                                        <i class="fas fa-coins me-1"></i>
                                                        <span class="currency-symbol">{{ $pair['to_currency'] }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="swap-count-badge">
                                                <span class="count-number">{{ $pair['count'] }}</span>
                                                <small class="count-label">سواپ</small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="volume-display">
                                                <div class="volume-value text-warning">
                                                    <i class="fas fa-chart-pie me-1"></i>
                                                    {{ $pair['formatted_total_volume'] }}
                                                </div>
                                                <div class="volume-bar">
                                                    <div class="volume-progress" style="width: {{ min(($pair['total_volume'] / max(array_column($dailyTradingStats['swap_stats']['swap_pairs'], 'total_volume'))) * 100, 100) }}%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="fee-display">
                                                <div class="fee-value text-success">
                                                    <i class="fas fa-coins me-1"></i>
                                                    {{ $pair['formatted_total_fees'] }}
                                                </div>
                                                <div class="fee-percentage">
                                                    <small class="text-muted">
                                                        {{ $pair['total_fees'] > 0 ? number_format(($pair['total_fees'] / $dailyTradingStats['swap_stats']['total_swap_fees']) * 100, 1) : 0 }}% از کل
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="usd-display">
                                                <div class="usd-value text-primary">
                                                    <i class="fas fa-dollar-sign me-1"></i>
                                                    {{ $pair['formatted_total_usd_value'] }}
                                                </div>
                                                <div class="usd-trend">
                                                    @if($index < 3)
                                                        <span class="badge bg-success badge-sm">
                                                            <i class="fas fa-trending-up me-1"></i>
                                                            محبوب
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- خلاصه آمار -->
                        <div class="row mt-4">
                            <div class="col-md-3">
                                <div class="summary-card bg-primary-subtle rounded-3 p-3 text-center">
                                    <div class="summary-icon mb-2">
                                        <i class="fas fa-exchange-alt text-primary fa-2x"></i>
                                    </div>
                                    <h6 class="text-primary mb-1">جفت ارز فعال</h6>
                                    <p class="mb-0 text-muted">{{ count($dailyTradingStats['swap_stats']['swap_pairs']) }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-card bg-success-subtle rounded-3 p-3 text-center">
                                    <div class="summary-icon mb-2">
                                        <i class="fas fa-chart-line text-success fa-2x"></i>
                                    </div>
                                    <h6 class="text-success mb-1">میانگین حجم</h6>
                                    <p class="mb-0 text-muted">{{ $dailyTradingStats['swap_stats']['total_swaps'] > 0 ? number_format($dailyTradingStats['swap_stats']['total_swap_volume'] / $dailyTradingStats['swap_stats']['total_swaps'], 8) : 0 }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-card bg-warning-subtle rounded-3 p-3 text-center">
                                    <div class="summary-icon mb-2">
                                        <i class="fas fa-percentage text-warning fa-2x"></i>
                                    </div>
                                    <h6 class="text-warning mb-1">میانگین کارمزد</h6>
                                    <p class="mb-0 text-muted">{{ $dailyTradingStats['swap_stats']['total_swaps'] > 0 ? number_format($dailyTradingStats['swap_stats']['total_swap_fees'] / $dailyTradingStats['swap_stats']['total_swaps'], 8) : 0 }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="summary-card bg-info-subtle rounded-3 p-3 text-center">
                                    <div class="summary-icon mb-2">
                                        <i class="fas fa-dollar-sign text-info fa-2x"></i>
                                    </div>
                                    <h6 class="text-info mb-1">میانگین ارزش</h6>
                                    <p class="mb-0 text-muted">${{ $dailyTradingStats['swap_stats']['total_swaps'] > 0 ? number_format($dailyTradingStats['swap_stats']['total_swap_usd_value'] / $dailyTradingStats['swap_stats']['total_swaps'], 2) : 0 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="mt-4 text-center text-muted empty-state">
                        <div class="empty-icon mb-3">
                            <i class="fas fa-exchange-alt fa-4x text-muted"></i>
                        </div>
                        <h5 class="text-muted mb-2">هیچ سواپی برای امروز انجام نشده است</h5>
                        <p class="text-muted mb-0">کاربران هنوز هیچ عملیات تبدیل ارزی انجام نداده‌اند</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- بخش جستجوی سریع کاربر -->
    <div class="row mb-4">
        <div class="col-12 text-end">
            <button class="btn btn-primary btn-lg" id="openQuickUserSearchModal">
                <i class="fas fa-search me-2"></i> جستجوی سریع کاربر
            </button>
        </div>
    </div>

    <!-- بخش دسترسی سریع -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-bolt me-2"></i>
                        دسترسی سریع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.users.create') }}" class="quick-action-btn" data-bs-toggle="tooltip" data-bs-placement="top" title="افزودن کاربر جدید">
                                <div class="quick-action-icon bg-primary-subtle bounce">
                                    <i class="fas fa-user-plus text-primary"></i>
                                </div>
                                <span>افزودن کاربر</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.transaction.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-success-subtle">
                                    <i class="fas fa-exchange-alt text-success"></i>
                                </div>
                                <span>مدیریت تراکنش</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.activities.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-info-subtle">
                                    <i class="fas fa-chart-bar text-info"></i>
                                </div>
                                <span>گزارشات فعالیت</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.settings.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-warning-subtle">
                                    <i class="fas fa-cog text-warning"></i>
                                </div>
                                <span>تنظیمات</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.documents.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-secondary-subtle">
                                    <i class="fas fa-file-text text-secondary"></i>
                                </div>
                                <span>مدیریت اسناد</span>
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-6">
                            <a href="{{ route('admin.support.index') }}" class="quick-action-btn">
                                <div class="quick-action-icon bg-danger-subtle">
                                    <i class="fas fa-headset text-danger"></i>
                                </div>
                                <span>پشتیبانی</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش آمار و نمودارها -->
    <div class="row mb-4">
        <!-- نمودار تراکنش‌ها -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آمار تراکنش‌ها</h5>
                    <div class="card-actions">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary active">روزانه</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">هفتگی</button>
                            <button type="button" class="btn btn-sm btn-outline-primary">ماهانه</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="transactions-chart" style="height: 350px;"></div>
                </div>
            </div>
        </div>

        <!-- وضعیت سیستم -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">وضعیت سیستم</h5>
                </div>
                <div class="card-body">
                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}-subtle rounded-circle me-3">
                            <i class="fas fa-server text-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">وضعیت سرور</h6>
                            <span class="text-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}">
                                {{ $systemStatus['server']['message'] }}
                            </span>
                        </div>
                        <span class="badge bg-{{ $systemStatus['server']['status'] ? 'success' : 'danger' }}">
                            {{ $systemStatus['server']['uptime'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}-subtle rounded-circle me-3">
                            <i class="fas fa-database text-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">وضعیت دیتابیس</h6>
                            <span class="text-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}">
                                {{ $systemStatus['database']['message'] }}
                            </span>
                        </div>
                        <span class="badge bg-{{ $systemStatus['database']['status'] ? 'primary' : 'danger' }}">
                            {{ $systemStatus['database']['uptime'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center mb-3">
                        <div class="status-icon bg-{{ $systemStatus['memory']['status'] }}-subtle rounded-circle me-3">
                            <i class="fas fa-memory text-{{ $systemStatus['memory']['status'] }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">مصرف حافظه</h6>
                            <div class="progress mt-1" style="height: 6px;">
                                <div class="progress-bar bg-{{ $systemStatus['memory']['status'] }}"
                                     role="progressbar"
                                     style="width: {{ $systemStatus['memory']['usage'] }}%"
                                     aria-valuenow="{{ $systemStatus['memory']['usage'] }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                        <span class="badge bg-{{ $systemStatus['memory']['status'] }}">
                            {{ $systemStatus['memory']['usage'] }}%
                        </span>
                    </div>

                    <div class="system-status-item d-flex align-items-center">
                        <div class="status-icon bg-{{ $systemStatus['cpu']['status'] }}-subtle rounded-circle me-3">
                            <i class="fas fa-microchip text-{{ $systemStatus['cpu']['status'] }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-0">مصرف CPU</h6>
                            <div class="progress mt-1" style="height: 6px;">
                                <div class="progress-bar bg-{{ $systemStatus['cpu']['status'] }}"
                                     role="progressbar"
                                     style="width: {{ $systemStatus['cpu']['usage'] }}%"
                                     aria-valuenow="{{ $systemStatus['cpu']['usage'] }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                        <span class="badge bg-{{ $systemStatus['cpu']['status'] }}">
                            {{ $systemStatus['cpu']['usage'] }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش آمار پیشرفته -->
    <div class="row mb-4">
        <!-- نمودار دونات درآمد -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">توزیع درآمد</h5>
                </div>
                <div class="card-body">
                    <div id="revenue-donut-chart" style="height: 300px;"></div>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">واریز مستقیم</span>
                            <span class="fw-bold">{{ number_format($data['direct_deposits'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">کارمزد تراکنش</span>
                            <span class="fw-bold">{{ number_format($data['transaction_fees'] ?? 0) }} تومان</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-muted">سایر درآمدها</span>
                            <span class="fw-bold">{{ number_format($data['other_revenue'] ?? 0) }} تومان</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار کاربران -->
        <div class="col-xl-4 col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">آمار کاربران</h5>
                </div>
                <div class="card-body">
                    <div class="user-stats">
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-success-subtle rounded me-3">
                                    <i class="fas fa-user-check text-success"></i>
                                </div>
                                <span>کاربران فعال</span>
                            </div>
                            <span class="fw-bold text-success">{{ number_format($data['active_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-warning-subtle rounded me-3">
                                    <i class="fas fa-user-clock text-warning"></i>
                                </div>
                                <span>در انتظار تایید</span>
                            </div>
                            <span class="fw-bold text-warning">{{ number_format($data['pending_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-danger-subtle rounded me-3">
                                    <i class="fas fa-user-times text-danger"></i>
                                </div>
                                <span>مسدود شده</span>
                            </div>
                            <span class="fw-bold text-danger">{{ number_format($data['blocked_users'] ?? 0) }}</span>
                        </div>
                        <div class="stat-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon-sm bg-info-subtle rounded me-3">
                                    <i class="fas fa-user-shield text-info"></i>
                                </div>
                                <span>VIP</span>
                            </div>
                            <span class="fw-bold text-info">{{ number_format($data['vip_users'] ?? 0) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین تراکنش‌ها -->
        <div class="col-xl-4 col-lg-12 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخرین تراکنش‌ها</h5>
                    <a href="{{ route('admin.transaction.index') }}" class="btn btn-sm btn-outline-primary">مشاهده همه</a>
                </div>
                <div class="card-body p-0">
                    <div class="latest-trans-list">
                        @forelse($latestTransactions as $transaction)
                            <div class="latest-trans-item d-flex align-items-center justify-content-between p-3 border-bottom">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="trans-icon rounded-circle d-flex align-items-center justify-content-center me-2 bg-{{
                                        $transaction->type == 'deposit' ? 'success' :
                                        ($transaction->type == 'withdraw' ? 'danger' :
                                        ($transaction->type == 'buy' ? 'primary' :
                                        ($transaction->type == 'sell' ? 'warning' :
                                        ($transaction->type == 'swap_in' ? 'info' :
                                        ($transaction->type == 'swap_out' ? 'secondary' : 'secondary')))))
                                    }}-subtle">
                                        <i class="fas fa-{{
                                            $transaction->type == 'deposit' ? 'arrow-down' :
                                            ($transaction->type == 'withdraw' ? 'arrow-up' :
                                            ($transaction->type == 'buy' ? 'shopping-cart' :
                                            ($transaction->type == 'sell' ? 'money-bill-wave' :
                                            ($transaction->type == 'swap_in' ? 'exchange-alt' :
                                            ($transaction->type == 'swap_out' ? 'exchange-alt' : 'question')))))
                                        }} text-{{
                                            $transaction->type == 'deposit' ? 'success' :
                                            ($transaction->type == 'withdraw' ? 'danger' :
                                            ($transaction->type == 'buy' ? 'primary' :
                                            ($transaction->type == 'sell' ? 'warning' :
                                            ($transaction->type == 'swap_in' ? 'info' :
                                            ($transaction->type == 'swap_out' ? 'secondary' : 'secondary')))))
                                        }}"></i>
                                    </div>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold small">{{ $transaction->user ? $transaction->user->email : 'N/A' }}</span>
                                        <span class="text-muted tiny">ID: {{ $transaction->user_id }}</span>
                                    </div>
                                </div>
                                <div class="d-flex flex-column align-items-end gap-1">
                                    <span class="badge rounded-pill px-3 py-1 mb-1 bg-{{
                                        in_array($transaction->type, ['buy', 'deposit', 'increase']) ? 'success' :
                                        (in_array($transaction->type, ['sell', 'withdraw', 'decrease']) ? 'danger' :
                                        ($transaction->type == 'swap_out' ? 'warning' :
                                        ($transaction->type == 'swap_in' ? 'info' : 'secondary')))
                                    }}-subtle text-{{
                                        in_array($transaction->type, ['buy', 'deposit', 'increase']) ? 'success' :
                                        (in_array($transaction->type, ['sell', 'withdraw', 'decrease']) ? 'danger' :
                                        ($transaction->type == 'swap_out' ? 'warning' :
                                        ($transaction->type == 'swap_in' ? 'info' : 'secondary')))
                                    }}">
                                        {{ $transaction->type_description ?? $transaction->type }}
                                    </span>
                                    <span class="fw-bold small">
                                        {{ number_format($transaction->amount, 8) }} {{ $transaction->currency->coin_type ?? '' }}
                                    </span>
                                    <span class="badge rounded-pill px-2 py-1 bg-{{
                                        $transaction->status == 'done' ? 'success' :
                                        ($transaction->status == 'pending' ? 'warning' :
                                        ($transaction->status == 'rejected' ? 'danger' : 'secondary'))
                                    }}-subtle text-{{
                                        $transaction->status == 'done' ? 'success' :
                                        ($transaction->status == 'pending' ? 'warning' :
                                        ($transaction->status == 'rejected' ? 'danger' : 'secondary'))
                                    }} tiny">
                                        {{ $transaction->status }}
                                    </span>
                                    <span class="text-muted tiny">{{ $transaction->created_at ? \Hekmatinasser\Verta\Verta::instance($transaction->created_at)->format('Y/m/d H:i') : 'N/A' }}</span>
                                </div>
                                <a href="{{ route('admin.transaction.show', $transaction->id) }}" class="btn btn-sm btn-light border ms-2" title="مشاهده">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-inbox text-muted fs-1 mb-3"></i>
                                <p class="text-muted">تراکنشی یافت نشد</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بخش فعالیت‌های اخیر و کاربران آنلاین -->
    <div class="row">
        <!-- هشدارهای اخیر -->
        <div class="col-xl-7 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">هشدارهای اخیر</h5>
                    <a href="{{ route('admin.alerts.index') }}" class="btn btn-sm btn-primary">مشاهده همه</a>
                </div>
                <div class="card-body p-0">
                    <div class="activity-list">
                        @forelse($userAlerts as $alert)
                        <div class="activity-item d-flex p-3 border-bottom">
                            <div class="activity-icon bg-{{ $alert->type }}-subtle rounded-circle me-3">
                                @if($alert->type == 'success')
                                    <i class="fas fa-check-circle text-success"></i>
                                @elseif($alert->type == 'warning')
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                @elseif($alert->type == 'error')
                                    <i class="fas fa-times-circle text-danger"></i>
                                @elseif($alert->type == 'info')
                                    <i class="fas fa-info-circle text-info"></i>
                                @elseif($alert->type == 'primary')
                                    <i class="fas fa-bell text-primary"></i>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    @if($alert->user)
                                        {{ $alert->user->firstname }} {{ $alert->user->lastname }}
                                    @else
                                        هشدار سیستم
                                    @endif
                                </h6>
                                <p class="mb-0 text-muted small">{{ $alert->message }}</p>
                                <span class="text-muted smaller">{{ $alert->time_ago }}</span>
                            </div>
                            <div class="ms-2">
                                @if(!$alert->read)
                                    <span class="badge bg-danger rounded-pill">جدید</span>
                                @endif
                            </div>
                        </div>
                        @empty
                        <div class="activity-item d-flex p-3 border-bottom">
                            <div class="activity-icon bg-info-subtle rounded-circle me-3">
                                <i class="fas fa-info-circle text-info"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">هشداری وجود ندارد</h6>
                                <p class="mb-0 text-muted small">در حال حاضر هیچ هشداری برای نمایش وجود ندارد.</p>
                            </div>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- کاربران آنلاین -->
        <div class="col-xl-5 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">کاربران آنلاین</h5>
                    <span class="badge bg-success">{{ $onlineUsers->count() }} آنلاین</span>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        @forelse($onlineUsers as $user)
                        <li class="list-group-item d-flex align-items-center px-3 py-3 user-item"
                            onclick="showUserDetails('{{ $user->id }}')"
                            style="cursor: pointer; transition: all 0.3s ease;">
                            <div class="me-3 position-relative">
                                <img src="{{ $user->avatar ?? "https://ui-avatars.com/api/?name={$user->firstname}+{$user->lastname}&background=5a67d8&color=fff" }}"
                                     class="rounded-circle"
                                     width="40"
                                     height="40"
                                     alt="{{ $user->firstname }} {{ $user->lastname }}"
                                     style="transition: transform 0.3s ease;">
                                <span class="position-absolute bottom-0 end-0 transform translate-middle p-1 bg-success border border-light rounded-circle"
                                      style="box-shadow: 0 0 0 2px #fff;"></span>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $user->firstname }} {{ $user->lastname }}</h6>
                                <small class="text-muted">{{ $user->email }}</small>
                            </div>
                            <div class="ms-2">
                                <i class="fas fa-chevron-left text-muted"></i>
                            </div>
                        </li>
                        @empty
                        <li class="list-group-item text-center py-3">
                            <p class="text-muted mb-0">کاربر آنلاینی وجود ندارد</p>
                        </li>
                        @endforelse
                    </ul>
                </div>

                <style>
                    .user-item:hover {
                        background: #f8fafc;
                        transform: translateX(-5px);
                    }
                    .user-item:hover img {
                        transform: scale(1.1);
                    }
                    .user-item:active {
                        transform: translateX(-2px);
                    }

                    /* استایل‌های جدید */
                    .quick-action-btn {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        text-decoration: none;
                        color: inherit;
                        padding: 1rem;
                        border-radius: 12px;
                        transition: all 0.3s ease;
                        background: #fff;
                        border: 1px solid #e2e8f0;
                    }

                    .quick-action-btn:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                        color: inherit;
                        text-decoration: none;
                    }

                    .quick-action-icon {
                        width: 50px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 12px;
                        margin-bottom: 0.5rem;
                        font-size: 1.2rem;
                    }

                    .quick-action-btn span {
                        font-size: 0.875rem;
                        font-weight: 500;
                        text-align: center;
                    }

                    .stat-icon-sm {
                        width: 35px;
                        height: 35px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .transaction-icon {
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .bg-gradient-primary {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    }

                    .card {
                        transition: all 0.3s ease;
                    }

                    .card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    }

                    .stat-card {
                        padding: 1.5rem;
                        border-radius: 12px;
                        position: relative;
                        overflow: hidden;
                    }

                    .stat-card::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                        pointer-events: none;
                    }

                    .stat-card.primary {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                    }

                    .stat-card.success {
                        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                        color: white;
                    }

                    .stat-card.info {
                        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                        color: white;
                    }

                    .stat-card.warning {
                        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
                        color: white;
                    }

                    .stat-icon {
                        font-size: 2rem;
                        opacity: 0.8;
                    }

                    .stat-content {
                        margin-left: 1rem;
                    }

                    .stat-value {
                        font-size: 1.75rem;
                        font-weight: 700;
                        line-height: 1;
                    }

                    .stat-label {
                        font-size: 0.875rem;
                        opacity: 0.9;
                        margin-top: 0.25rem;
                    }

                    .fade-in {
                        animation: fadeIn 0.5s ease-in;
                    }

                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    /* استایل‌های جستجوی سریع */
                    .search-container {
                        max-width: 100%;
                        position: relative;
                        z-index: 1050;
                    }

                    .search-results {
                        border: 1px solid #e2e8f0 !important;
                        box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
                        z-index: 10000 !important;
                        position: absolute !important;
                        left: 0 !important;
                        right: 0 !important;
                        top: 100% !important;
                        margin-top: 4px !important;
                        background: white !important;
                        border-radius: 8px !important;
                    }

                    .search-result-item {
                        padding: 12px 16px;
                        border-bottom: 1px solid #f1f5f9;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        display: flex;
                        align-items: center;
                    }

                    .search-result-item:hover {
                        background: #f8fafc;
                    }

                    .search-result-item:last-child {
                        border-bottom: none;
                    }

                    .search-result-avatar {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        margin-left: 12px;
                        background: #e2e8f0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: 600;
                        color: #64748b;
                    }

                    .search-result-info {
                        flex: 1;
                    }

                    .search-result-name {
                        font-weight: 600;
                        color: #1e293b;
                        margin-bottom: 2px;
                    }

                    .search-result-details {
                        font-size: 0.875rem;
                        color: #64748b;
                    }

                    .search-result-badge {
                        font-size: 0.75rem;
                        padding: 2px 8px;
                        border-radius: 12px;
                        margin-right: 8px;
                    }

                    .search-highlight {
                        background: #fef3c7;
                        padding: 1px 3px;
                        border-radius: 3px;
                        font-weight: 600;
                    }

                    #quickUserSearch:focus {
                        border-color: #667eea;
                        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
                    }

                    .input-group-text {
                        background: #f8fafc;
                        border-color: #e2e8f0;
                    }

                    .search-more {
                        padding: 12px 16px;
                        text-align: center;
                        background: #f8fafc;
                        border-top: 1px solid #e2e8f0;
                        color: #667eea;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    }

                    .search-more:hover {
                        background: #e2e8f0;
                        color: #5a67d8;
                    }

                    /* اطمینان از عدم کلیپ شدن dropdown */
                    .container-fluid {
                        overflow: visible !important;
                    }

                    .row {
                        overflow: visible !important;
                    }

                    .col-12 {
                        overflow: visible !important;
                    }

                    .card {
                        overflow: visible !important;
                    }

                    .card-body {
                        overflow: visible !important;
                    }

                    .stat-card-hover {
                        transition: all 0.2s;
                        overflow: visible;
                        position: relative;
                    }
                    .stat-card-hover:hover {
                        box-shadow: 0 8px 32px rgba(90,103,216,0.18) !important;
                        transform: translateY(-4px) scale(1.04);
                        z-index: 2;
                    }
                    .pulse {
                        animation: pulse 1.2s infinite;
                    }
                    @keyframes pulse {
                        0% { transform: scale(1); opacity: 1; }
                        50% { transform: scale(1.15); opacity: 0.7; }
                        100% { transform: scale(1); opacity: 1; }
                    }
                    .info-badge {
                        position: absolute;
                        top: 12px;
                        left: 12px;
                        color: #fff;
                        background: rgba(0,0,0,0.15);
                        border-radius: 50%;
                        padding: 4px 7px;
                        font-size: 1.1rem;
                        cursor: pointer;
                        transition: background 0.2s;
                    }
                    .info-badge:hover {
                        background: rgba(0,0,0,0.3);
                    }
                    .bounce {
                        animation: bounce 1.2s infinite;
                    }
                    @keyframes bounce {
                        0%, 100% { transform: translateY(0);}
                        50% { transform: translateY(-8px);}
                    }

                    .quick-user-quickview {
                        position: absolute;
                        left: 110%;
                        top: 0;
                        min-width: 320px;
                        background: #fff;
                        border-radius: 12px;
                        box-shadow: 0 8px 32px rgba(90,103,216,0.18);
                        z-index: 11000;
                        padding: 1.5rem 1.2rem;
                        border: 1px solid #e2e8f0;
                        transition: opacity 0.2s, transform 0.2s;
                        opacity: 0;
                        pointer-events: none;
                    }
                    .quick-user-quickview.active {
                        opacity: 1;
                        pointer-events: auto;
                        transform: translateY(0);
                    }
                    .quick-user-quickview .avatar {
                        width: 64px;
                        height: 64px;
                        border-radius: 50%;
                        margin-bottom: 1rem;
                        object-fit: cover;
                    }
                    .quick-user-quickview .user-name {
                        font-weight: bold;
                        font-size: 1.1rem;
                        margin-bottom: 0.5rem;
                    }
                    .quick-user-quickview .user-info {
                        font-size: 0.95rem;
                        color: #64748b;
                        margin-bottom: 0.3rem;
                    }
                    .quick-user-quickview .badge {
                        font-size: 0.8rem;
                        margin-left: 0.3rem;
                    }
                </style>

                <script>
                    function showUserDetails(userId) {
                        Swal.fire({
                            title: 'جزئیات کاربر',
                            html: '<div class="text-center">در حال بارگذاری...</div>',
                            showConfirmButton: false,
                            showCloseButton: true,
                            customClass: {
                                popup: 'animated fadeInDown faster'
                            }
                        });

                        // اینجا می‌تونید با AJAX اطلاعات کاربر رو لود کنید
                        fetch(`/admin/users/${userId}/details`)
                            .then(response => response.json())
                            .then(data => {
                                Swal.update({
                                    html: `
                                        <div class="user-details p-3">
                                            <div class="text-center mb-4">
                                                <img src="${data.avatar}" class="rounded-circle mb-3" width="80" height="80">
                                                <h5 class="mb-1">${data.name}</h5>
                                                <p class="text-muted">${data.email}</p>
                                            </div>
                                            <div class="row text-start">
                                                <div class="col-6 mb-3">
                                                    <small class="text-muted">شماره تماس</small>
                                                    <p class="mb-0">${data.phone}</p>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <small class="text-muted">تاریخ عضویت</small>
                                                    <p class="mb-0">${data.created_at}</p>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">وضعیت</small>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success">فعال</span>
                                                    </p>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">آخرین فعالیت</small>
                                                    <p class="mb-0">${data.last_activity}</p>
                                                </div>
                                            </div>
                                            <div class="mt-4">
                                                <a href="/admin/users/${userId}" class="btn btn-primary btn-sm">
                                                    مشاهده پروفایل کامل
                                                </a>
                                            </div>
                                        </div>
                                    `
                                });
                            });
                    }
                </script>
                <div class="card-footer text-center">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-sm btn-outline-primary">مشاهده همه کاربران</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick View Popup -->
<div id="quickUserQuickView" class="quick-user-quickview d-none"></div>

<!-- Modal جستجوی سریع کاربر -->
<div class="modal fade" id="quickUserSearchModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content" style="border-radius: 18px; overflow: hidden;">
      <div class="modal-header bg-gradient-primary text-white" style="border-bottom: none;">
        <h5 class="modal-title d-flex align-items-center gap-2">
          <span class="d-flex align-items-center justify-content-center bg-white bg-opacity-25 rounded-circle p-2 me-2">
            <i class="fas fa-search fa-lg text-white"></i>
          </span>
          جستجوی سریع کاربر
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="بستن"></button>
      </div>
      <div class="modal-body" style="background: #f8fafc;">
        <div class="card shadow-sm mb-3" style="border-radius: 14px;">
          <div class="card-body py-3 px-4">
            <div class="row g-2 align-items-center">
              <div class="col-md-5 mb-2 mb-md-0">
                <div class="input-group input-group-lg">
                  <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-search text-primary"></i>
                  </span>
                  <input type="text" class="form-control border-start-0 ps-0" id="quickUserSearch" placeholder="جستجو بر اساس نام، موبایل، ایمیل، کد ملی ..." autocomplete="off">
                </div>
              </div>
              <div class="col-md-3 mb-2 mb-md-0">
                <select id="userStatusFilter" class="form-select form-select-lg">
                  <option value="">همه وضعیت‌ها</option>
                  <option value="approved">تایید شده</option>
                  <option value="pending">در انتظار</option>
                  <option value="rejected">رد شده</option>
                  <option value="blocked">مسدود</option>
                </select>
              </div>
              <div class="col-md-2 mb-2 mb-md-0">
                <select id="userRoleFilter" class="form-select form-select-lg">
                  <option value="">همه نقش‌ها</option>
                  <option value="admin">ادمین</option>
                  <option value="user">کاربر عادی</option>
                  <!-- نقش‌های دیگر در صورت نیاز -->
                </select>
              </div>
              <div class="col-md-2 text-end">
                <button class="btn btn-outline-danger btn-lg w-100 d-flex align-items-center justify-content-center gap-2" type="button" id="clearSearch">
                  <i class="fas fa-times-circle"></i> پاک کردن
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="search-loading text-center py-4 d-none">
          <div class="spinner-border spinner-border-md text-primary me-2" role="status"></div>
          <span class="text-muted">در حال جستجو...</span>
        </div>
        <div class="search-content" style="max-height: 350px; overflow-y: auto;"></div>
        <div class="search-empty text-center py-5 text-muted d-none">
          <i class="fas fa-user-slash fs-1 mb-2 d-block"></i>
          <span>کاربری یافت نشد</span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const openModalBtn = document.getElementById('openQuickUserSearchModal');
        const searchModal = new bootstrap.Modal(document.getElementById('quickUserSearchModal'));
        const modalEl = document.getElementById('quickUserSearchModal');
        const quickSearchInput = document.getElementById('quickUserSearch');
        const statusFilter = document.getElementById('userStatusFilter');
        const roleFilter = document.getElementById('userRoleFilter');
        const clearSearchBtn = document.getElementById('clearSearch');
        const searchContent = modalEl.querySelector('.search-content');
        const searchLoading = modalEl.querySelector('.search-loading');
        const searchEmpty = modalEl.querySelector('.search-empty');
        let searchTimeout;
        let currentRequest;
        let selectedIndex = -1;
        let lastUsers = [];

        openModalBtn.addEventListener('click', function() {
            searchModal.show();
            setTimeout(() => quickSearchInput.focus(), 400);
        });

        function performSearch(query) {
            if (query.length < 2) {
                searchContent.innerHTML = '';
                searchEmpty.classList.add('d-none');
                searchLoading.classList.add('d-none');
                return;
            }
            if (currentRequest) currentRequest.abort();
            showSearchLoading();
            currentRequest = new AbortController();
            const status = statusFilter.value;
            const role = roleFilter.value;
            let url = `/admin/users/quick-search?q=${encodeURIComponent(query)}`;
            if (status) url += `&status=${status}`;
            if (role) url += `&role=${role}`;
            fetch(url, {
                signal: currentRequest.signal,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideSearchLoading();
                lastUsers = data.users || [];
                displaySearchResults(lastUsers, query);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    hideSearchLoading();
                    showSearchEmpty();
                }
            });
        }
        function updateSelection(index) {
            const items = searchContent.querySelectorAll('.search-result-item');
            items.forEach((item, i) => {
                if (i === index) {
                    item.classList.add('active');
                    item.scrollIntoView({ block: 'nearest' });
                } else {
                    item.classList.remove('active');
                }
            });
            selectedIndex = index;
        }
        function addResultEvents(users) {
            const items = searchContent.querySelectorAll('.search-result-item');
            items.forEach((item, i) => {
                item.addEventListener('mouseenter', () => {
                    updateSelection(i);
                });
                item.addEventListener('click', () => goToUser(users[i].id));
            });
        }
        function displaySearchResults(users, query) {
            if (users.length === 0) {
                showSearchEmpty();
                return;
            }
            const resultsHtml = users.map((user, idx) => {
                const avatar = user.avatar || `https://ui-avatars.com/api/?name=${user.firstname}+${user.lastname}&background=667eea&color=fff`;
                const fullName = `${user.firstname} ${user.lastname}`;
                const highlightedName = highlightText(fullName, query);
                const highlightedPhone = highlightText(user.phone || '', query);
                const highlightedEmail = highlightText(user.email || '', query);
                const highlightedNationalId = highlightText(user.national_id || '', query);
                return `
                    <div class="search-result-item d-flex align-items-center" tabindex="0" data-index="${idx}">
                        <div class="search-result-avatar me-3">
                            <img src="${avatar}" alt="${fullName}" class="w-100 h-100 rounded-circle" style="object-fit: cover;">
                        </div>
                        <div class="search-result-info flex-grow-1">
                            <div class="search-result-name">${highlightedName}</div>
                            <div class="search-result-details">
                                ${user.phone ? `📱 ${highlightedPhone}` : ''}
                                ${user.email ? `📧 ${highlightedEmail}` : ''}
                                ${user.national_id ? `🆔 ${highlightedNationalId}` : ''}
                            </div>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <span class="search-result-badge bg-${getStatusColor(user.status)}-subtle text-${getStatusColor(user.status)} mb-1">
                                ${getStatusText(user.status)}
                            </span>
                            <span class="badge bg-info-subtle text-info mb-1">${user.role || 'نامشخص'}</span>
                            <small class="text-muted mt-1">ID: ${user.id}</small>
                        </div>
                    </div>
                `;
            }).join('');
            searchContent.innerHTML = resultsHtml + `<div class="search-more text-muted">تعداد نتایج: ${users.length} <button class='btn btn-link btn-sm' onclick='goToUsersPage("${query}")'>مشاهده همه</button></div>`;
            addResultEvents(users);
        }
        function highlightText(text, query) {
            if (!text || !query) return text;
            const regex = new RegExp(`(${query})`, 'gi');
            return text.replace(regex, '<span class="search-highlight">$1</span>');
        }
        function getStatusColor(status) {
            switch(status) {
                case 'approved': return 'success';
                case 'pending': return 'warning';
                case 'rejected': return 'danger';
                case 'blocked': return 'dark';
                default: return 'secondary';
            }
        }
        function getStatusText(status) {
            switch(status) {
                case 'approved': return 'تایید شده';
                case 'pending': return 'در انتظار';
                case 'rejected': return 'رد شده';
                case 'blocked': return 'مسدود';
                default: return 'نامشخص';
            }
        }
        function showSearchLoading() {
            searchLoading.classList.remove('d-none');
            searchEmpty.classList.add('d-none');
            searchContent.innerHTML = '';
        }
        function hideSearchLoading() {
            searchLoading.classList.add('d-none');
        }
        function showSearchEmpty() {
            searchEmpty.classList.remove('d-none');
            searchLoading.classList.add('d-none');
            searchContent.innerHTML = '';
        }
        quickSearchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (query.length >= 2) {
                    performSearch(query);
                }
            }, 300);
        });
        statusFilter.addEventListener('change', function() {
            const query = quickSearchInput.value.trim();
            if (query.length >= 2) performSearch(query);
        });
        roleFilter.addEventListener('change', function() {
            const query = quickSearchInput.value.trim();
            if (query.length >= 2) performSearch(query);
        });
        clearSearchBtn.addEventListener('click', function() {
            quickSearchInput.value = '';
            searchContent.innerHTML = '';
            searchEmpty.classList.add('d-none');
            searchLoading.classList.add('d-none');
            quickSearchInput.focus();
        });
        quickSearchInput.addEventListener('keydown', function(e) {
            const items = searchContent.querySelectorAll('.search-result-item');
            if (['ArrowDown', 'ArrowUp', 'Enter'].includes(e.key) && items.length > 0) {
                e.preventDefault();
                if (e.key === 'ArrowDown') {
                    updateSelection((selectedIndex + 1) % items.length);
                } else if (e.key === 'ArrowUp') {
                    updateSelection((selectedIndex - 1 + items.length) % items.length);
                } else if (e.key === 'Enter' && selectedIndex >= 0) {
                    items[selectedIndex].click();
                }
            }
        });
        window.goToUser = function(userId) {
            window.location.href = `/admin/users/${userId}`;
        };
        window.goToUsersPage = function(query) {
            window.location.href = `/admin/users?search=${encodeURIComponent(query)}`;
        };
    });
</script>

<script>
    // تابع به‌روزرسانی آمار روزانه خرید و فروش
    function refreshTradingStats() {
        const refreshBtn = document.getElementById('refresh-stats-btn');
        const originalContent = refreshBtn.innerHTML;
        
        // نمایش loading
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>در حال به‌روزرسانی...';
        refreshBtn.disabled = true;
        
        // درخواست به API
        fetch('/admin/api/daily-trading-stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateTradingStatsDisplay(data.data);
                    showToast('آمار با موفقیت به‌روزرسانی شد', 'success');
                } else {
                    showToast('خطا در به‌روزرسانی آمار: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error refreshing stats:', error);
                showToast('خطا در اتصال به سرور', 'error');
            })
            .finally(() => {
                // بازگرداندن دکمه به حالت عادی
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            });
    }
    
    // تابع به‌روزرسانی نمایش آمار
    function updateTradingStatsDisplay(stats) {
        // به‌روزرسانی کارت‌های اصلی
        document.querySelector('.trading-stat-card:nth-child(1) h4').textContent = stats.formatted_total_buy;
        document.querySelector('.trading-stat-card:nth-child(1) .badge').textContent = stats.transaction_count.buy + ' تراکنش';
        
        document.querySelector('.trading-stat-card:nth-child(2) h4').textContent = stats.formatted_total_sell;
        document.querySelector('.trading-stat-card:nth-child(2) .badge').textContent = stats.transaction_count.sell + ' تراکنش';
        
        document.querySelector('.trading-stat-card:nth-child(3) h4').textContent = stats.formatted_net_flow;
        document.querySelector('.trading-stat-card:nth-child(3) .badge').textContent = stats.net_flow >= 0 ? 'ورودی' : 'خروجی';
        
        document.querySelector('.trading-stat-card:nth-child(4) h4').textContent = stats.formatted_profit;
        document.querySelector('.trading-stat-card:nth-child(4) .badge').textContent = stats.profit_percentage + '%';
        
        // به‌روزرسانی سود دلاری
        const profitCard = document.querySelector('.trading-stat-card:nth-child(4)');
        const usdProfitElement = profitCard.querySelector('.small.text-muted');
        if (usdProfitElement && stats.formatted_profit_usd) {
            usdProfitElement.textContent = stats.formatted_profit_usd;
        }
        
        // به‌روزرسانی جدول ارزها
        updateCurrencyStatsTable(stats.currency_stats);
        
        // به‌روزرسانی قیمت دلار
        updateUsdRates(stats.currency_stats);
        
        // به‌روزرسانی آمار سواپ
        updateSwapStatsDisplay(stats.swap_stats);
        
        // به‌روزرسانی تاریخ
        document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-chart-line me-2"></i>آمار روزانه خرید و فروش - ' + stats.date;
    }
    
    // تابع به‌روزرسانی جدول ارزها
    function updateCurrencyStatsTable(currencyStats) {
        const tableBody = document.querySelector('.table tbody');
        if (!tableBody) return;
        
        if (currencyStats.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center text-muted">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <p>هیچ تراکنشی برای امروز ثبت نشده است</p>
                    </td>
                </tr>
            `;
            return;
        }
        
        const rows = currencyStats.map(currency => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-secondary me-2">${currency.symbol}</span>
                        ${currency.name}
                    </div>
                </td>
                <td class="text-center text-primary">${currency.formatted_buy_volume}</td>
                <td class="text-center text-primary">${currency.formatted_buy_amount}</td>
                <td class="text-center text-primary">${currency.formatted_average_buy_price}</td>
                <td class="text-center text-primary">${currency.formatted_average_buy_usd_rate}</td>
                <td class="text-center text-success">${currency.formatted_sell_volume}</td>
                <td class="text-center text-success">${currency.formatted_sell_amount}</td>
                <td class="text-center text-success">${currency.formatted_average_sell_price}</td>
                <td class="text-center text-success">${currency.formatted_average_sell_usd_rate}</td>
                <td class="text-center">
                    <span class="badge ${currency.profit >= 0 ? 'bg-success' : 'bg-danger'}">
                        ${currency.formatted_profit}
                    </span>
                </td>
                <td class="text-center">
                    <span class="badge ${currency.profit_usd >= 0 ? 'bg-success' : 'bg-danger'}">
                        ${currency.formatted_profit_usd}
                    </span>
                </td>
            </tr>
        `).join('');
        
        tableBody.innerHTML = rows;
    }
    
    // تابع به‌روزرسانی قیمت دلار
    function updateUsdRates(currencyStats) {
        const alertElement = document.querySelector('.alert-info');
        if (alertElement && currencyStats.length > 0) {
            const firstCurrency = currencyStats[0];
            const buyRateElement = alertElement.querySelector('.col-md-6:first-child .badge');
            const sellRateElement = alertElement.querySelector('.col-md-6:last-child .badge');
            
            if (buyRateElement) {
                buyRateElement.textContent = firstCurrency.formatted_average_buy_usd_rate;
            }
            if (sellRateElement) {
                sellRateElement.textContent = firstCurrency.formatted_average_sell_usd_rate;
            }
        }
    }
    
    // تابع نمایش toast
    function showToast(message, type = 'info') {
        // ایجاد toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0 position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // نمایش toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        bsToast.show();
        
        // حذف toast بعد از نمایش
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }
    
    // تابع به‌روزرسانی نمایش آمار سواپ
    function updateSwapStatsDisplay(swapStats) {
        // به‌روزرسانی کارت‌های سواپ
        const swapCards = document.querySelectorAll('.trading-stat-card');
        if (swapCards.length >= 8) { // بعد از کارت‌های اصلی
            swapCards[4].querySelector('h4').textContent = swapStats.formatted_total_swaps;
            swapCards[4].querySelector('.badge').textContent = swapStats.total_swaps + ' تراکنش';
            
            swapCards[5].querySelector('h4').textContent = swapStats.formatted_total_swap_volume;
            swapCards[6].querySelector('h4').textContent = swapStats.formatted_total_swap_fees;
            swapCards[7].querySelector('h4').textContent = swapStats.formatted_total_swap_usd_value;
        }
        
        // به‌روزرسانی جدول جفت ارزها
        updateSwapStatsTable(swapStats.swap_pairs);
    }
    
    // تابع به‌روزرسانی جدول آمار سواپ
    function updateSwapStatsTable(swapPairs) {
        const swapTableBody = document.querySelector('.table tbody');
        if (!swapTableBody) return;
        
        // پیدا کردن جدول سواپ (جدول دوم)
        const swapTable = document.querySelectorAll('.table-responsive')[1];
        if (!swapTable) return;
        
        const swapTableBodyElement = swapTable.querySelector('tbody');
        if (!swapTableBodyElement) return;
        
        if (swapPairs.length === 0) {
            swapTableBodyElement.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-muted empty-state">
                        <div class="empty-icon mb-3">
                            <i class="fas fa-exchange-alt fa-4x text-muted"></i>
                        </div>
                        <h5 class="text-muted mb-2">هیچ سواپی برای امروز انجام نشده است</h5>
                        <p class="text-muted mb-0">کاربران هنوز هیچ عملیات تبدیل ارزی انجام نداده‌اند</p>
                    </td>
                </tr>
            `;
            return;
        }
        
        // محاسبه حداکثر حجم برای نوار پیشرفت
        const maxVolume = Math.max(...swapPairs.map(pair => pair.total_volume));
        const totalFees = swapPairs.reduce((sum, pair) => sum + pair.total_fees, 0);
        
        const rows = swapPairs.map((pair, index) => {
            const progressWidth = maxVolume > 0 ? (pair.total_volume / maxVolume) * 100 : 0;
            const feePercentage = totalFees > 0 ? ((pair.total_fees / totalFees) * 100).toFixed(1) : 0;
            const isPopular = index < 3;
            
            return `
                <tr class="swap-row" data-pair="${pair.from_currency}_${pair.to_currency}">
                    <td>
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="currency-pair-display">
                                <div class="currency-badge from-currency">
                                    <i class="fas fa-coins me-1"></i>
                                    <span class="currency-symbol">${pair.from_currency}</span>
                                </div>
                                <div class="swap-arrow">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="currency-badge to-currency">
                                    <i class="fas fa-coins me-1"></i>
                                    <span class="currency-symbol">${pair.to_currency}</span>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        <div class="swap-count-badge">
                            <span class="count-number">${pair.count}</span>
                            <small class="count-label">سواپ</small>
                        </div>
                    </td>
                    <td class="text-center">
                        <div class="volume-display">
                            <div class="volume-value text-warning">
                                <i class="fas fa-chart-pie me-1"></i>
                                ${pair.formatted_total_volume}
                            </div>
                            <div class="volume-bar">
                                <div class="volume-progress" style="width: ${progressWidth}%"></div>
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        <div class="fee-display">
                            <div class="fee-value text-success">
                                <i class="fas fa-coins me-1"></i>
                                ${pair.formatted_total_fees}
                            </div>
                            <div class="fee-percentage">
                                <small class="text-muted">
                                    ${feePercentage}% از کل
                                </small>
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        <div class="usd-display">
                            <div class="usd-value text-primary">
                                <i class="fas fa-dollar-sign me-1"></i>
                                ${pair.formatted_total_usd_value}
                            </div>
                            <div class="usd-trend">
                                ${isPopular ? `
                                    <span class="badge bg-success badge-sm">
                                        <i class="fas fa-trending-up me-1"></i>
                                        محبوب
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
        
        swapTableBodyElement.innerHTML = rows;
        
        // به‌روزرسانی خلاصه آمار
        updateSwapSummaryCards(swapPairs);
    }
    
    // تابع به‌روزرسانی کارت‌های خلاصه سواپ
    function updateSwapSummaryCards(swapPairs) {
        const totalSwaps = swapPairs.reduce((sum, pair) => sum + pair.count, 0);
        const totalVolume = swapPairs.reduce((sum, pair) => sum + pair.total_volume, 0);
        const totalFees = swapPairs.reduce((sum, pair) => sum + pair.total_fees, 0);
        const totalUsdValue = swapPairs.reduce((sum, pair) => sum + pair.total_usd_value, 0);
        
        const avgVolume = totalSwaps > 0 ? (totalVolume / totalSwaps).toFixed(8) : 0;
        const avgFees = totalSwaps > 0 ? (totalFees / totalSwaps).toFixed(8) : 0;
        const avgUsdValue = totalSwaps > 0 ? (totalUsdValue / totalSwaps).toFixed(2) : 0;
        
        // به‌روزرسانی کارت‌های خلاصه
        const summaryCards = document.querySelectorAll('.summary-card');
        if (summaryCards.length >= 4) {
            summaryCards[0].querySelector('p').textContent = swapPairs.length;
            summaryCards[1].querySelector('p').textContent = avgVolume;
            summaryCards[2].querySelector('p').textContent = avgFees;
            summaryCards[3].querySelector('p').textContent = `$${avgUsdValue}`;
        }
    }
</script>

@endpush

<style>
    #quickUserSearchModal .modal-content {
        box-shadow: 0 8px 32px rgba(90,103,216,0.18);
    }
    #quickUserSearchModal .modal-header {
        border-bottom: none;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    #quickUserSearchModal .modal-title {
        font-weight: bold;
        font-size: 1.25rem;
    }
    #quickUserSearchModal .input-group-text {
        background: #f8fafc;
        border-color: #e2e8f0;
        font-size: 1.2rem;
    }
    #quickUserSearchModal .form-select-lg, #quickUserSearchModal .form-control-lg {
        font-size: 1.05rem;
        border-radius: 10px;
    }
    #quickUserSearchModal .search-result-item {
        padding: 16px 18px;
        border-bottom: 1px solid #f1f5f9;
        cursor: pointer;
        transition: all 0.2s;
    }
    #quickUserSearchModal .search-result-item:hover {
        background: #f8fafc;
        transform: translateY(-1px);
    }
    #quickUserSearchModal .search-result-item:last-child {
        border-bottom: none;
    }
    #quickUserSearchModal .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
    }
    #quickUserSearchModal .user-info h6 {
        margin: 0;
        font-weight: 600;
        color: #1e293b;
    }
    #quickUserSearchModal .user-info p {
        margin: 0;
        font-size: 0.875rem;
        color: #64748b;
    }
    #quickUserSearchModal .user-level {
        font-size: 0.75rem;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
    }
    #quickUserSearchModal .user-level.bronze {
        background: #fef3c7;
        color: #92400e;
    }
    #quickUserSearchModal .user-level.silver {
        background: #e5e7eb;
        color: #374151;
    }
    #quickUserSearchModal .user-level.gold {
        background: #fef3c7;
        color: #92400e;
    }
    #quickUserSearchModal .user-level.platinum {
        background: #e0e7ff;
        color: #3730a3;
    }

    /* Enhanced Swap Statistics Table Styles */
    .swap-stats-table {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: none;
    }

    .swap-stats-table thead {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }

    .swap-stats-table thead th {
        border: none;
        padding: 16px 12px;
        font-weight: 600;
        color: white;
        text-align: center;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }

    .swap-stats-table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f5f9;
    }

    .swap-stats-table tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .swap-stats-table tbody td {
        padding: 20px 12px;
        border: none;
        vertical-align: middle;
    }

    /* Currency Pair Display */
    .currency-pair-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    .currency-badge {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .currency-badge.from-currency {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
    }

    .currency-badge.to-currency {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }

    .currency-badge:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .swap-arrow {
        color: #64748b;
        font-size: 1.2rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Swap Count Badge */
    .swap-count-badge {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
    }

    .count-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #3b82f6;
        line-height: 1;
    }

    .count-label {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    /* Volume Display */
    .volume-display {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .volume-value {
        font-weight: 600;
        font-size: 0.95rem;
    }

    .volume-bar {
        width: 100%;
        height: 6px;
        background: #e2e8f0;
        border-radius: 3px;
        overflow: hidden;
    }

    .volume-progress {
        height: 100%;
        background: linear-gradient(90deg, #f59e0b 0%, #f97316 100%);
        border-radius: 3px;
        transition: width 0.8s ease;
        animation: slideIn 1s ease-out;
    }

    @keyframes slideIn {
        from { width: 0; }
        to { width: var(--progress-width); }
    }

    /* Fee Display */
    .fee-display {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .fee-value {
        font-weight: 600;
        font-size: 0.95rem;
    }

    .fee-percentage {
        font-size: 0.8rem;
    }

    /* USD Display */
    .usd-display {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
    }

    .usd-value {
        font-weight: 600;
        font-size: 0.95rem;
    }

    .usd-trend .badge {
        font-size: 0.7rem;
        padding: 4px 8px;
        border-radius: 12px;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-3px); }
        60% { transform: translateY(-2px); }
    }

    /* Summary Cards */
    .summary-card {
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: rgba(0,0,0,0.1);
    }

    .summary-icon {
        transition: all 0.3s ease;
    }

    .summary-card:hover .summary-icon {
        transform: scale(1.1);
    }

    /* Empty State */
    .empty-state {
        padding: 60px 20px;
    }

    .empty-icon {
        opacity: 0.5;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .currency-pair-display {
            flex-direction: column;
            gap: 8px;
        }
        
        .swap-arrow {
            transform: rotate(90deg);
        }
        
        .swap-stats-table thead th {
            font-size: 0.8rem;
            padding: 12px 8px;
        }
        
        .swap-stats-table tbody td {
            padding: 16px 8px;
        }
        
        .count-number {
            font-size: 1.2rem;
        }
        
        .volume-value, .fee-value, .usd-value {
            font-size: 0.85rem;
        }
    }

    /* Loading Animation */
    .swap-row {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Staggered Animation for Rows */
    .swap-row:nth-child(1) { animation-delay: 0.1s; }
    .swap-row:nth-child(2) { animation-delay: 0.2s; }
    .swap-row:nth-child(3) { animation-delay: 0.3s; }
    .swap-row:nth-child(4) { animation-delay: 0.4s; }
    .swap-row:nth-child(5) { animation-delay: 0.5s; }
</style>
