@extends('admin.layouts.app')
@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>مدیریت پست‌های وبلاگ</h2>
        <a href="{{ route('admin.blog.posts.create') }}" class="btn btn-primary">ایجاد پست جدید</a>
    </div>
    <form method="get" class="mb-3">
        <div class="row">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" placeholder="جستجو بر اساس عنوان..." value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <button class="btn btn-secondary" type="submit">جستجو</button>
            </div>
        </div>
    </form>
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>عنوان</th>
                            <th>دسته‌بندی</th>
                            <th>وضعیت</th>
                            <th>نویسنده</th>
                            <th>تاریخ انتشار</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($posts as $post)
                            <tr>
                                <td>{{ $post->id }}</td>
                                <td>{{ $post->title }}</td>
                                <td>{{ $post->category?->name ?? '-' }}</td>
                                <td>
                                    <span class="badge badge-{{ $post->status == 'published' ? 'success' : 'secondary' }}">{{ $post->status == 'published' ? 'منتشر شده' : 'پیش‌نویس' }}</span>
                                </td>
                                <td>{{ $post->user?->name ?? '-' }}</td>
                                <td>{{ $post->published_at ? verta($post->published_at)->format('Y/m/d') : '-' }}</td>
                                <td>
                                    <a href="{{ route('admin.blog.posts.edit', $post) }}" class="btn btn-sm btn-info">ویرایش</a>
                                    <form action="{{ route('admin.blog.posts.destroy', $post) }}" method="POST" style="display:inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('آیا مطمئن هستید؟')">حذف</button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr><td colspan="7" class="text-center">هیچ پستی یافت نشد.</td></tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            {{ $posts->links() }}
        </div>
    </div>
</div>
@endsection 