@extends('admin.layouts.app')
@section('content')
<div class="container-fluid">
    <h2 class="mb-4">ایجاد پست جدید</h2>
    <form action="{{ route('admin.blog.posts.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label>عنوان</label>
                    <input type="text" name="title" class="form-control" required value="{{ old('title') }}">
                </div>
                <div class="form-group">
                    <label>خلاصه</label>
                    <textarea name="excerpt" class="form-control" rows="2">{{ old('excerpt') }}</textarea>
                </div>
                <div class="form-group">
                    <label>متن کامل</label>
                    <textarea name="body" class="form-control" rows="8" required>{{ old('body') }}</textarea>
                </div>
                <div class="form-group">
                    <label>عکس کاور  1050 * 300</label>
                    <input type="file" name="cover" class="form-control-file">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>وضعیت</label>
                    <select name="status" class="form-control">
                        <option value="draft" {{ old('status')=='draft'?'selected':'' }}>پیش‌نویس</option>
                        <option value="published" {{ old('status')=='published'?'selected':'' }}>منتشر شده</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>دسته‌بندی</label>
                    <select name="category_id" class="form-control">
                        <option value="">-- انتخاب --</option>
                        @foreach($categories as $cat)
                            <option value="{{ $cat->id }}" {{ old('category_id')==$cat->id?'selected':'' }}>{{ $cat->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label>تگ‌ها</label>
                    <select name="tags[]" class="form-control" multiple>
                        @foreach($tags as $tag)
                            <option value="{{ $tag->id }}" {{ collect(old('tags'))->contains($tag->id)?'selected':'' }}>{{ $tag->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label>تاریخ انتشار</label>
                    <input type="datetime-local" name="published_at" class="form-control" value="{{ old('published_at') }}">
                </div>
                <button class="btn btn-success btn-block mt-3">ثبت پست</button>
            </div>
        </div>
    </form>
</div>
@endsection 