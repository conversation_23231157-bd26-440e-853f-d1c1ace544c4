@extends('admin.layouts.app')
@section('title', 'لیست قالب‌های پیامک')
@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>لیست قالب‌های پیامک</h2>
        <a href="{{ route('admin.sms-templates.create') }}" class="btn btn-success">افزودن قالب جدید</a>
    </div>
    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>عنوان</th>
                <th>کد</th>
                <th>template_id</th>
                <th>توضیحات</th>
                <th>عملیات</th>
            </tr>
        </thead>
        <tbody>
            @foreach($templates as $template)
                <tr>
                    <td>{{ $template->id }}</td>
                    <td>{{ $template->title }}</td>
                    <td>{{ $template->code }}</td>
                    <td>{{ $template->template_id }}</td>
                    <td>{{ $template->description }}</td>
                    <td>
                        <a href="{{ route('admin.sms-templates.edit', $template->id) }}" class="btn btn-primary btn-sm">ویرایش</a>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{ $templates->links() }}
</div>
@endsection 