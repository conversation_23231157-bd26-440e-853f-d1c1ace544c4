@extends('admin.layouts.app')
@section('title', 'ویرایش قالب پیامک')
@section('content')
<div class="container">
    <h2>ویرایش قالب پیامک</h2>
    <form action="{{ route('admin.sms-templates.update', $template->id) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="form-group">
            <label>عنوان</label>
            <input type="text" name="title" class="form-control" required value="{{ old('title', $template->title) }}">
        </div>
        <div class="form-group">
            <label>کد</label>
            <input type="text" name="code" class="form-control" required value="{{ old('code', $template->code) }}">
        </div>
        <div class="form-group">
            <label>template_id</label>
            <input type="text" name="template_id" class="form-control" required value="{{ old('template_id', $template->template_id) }}">
        </div>
        <div class="form-group">
            <label>توضیحات</label>
            <textarea name="description" class="form-control">{{ old('description', $template->description) }}</textarea>
        </div>
        <button type="submit" class="btn btn-primary">ذخیره تغییرات</button>
        <a href="{{ route('admin.sms-templates.index') }}" class="btn btn-secondary">بازگشت</a>
    </form>
</div>
@endsection 