@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header with Gradient -->
    <div class="page-header mb-4">
        <div class="header-content">
            <div class="d-flex align-items-center">
                <div class="header-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="header-text">
                    <h1 class="page-title">لیست تیکت‌ها</h1>
                    <p class="page-subtitle">مدیریت و پیگیری تیکت‌های پشتیبانی</p>
                </div>
            </div>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ $tickets->total() }}</span>
                    <span class="stat-label">کل تیکت‌ها</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters Card -->
    <div class="filters-card mb-4">
        <div class="filters-header">
            <i class="fas fa-filter"></i>
            <span>فیلترها و جستجو</span>
        </div>
        <div class="filters-body">
            <form method="GET" action="{{ route('admin.tickets.index') }}" class="filters-form">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <div class="form-group">
                            <label for="search" class="form-label">
                                <i class="fas fa-search"></i>
                                جستجو
                            </label>
                            <input type="text" class="form-control search-input" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="موضوع یا نام کاربر...">
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="form-group">
                            <label for="status" class="form-label">
                                <i class="fas fa-info-circle"></i>
                                وضعیت
                            </label>
                            <select class="form-select status-select" id="status" name="status">
                                <option value="">همه وضعیت‌ها</option>
                                <option value="open" {{ request('status') == 'open' ? 'selected' : '' }}>باز</option>
                                <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>بسته</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار پاسخ</option>
                                <option value="admin_response" {{ request('status') == 'admin_response' ? 'selected' : '' }}>پاسخ ادمین</option>
                                <option value="user_response" {{ request('status') == 'user_response' ? 'selected' : '' }}>پاسخ کاربر</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="form-group">
                            <label for="unit" class="form-label">
                                <i class="fas fa-building"></i>
                                دپارتمان
                            </label>
                            <select class="form-select unit-select" id="unit" name="unit">
                                <option value="">همه دپارتمان‌ها</option>
                                @foreach($units as $id => $name)
                                    <option value="{{ $id }}" {{ request('unit') == $id ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="form-group">
                            <label for="level" class="form-label">
                                <i class="fas fa-star"></i>
                                اولویت
                            </label>
                            <select class="form-select level-select" id="level" name="level">
                                <option value="">همه اولویت‌ها</option>
                                @foreach($levels as $id => $name)
                                    <option value="{{ $id }}" {{ request('level') == $id ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-3 d-flex align-items-end">
                        <div class="filter-buttons">
                            <button type="submit" class="btn btn-primary search-btn">
                                <i class="fas fa-search"></i>
                                جستجو
                            </button>
                            <a href="{{ route('admin.tickets.index') }}" class="btn btn-outline-secondary clear-btn">
                                <i class="fas fa-times"></i>
                                پاک کردن
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Tickets List -->
    <div class="tickets-container">
        @if($tickets->count() > 0)
            <!-- View Toggle -->
            <div class="view-toggle-container mb-3">
                <div class="view-toggle">
                    <button class="toggle-btn active" data-view="card" id="card-view-btn">
                        <i class="fas fa-th-large"></i>
                        <span>نمایش کارتی</span>
                    </button>
                    <button class="toggle-btn" data-view="list" id="list-view-btn">
                        <i class="fas fa-list"></i>
                        <span>نمایش لیستی</span>
                    </button>
                </div>
            </div>

            <!-- Card View -->
            <div class="tickets-grid" id="card-view">
                @foreach($tickets as $ticket)
                    <div class="ticket-card" data-status="{{ $ticket->status }}">
                        <div class="ticket-header">
                            <div class="ticket-id">#{{ $ticket->id }}</div>
                            <div class="ticket-status">
                                @switch($ticket->status)
                                    @case('open')
                                        <span class="status-badge status-open">
                                            <i class="fas fa-circle"></i>
                                            باز
                                        </span>
                                        @break
                                    @case('closed')
                                        <span class="status-badge status-closed">
                                            <i class="fas fa-circle"></i>
                                            بسته
                                        </span>
                                        @break
                                    @case('pending')
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-circle"></i>
                                            در انتظار
                                        </span>
                                        @break
                                    @case('admin_response')
                                        <span class="status-badge status-admin">
                                            <i class="fas fa-circle"></i>
                                            پاسخ ادمین
                                        </span>
                                        @break
                                    @case('user_response')
                                        <span class="status-badge status-user">
                                            <i class="fas fa-circle"></i>
                                            پاسخ کاربر و در انتظار
                                        </span>
                                        @break
                                    @default
                                        <span class="status-badge status-default">
                                            <i class="fas fa-circle"></i>
                                            {{ $ticket->status }}
                                        </span>
                                @endswitch
                            </div>
                        </div>
                        
                        <div class="ticket-content">
                            <h3 class="ticket-subject">{{ Str::limit($ticket->subject, 60) }}</h3>
                            
                            <div class="ticket-user">
                                <div class="user-info">
                                    <div class="user-name">{{ $ticket->user->firstname }} {{ $ticket->user->lastname }}</div>
                                    <div class="user-details">
                                        @if($ticket->user->phone)
                                            <div class="detail-item">
                                                <i class="fas fa-phone"></i>
                                                <span>0{{ $ticket->user->phone }}</span>
                                            </div>
                                        @endif
                                        @if($ticket->user->national_id)
                                            <div class="detail-item">
                                                <i class="fas fa-id-card"></i>
                                                <span>{{ $ticket->user->national_id}}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <div class="ticket-meta">
                                <div class="meta-item">
                                    <i class="fas fa-building"></i>
                                    <span>
                                        @switch($ticket->unit_id)
                                            @case(1)
                                                احراز هویت
                                                @break
                                            @case(2)
                                                مالی
                                                @break
                                            @case(3)
                                                سایر
                                                @break
                                            @default
                                                نامشخص
                                        @endswitch
                                    </span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-star"></i>
                                    <span>
                                        @switch($ticket->level_id)
                                            @case(1)
                                                عادی
                                                @break
                                            @case(2)
                                                متوسط
                                                @break
                                            @case(3)
                                                فوری
                                                @break
                                            @default
                                                نامشخص
                                        @endswitch
                                    </span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-comments"></i>
                                    <span>{{ $ticket->tickets->count() }} پیام</span>
                                </div>
                            </div>
                            
                            <div class="ticket-dates">
                                <div class="date-item">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>{{ jdate($ticket->created_at)->format('Y/m/d H:i') }}</span>
                                </div>
                                <div class="date-item">
                                    <i class="fas fa-clock"></i>
                                    <span>{{ jdate($ticket->updated_at)->format('Y/m/d H:i') }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ticket-actions">
                            <a href="{{ route('admin.tickets.show', $ticket->id) }}" 
                               class="btn btn-primary view-btn">
                                <i class="fas fa-eye"></i>
                                مشاهده
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- List View -->
            <div class="tickets-list" id="list-view" style="display: none;">
                <div class="list-table-container">
                    <table class="list-table">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>موضوع</th>
                                <th>کاربر</th>
                                <th>دپارتمان</th>
                                <th>اولویت</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tickets as $ticket)
                                <tr class="list-row" data-status="{{ $ticket->status }}">
                                    <td class="ticket-id-cell">#{{ $ticket->id }}</td>
                                    <td class="ticket-subject-cell">
                                        <div class="subject-text">{{ Str::limit($ticket->subject, 50) }}</div>
                                        <div class="message-count">{{ $ticket->tickets->count() }} پیام</div>
                                    </td>
                                    <td class="user-cell">
                                        <div class="user-name-list">{{ $ticket->user->firstname }} {{ $ticket->user->lastname }}</div>
                                        <div class="user-contact">
                                            @if($ticket->user->phone)
                                                <span class="contact-item">0{{ $ticket->user->phone }}</span>
                                            @endif
                                            @if($ticket->user->national_id)
                                                <span class="contact-item">{{ $ticket->user->national_id }}</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="unit-cell">
                                        @switch($ticket->unit_id)
                                            @case(1)
                                                <span class="unit-badge unit-auth">احراز هویت</span>
                                                @break
                                            @case(2)
                                                <span class="unit-badge unit-financial">مالی</span>
                                                @break
                                            @case(3)
                                                <span class="unit-badge unit-other">سایر</span>
                                                @break
                                            @default
                                                <span class="unit-badge unit-unknown">نامشخص</span>
                                        @endswitch
                                    </td>
                                    <td class="level-cell">
                                        @switch($ticket->level_id)
                                            @case(1)
                                                <span class="level-badge level-normal">عادی</span>
                                                @break
                                            @case(2)
                                                <span class="level-badge level-medium">متوسط</span>
                                                @break
                                            @case(3)
                                                <span class="level-badge level-urgent">فوری</span>
                                                @break
                                            @default
                                                <span class="level-badge level-unknown">نامشخص</span>
                                        @endswitch
                                    </td>
                                    <td class="status-cell">
                                        @switch($ticket->status)
                                            @case('open')
                                                <span class="status-badge status-open">
                                                    <i class="fas fa-circle"></i>
                                                    باز
                                                </span>
                                                @break
                                            @case('closed')
                                                <span class="status-badge status-closed">
                                                    <i class="fas fa-circle"></i>
                                                    بسته
                                                </span>
                                                @break
                                            @case('pending')
                                                <span class="status-badge status-pending">
                                                    <i class="fas fa-circle"></i>
                                                    در انتظار
                                                </span>
                                                @break
                                            @case('admin_response')
                                                <span class="status-badge status-admin">
                                                    <i class="fas fa-circle"></i>
                                                    پاسخ ادمین
                                                </span>
                                                @break
                                            @case('user_response')
                                                <span class="status-badge status-user">
                                                    <i class="fas fa-circle"></i>
                                                    پاسخ کاربر و در انتظار
                                                </span>
                                                @break
                                            @default
                                                <span class="status-badge status-default">
                                                    <i class="fas fa-circle"></i>
                                                    {{ $ticket->status }}
                                                </span>
                                        @endswitch
                                    </td>
                                    <td class="date-cell">
                                        <div class="created-date">{{ jdate($ticket->created_at)->format('Y/m/d') }}</div>
                                        <div class="created-time">{{ jdate($ticket->created_at)->format('H:i') }}</div>
                                    </td>
                                    <td class="actions-cell">
                                        <a href="{{ route('admin.tickets.show', $ticket->id) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                            مشاهده
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Pagination -->
            <div class="pagination-container">
                {{ $tickets->appends(request()->query())->links() }}
            </div>
        @else
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h3>هیچ تیکتی یافت نشد</h3>
                <p>با تغییر فیلترها دوباره تلاش کنید</p>
                <a href="{{ route('admin.tickets.index') }}" class="btn btn-primary">
                    <i class="fas fa-refresh"></i>
                    نمایش همه تیکت‌ها
                </a>
            </div>
        @endif
    </div>
</div>

<style>
/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.9;
}

.page-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.header-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Filters Card Styles */
.filters-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.filters-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.filters-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.filters-header i {
    margin-left: 0.5rem;
}

.filters-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-left: 0.5rem;
    color: #6b7280;
}

.form-control, .form-select {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.filter-buttons {
    display: flex;
    gap: 0.75rem;
}

.search-btn, .clear-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.search-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.clear-btn {
    background: white;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.clear-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

/* Tickets Grid Styles */
.tickets-container {
    margin-top: 2rem;
}

.tickets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.ticket-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.ticket-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.ticket-card[data-status="open"] {
    border-left: 4px solid #10b981;
}

.ticket-card[data-status="closed"] {
    border-left: 4px solid #6b7280;
}

.ticket-card[data-status="pending"] {
    border-left: 4px solid #f59e0b;
}

.ticket-card[data-status="admin_response"] {
    border-left: 4px solid #3b82f6;
}

.ticket-card[data-status="user_response"] {
    border-left: 4px solid #f59e0b;
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
}

.ticket-id {
    font-weight: 700;
    color: #374151;
    font-size: 1.1rem;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-badge i {
    font-size: 0.75rem;
}

.status-open {
    background: #dcfce7;
    color: #166534;
}

.status-closed {
    background: #f3f4f6;
    color: #374151;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-admin {
    background: #dbeafe;
    color: #1e40af;
}

.status-user {
    background: #fef3c7;
    color: #92400e;
}

.status-default {
    background: #f3f4f6;
    color: #374151;
}

.ticket-content {
    padding: 1.5rem;
}

.ticket-subject {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.ticket-user {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 1.25rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 15px;
    border: 1px solid #e2e8f0;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-name::before {
    content: '\f007';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: #667eea;
    font-size: 0.875rem;
}

.user-details {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.2s ease;
}

.detail-item:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-item i {
    color: #667eea;
    width: 16px;
    text-align: center;
    font-size: 0.8rem;
}

.detail-item span {
    font-weight: 500;
    white-space: nowrap;
}

.ticket-meta {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.meta-item i {
    color: #9ca3af;
}

.ticket-dates {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.date-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.date-item i {
    color: #9ca3af;
}

.ticket-actions {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    text-align: center;
}

.view-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    color: #374151;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6b7280;
    margin-bottom: 2rem;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination {
    display: flex;
    gap: 0.5rem;
}

.page-link {
    border-radius: 12px;
    border: 2px solid #e5e7eb;
    color: #374151;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tickets-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .ticket-meta {
        grid-template-columns: 1fr;
    }
    
    .ticket-dates {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Animation Classes */
.ticket-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ticket-card:nth-child(1) { animation-delay: 0.1s; }
.ticket-card:nth-child(2) { animation-delay: 0.2s; }
.ticket-card:nth-child(3) { animation-delay: 0.3s; }
.ticket-card:nth-child(4) { animation-delay: 0.4s; }
.ticket-card:nth-child(5) { animation-delay: 0.5s; }
.ticket-card:nth-child(6) { animation-delay: 0.6s; }

/* View Toggle Styles */
.view-toggle-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1.5rem;
}

.view-toggle {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.toggle-btn:hover {
    color: #374151;
    background: #f9fafb;
}

.toggle-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.toggle-btn i {
    font-size: 0.875rem;
}

/* List View Styles */
.tickets-list {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.list-table-container {
    overflow-x: auto;
}

.list-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.list-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.list-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.list-row {
    transition: all 0.2s ease;
}

.list-row:hover {
    background: #f8fafc;
    transform: translateX(-2px);
}

.list-row[data-status="open"] {
    border-left: 4px solid #10b981;
}

.list-row[data-status="closed"] {
    border-left: 4px solid #6b7280;
}

.list-row[data-status="pending"] {
    border-left: 4px solid #f59e0b;
}

.list-row[data-status="admin_response"] {
    border-left: 4px solid #3b82f6;
}

.list-row[data-status="user_response"] {
    border-left: 4px solid #f59e0b;
}

/* List Table Cell Styles */
.ticket-id-cell {
    font-weight: 700;
    color: #374151;
    font-size: 0.9rem;
}

.ticket-subject-cell {
    max-width: 250px;
}

.subject-text {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.message-count {
    font-size: 0.75rem;
    color: #6b7280;
}

.user-cell {
    min-width: 200px;
}

.user-name-list {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.user-contact {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.contact-item {
    font-size: 0.75rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

/* Badge Styles for List View */
.unit-badge, .level-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.unit-auth {
    background: #dbeafe;
    color: #1e40af;
}

.unit-financial {
    background: #dcfce7;
    color: #166534;
}

.unit-other {
    background: #fef3c7;
    color: #92400e;
}

.unit-unknown {
    background: #f3f4f6;
    color: #374151;
}

.level-normal {
    background: #f3f4f6;
    color: #374151;
}

.level-medium {
    background: #fef3c7;
    color: #92400e;
}

.level-urgent {
    background: #fee2e2;
    color: #991b1b;
}

.level-unknown {
    background: #f3f4f6;
    color: #374151;
}

.date-cell {
    text-align: center;
}

.created-date {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.created-time {
    font-size: 0.75rem;
    color: #6b7280;
}

.actions-cell {
    text-align: center;
}

.actions-cell .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Responsive Design for List View */
@media (max-width: 1024px) {
    .list-table th,
    .list-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .ticket-subject-cell {
        max-width: 200px;
    }
    
    .user-cell {
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .view-toggle {
        width: 100%;
        justify-content: center;
    }
    
    .list-table-container {
        font-size: 0.75rem;
    }
    
    .list-table th,
    .list-table td {
        padding: 0.5rem 0.25rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const cardViewBtn = document.getElementById('card-view-btn');
    const listViewBtn = document.getElementById('list-view-btn');
    const cardView = document.getElementById('card-view');
    const listView = document.getElementById('list-view');
    
    // Get saved view preference from localStorage
    const savedView = localStorage.getItem('ticketViewMode') || 'card';
    
    // Set initial view based on saved preference
    setView(savedView);
    
    // Card view button click
    cardViewBtn.addEventListener('click', function() {
        setView('card');
    });
    
    // List view button click
    listViewBtn.addEventListener('click', function() {
        setView('list');
    });
    
    function setView(view) {
        if (view === 'card') {
            cardView.style.display = 'grid';
            listView.style.display = 'none';
            cardViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
        } else {
            cardView.style.display = 'none';
            listView.style.display = 'block';
            listViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
        }
        
        // Save preference to localStorage
        localStorage.setItem('ticketViewMode', view);
    }
});
</script>

@endsection 