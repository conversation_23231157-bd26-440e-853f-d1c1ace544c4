<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#5a67d8">
    <title>پنل مدیریت | @yield('title', 'Exchangim')</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Space+Mono&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.7.0/vanilla-tilt.min.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <style>
        :root {
            --primary-color: #5a67d8;
            --secondary-color: #4c51bf;
            --dark-color: #1a202c;
            --light-color: #f7fafc;
            --success-color: #48bb78;
            --warning-color: #ecc94b;
            --danger-color: #e53e3e;
            --info-color: #4299e1;
            --sidebar-width: 280px;
            --header-height: 70px;
            --card-border-radius: 15px;
            --btn-border-radius: 10px;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Vazirmatn', sans-serif;
            background-color: #f0f2f5;
            overflow-x: hidden;
        }

        /* Improved Wrapper */
        .wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* Enhanced Main Content */
        .main-content {
            flex: 1;
            margin-right: var(--sidebar-width);
            padding: 1.5rem;
            transition: margin var(--transition-speed) ease;
        }

        /* Improved Cards */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Stat Cards */
        .stat-card {
            position: relative;
            overflow: hidden;
            border-radius: var(--card-border-radius);
            padding: 1.5rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .stat-card.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .stat-card.success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .stat-card.warning {
            background: linear-gradient(135deg, #ecc94b, #d69e2e);
            color: white;
        }

        .stat-card.danger {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
        }

        .stat-card.info {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1));
            z-index: 1;
        }

        .stat-card .stat-icon {
            position: absolute;
            bottom: -15px;
            left: -15px;
            font-size: 5rem;
            opacity: 0.2;
            z-index: 0;
        }

        .stat-card .stat-content {
            position: relative;
            z-index: 2;
        }

        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-card .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stat-card .stat-change {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }

        .stat-card .stat-change i {
            margin-left: 0.25rem;
        }

        /* Enhanced Buttons */
        .btn {
            border-radius: var(--btn-border-radius);
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            transition: all var(--transition-speed) ease;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(90, 103, 216, 0.4);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Improved Tables */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(90, 103, 216, 0.05);
        }

        .table th {
            font-weight: 600;
            color: #4a5568;
            border-bottom-width: 1px;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        /* Custom Navbar */
        .custom-nav {
            background: rgba(255,255,255,0.85);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
            backdrop-filter: blur(10px);
            border-radius: 22px;
            border: 1px solid rgba(255,255,255,0.18);
            padding: 1.2rem 2.2rem;
            margin: 1.5rem 1.5rem 2rem 1.5rem;
            position: sticky;
            top: 0;
            z-index: 100;
            transition: all 0.3s cubic-bezier(.4,2.3,.3,1);
        }

        .custom-nav .nav-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .custom-nav .search-box {
            position: relative;
            min-width: 220px;
            margin-left: 1.5rem;
        }
        .custom-nav .search-box input {
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            background: #f8fafc;
            transition: all 0.3s;
            font-size: 1rem;
        }
        .custom-nav .search-box input:focus {
            border-color: #5a67d8;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(90,103,216,0.08);
        }
        .custom-nav .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 1.1rem;
        }

        .custom-nav .quick-action-btn, .custom-nav .nav-link {
            border-radius: 14px;
            font-size: 1rem;
            font-weight: 500;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
            padding: 0.5rem 1.1rem;
            background: transparent;
            border: none;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .custom-nav .quick-action-btn:hover, .custom-nav .nav-link:hover {
            background: linear-gradient(90deg, #5a67d8 0%, #4c51bf 100%);
            color: #fff !important;
            box-shadow: 0 4px 16px rgba(90,103,216,0.10);
            transform: translateY(-2px) scale(1.04);
        }

        .custom-nav .divider {
            width: 1px;
            height: 36px;
            background: #e2e8f0;
            margin: 0 1.5rem;
            display: inline-block;
        }

        .custom-nav .avatar {
            border: 2px solid #5a67d8;
            box-shadow: 0 2px 8px rgba(90,103,216,0.10);
            transition: box-shadow 0.2s;
        }
        .custom-nav .avatar:hover {
            box-shadow: 0 4px 16px rgba(90,103,216,0.18);
        }

        .custom-nav .notifications-badge {
            animation: pulse 1.2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(239,68,68,0.7);}
            70% { box-shadow: 0 0 0 10px rgba(239,68,68,0);}
            100% { box-shadow: 0 0 0 0 rgba(239,68,68,0);}
        }

        /* Page Title */
        .page-title {
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .page-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 4px;
            background: linear-gradient(to left, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        /* Modern Breadcrumb */
        .breadcrumb {
            background: none !important;
            padding: 0 !important;
            margin: 0 !important;
            font-size: 0.97rem;
            color: #6c757d;
            --bs-breadcrumb-divider: '›';
        }
        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }
        .breadcrumb-item a {
            color: #5a67d8;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.15s;
        }
        .breadcrumb-item a:hover {
            color: #2b308b;
            text-decoration: underline;
        }
        .breadcrumb-item.active {
            color: #6c757d;
            font-weight: 400;
        }
        .breadcrumb .fa-home {
            font-size: 1.1em;
            margin-left: 0.2em;
            color: #5a67d8;
        }

        /* Animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
        @media (max-width: 992px) {
            .main-content {
                margin-right: 0;
            }
        }

        .quick-action-btn {
            padding: 0.5rem 1rem;
            border-radius: 10px;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            color: #4a5568;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .quick-action-btn:hover {
            background: #f8fafc;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .quick-action-btn .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
        }

        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background-color: #f8d7da;
            color: #842029;
        }

        .badge-info {
            background-color: #cff4fc;
            color: #055160;
        }

        .quick-actions-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        @media (max-width: 992px) {
            .quick-actions-wrapper {
                display: none;
            }
        }

        /* --- Simple & Compact Top Nav --- */
        .simple-nav {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            border-radius: 12px;
            padding: 0.5rem 1.2rem;
            margin: 1rem 0 1.5rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 56px;
        }

        .simple-nav .nav-section {
            display: flex;
            align-items: center;
            gap: 0.7rem;
        }

        .simple-nav .quick-action-btn, .simple-nav .nav-link {
            background: none;
            border: none;
            color: #4a5568;
            font-size: 1rem;
            padding: 0.3rem 0.7rem;
            border-radius: 8px;
            transition: background 0.15s;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .simple-nav .quick-action-btn:hover, .simple-nav .nav-link:hover {
            background: #f3f4f6;
            color: #5a67d8;
        }

        .simple-nav .avatar img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #e2e8f0;
        }

        .simple-nav .badge {
            font-size: 0.75rem;
            padding: 0.15rem 0.5rem;
            border-radius: 6px;
            margin-right: 0.2rem;
        }
    </style>
    @stack('styles')
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        @include('admin.layouts.sidebar')

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <nav class="simple-nav">
                <!-- راست: دکمه سایدبار و بردکرامب -->
                <div class="nav-section">
                    <button class="btn sidebar-toggle d-lg-none me-2 p-0" style="font-size:1.2rem;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <nav aria-label="breadcrumb" class="d-none d-md-block">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">
                                    <i class="fas fa-home"></i>
                                    داشبورد
                                </a>
                            </li>
                            @yield('breadcrumb')
                        </ol>
                    </nav>
                </div>
                <!-- چپ: اکشن‌ها و پروفایل -->
                <div class="nav-section">
                    <button type="button" class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#ticketsModal">
                        <i class="fas fa-ticket-alt text-warning"></i>
                        <span class="badge badge-warning">{{ $pendingTicketsCount ?? 12 }}</span>
                    </button>
                    <button type="button" class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#documentsModal">
                        <i class="fas fa-file-alt text-danger"></i>
                        <span class="badge badge-danger">{{ $pendingDocsCount ?? 5 }}</span>
                    </button>
                    <button type="button" class="quick-action-btn" data-bs-toggle="modal" data-bs-target="#usersModal">
                        <i class="fas fa-user-clock text-info"></i>
                        <span class="badge badge-info">{{ $pendingUsersCount ?? 8 }}</span>
                    </button>
                    <div class="dropdown">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge badge-danger" style="position:absolute;top:0;right:0;">3</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end p-0" style="width: 220px;">
                            <div class="p-2 border-bottom">
                                <h6 class="mb-0 fw-bold" style="font-size:1rem;">اعلان‌ها</h6>
                            </div>
                            <div class="notifications-list">
                                <a href="#" class="dropdown-item d-flex align-items-center p-2">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <span style="font-size:0.95rem;">کاربر جدید ثبت نام کرد</span>
                                </a>
                            </div>
                            <a href="#" class="dropdown-item text-center p-2 text-primary fw-medium" style="font-size:0.95rem;">
                                مشاهده همه اعلان‌ها
                            </a>
                        </div>
                    </div>
                    <div class="dropdown">
                        <a class="nav-link d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <span class="avatar me-1">
                                <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name ?? 'Admin User') }}&background=5a67d8&color=fff"
                                     alt="پروفایل">
                            </span>
                            <span class="d-none d-md-block" style="font-size:0.97rem;">{{ auth()->user()->name ?? 'مدیر سیستم' }}</span>
                            <i class="fas fa-chevron-down ms-1 fs-12 d-none d-md-inline"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user me-2 text-primary"></i>
                                    پروفایل
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cog me-2 text-primary"></i>
                                    تنظیمات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{{ route('admin.logout') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        خروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>


            <!-- Content Area -->
            <div class="container-fluid">

                <!-- Main Content -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('dismiss'))
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ session('dismiss') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @yield('content')
            </div>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
  <script>
        $("#network_id").on('change', (e)=>{
            let id = $(e.target).val();
            getCurrencyByNetworkID(id);
        });

        function getResponse(response){
            if(response.hasOwnProperty("success") && response.success){
                renderCurrency(response.data);
                return;
            }
            VanillaToasts.create({
                text: response.message ?? '{{ __("ارز یافت نشد") }}',
                type: 'warning',
                timeout: 40000
            });
        }

        function getCurrencyByNetworkID(id) {
            if (!id) return;  // اگر شبکه‌ای انتخاب نشده، درخواست نفرست

            let url = '{{ route("admin.deposit.getCurrencyByNetworkID", ":id") }}'.replace(':id', id);
            $.get(url)
                .done(getResponse)
                .fail(function() {
                    VanillaToasts.create({
                        text: '{{ __("خطا در دریافت لیست ارزها") }}',
                        type: 'error',
                        timeout: 4000
                    });
                });
        }

        function renderCurrency(currency) {
            let select = $('select[name="coin_network"]');
            let options = `<option value="">{{ __("انتخاب ارز") }}</option>`;

            if (currency && currency.length > 0) {
                currency.forEach(coin => {
                    if (coin.coin) {  // اطمینان از وجود coin
                        let selected = '';
                        @if(isset($coin_network))
                            selected = (coin.id == '{{ $coin_network }}') ? ' selected' : '';
                        @endif
                        options += `<option value="${coin.id}"${selected}>${coin.coin.coin_type}</option>`;
                    }
                });
            } else {
                options = `<option value="">{{ __("ارز یافت نشد") }}</option>`;
            }

            select.html(options);

            // اگر ارزی یافت نشد، بعد از 3 ثانیه به حالت اولیه برگرد
            if (currency.length === 0) {
                setTimeout(() => {
                    select.html(`<option value="">{{ __("انتخاب ارز") }}</option>`);
                }, 3000);
            }
        }

        @if(isset($network))
            getCurrencyByNetworkID({{ $network }});
        @endif

    </script>




    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sidebar Toggle
        document.querySelector('.sidebar-toggle')?.addEventListener('click', () => {
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // Collapse Animation
        document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]').forEach(link => {
            const target = document.querySelector(link.getAttribute('data-bs-target'));
            if (target) {
                target.addEventListener('show.bs.collapse', () => {
                    link.classList.add('active');
                });
                target.addEventListener('hide.bs.collapse', () => {
                    link.classList.remove('active');
                });
            }
        });
    </script>
    <!-- DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <!-- ApexCharts JS -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.min.js"></script>
    <script>
        // تنظیمات عمومی برای توست‌ها
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-left",
            timeOut: 5000
        };

        // فعال‌سازی تولتیپ‌ها
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

        // انیمیشن ورود برای کارت‌ها
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.classList.add('animate__animated', 'animate__fadeInUp');
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>

<!-- مودال تیکت‌ها -->
<div class="modal fade" id="ticketsModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تیکت‌های جدید</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    @forelse($pendingTickets ?? [] as $ticket)
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <h6 class="mb-1">{{ $ticket->title ?? 'عنوان تیکت' }}</h6>
                            <small class="text-muted">{{ $ticket->created_at ?? 'تاریخ' }}</small>
                        </div>
                        <p class="mb-1">{{ $ticket->content ?? 'محتوای تیکت' }}</p>
                        <div class="d-flex justify-content-end gap-2 mt-2">
                            <a href="{{ route('admin.tickets.show', $ticket->id ?? 1) }}" class="btn btn-sm btn-primary">مشاهده</a>
                            <button class="btn btn-sm btn-success">پاسخ سریع</button>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                        <p>تیکت جدیدی وجود ندارد</p>
                    </div>
                    @endforelse
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{ route('admin.tickets.index') }}" class="btn btn-primary">مشاهده همه تیکت‌ها</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال مدارک -->
<div class="modal fade" id="documentsModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">درخواست‌های تایید مدارک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    @forelse($pendingDocs ?? [] as $doc)
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <h6 class="mb-1">{{ $doc->user->name ?? 'نام کاربر' }}</h6>
                            <small class="text-muted">{{ $doc->created_at ?? 'تاریخ' }}</small>
                        </div>
                        <p class="mb-1">{{ $doc->type ?? 'نوع مدرک' }}</p>
                        <div class="d-flex justify-content-end gap-2 mt-2">
                            <a href="{{ route('admin.documents.show', $doc->id ?? 1) }}" class="btn btn-sm btn-primary">بررسی</a>
                            <button class="btn btn-sm btn-success">تایید سریع</button>
                            <button class="btn btn-sm btn-danger">رد</button>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <p>درخواست جدیدی وجود ندارد</p>
                    </div>
                    @endforelse
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{ route('admin.documents.index') }}" class="btn btn-primary">مشاهده همه درخواست‌ها</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال کاربران -->
<div class="modal fade" id="usersModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">کاربران در انتظار تایید</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    @forelse($pendingUsers ?? [] as $user)
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <h6 class="mb-1">{{ $user->name ?? 'نام کاربر' }}</h6>
                            <small class="text-muted">{{ $user->created_at ?? 'تاریخ' }}</small>
                        </div>
                        <p class="mb-1">{{ $user->email ?? 'ایمیل کاربر' }}</p>
                        <div class="d-flex justify-content-end gap-2 mt-2">
                            <a href="{{ route('admin.users.show', $user->id ?? 1) }}" class="btn btn-sm btn-primary">مشاهده پروفایل</a>
                            <button class="btn btn-sm btn-success">تایید</button>
                            <button class="btn btn-sm btn-danger">رد درخواست</button>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                        <p>کاربر جدیدی وجود ندارد</p>
                    </div>
                    @endforelse
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{ route('admin.users.index') }}" class="btn btn-primary">مشاهده همه کاربران</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>
@stack('scripts')