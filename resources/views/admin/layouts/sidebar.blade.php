
<div class="sidebar">
    <div class="sidebar-brand">
        <div class="brand-content">
            <div class="logo-wrapper">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <h1 class="brand-title">Exchangim</h1>
        </div>
    </div>

    <div class="nav-items">
        <!-- Dashboard -->
        <div class="nav-item">
            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                <i class="fas fa-chart-line"></i>
                <span>داشبورد</span>
            </a>
        </div>

        <!-- Users Management -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseUsers">
                    <i class="fas fa-users-cog"></i>
                    <span>مدیریت کاربران</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseUsers" class="collapse {{ request()->routeIs('admin.users.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.users.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.users.index') ? 'active' : '' }}">
                            <i class="fas fa-user-friends"></i>
                            <span>لیست کاربران</span>
                        </a>
                        <a href="{{ route('admin.users.create') }}"
                           class="collapse-item {{ request()->routeIs('admin.users.create') ? 'active' : '' }}">
                            <i class="fas fa-user-plus"></i>
                            <span>افزودن کاربر</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Management -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.admin-management.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseAdminManagement">
                    <i class="fas fa-user-shield"></i>
                    <span>مدیریت ادمین‌ها</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseAdminManagement" class="collapse {{ request()->routeIs('admin.admin-management.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.admin-management.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.admin-management.index') ? 'active' : '' }}">
                            <i class="fas fa-users-cog"></i>
                            <span>لیست ادمین‌ها</span>
                        </a>
                        <a href="{{ route('admin.admin-management.create') }}"
                           class="collapse-item {{ request()->routeIs('admin.admin-management.create') ? 'active' : '' }}">
                            <i class="fas fa-user-plus"></i>
                            <span>افزودن ادمین</span>
                        </a>
                        <a href="{{ route('admin.roles.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                            <i class="fas fa-user-tag"></i>
                            <span>مدیریت نقش‌ها</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Management -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.accounting.*') || request()->routeIs('admin.deposit.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseFinancial">
                    <i class="fas fa-coins"></i>
                    <span>مدیریت مالی</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseFinancial" class="collapse {{ request()->routeIs('admin.accounting.*') || request()->routeIs('admin.deposit.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.accounting.settlement.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.accounting.settlement.*') ? 'active' : '' }}">
                            <i class="fas fa-money-check-alt"></i>
                            <span>تسویه حساب‌ها</span>
                        </a>
                        <a href="{{ route('admin.accounting.settlement.info.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.accounting.settlement.info.*') ? 'active' : '' }}">
                            <i class="fas fa-info-circle"></i>
                            <span>اطلاعات تسویه</span>
                        </a>
                        <a href="{{ route('admin.deposit.check') }}"
                           class="collapse-item {{ request()->routeIs('admin.deposit.check') ? 'active' : '' }}">
                            <i class="fas fa-search-dollar"></i>
                            <span>بررسی واریز</span>
                        </a>
                        <a href="{{ route('admin.deposit.pending-history') }}"
                           class="collapse-item {{ request()->routeIs('admin.deposit.pending-history') ? 'active' : '' }}">
                            <i class="fas fa-search-dollar"></i>
                            <span>انتقال توکن</span>
                        </a>

                    </div>
                </div>
            </div>
        </div>
         <!-- برداشت تومانی -->
         <div class="nav-section">
                            <div class="nav-item">
                                <a href="#" class="nav-link {{ request()->routeIs('admin.toman-withdrawal.*') ? 'active' : '' }}"
                                   data-bs-toggle="collapse" data-bs-target="#collapseTomanWithdrawal">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>برداشت تومانی</span>
                                    @if(CountWithdrawsToman() > 0)
                                        <span class="badge bg-danger ms-auto">{{ CountWithdrawsToman() }}</span>
                                    @endif
                                    <i class="fas fa-chevron-left ms-auto"></i>
                                </a>
                                <div id="collapseTomanWithdrawal" class="collapse {{ request()->routeIs('admin.toman-withdrawal.*') ? 'show' : '' }}">
                                    <div class="collapse-inner">
                                        <a href="{{ route('admin.toman-withdrawal.pending') }}"
                                           class="collapse-item {{ request()->routeIs('admin.toman-withdrawal.pending') ? 'active' : '' }}">
                                            <i class="fas fa-clock"></i>
                                            <span>در انتظار تایید</span>
                                            @if(CountWithdrawsToman() > 0)
                                                <span class="badge bg-danger">{{ CountWithdrawsToman() }}</span>
                                            @endif
                                        </a>
                                        <a href="{{ route('admin.toman-withdrawal.approved') }}"
                                           class="collapse-item {{ request()->routeIs('admin.toman-withdrawal.approved') ? 'active' : '' }}">
                                            <i class="fas fa-list"></i>
                                            <span>واریز شده</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
        <!-- Currencies Management -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.coins.*') || request()->routeIs('admin.currencies.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseCurrencies">
                    <i class="fas fa-coins"></i>
                    <span>مدیریت ارزها</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseCurrencies" class="collapse {{ request()->routeIs('admin.coins.*') || request()->routeIs('admin.currencies.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.coins.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.coins.index') ? 'active' : '' }}">
                            <i class="fas fa-list"></i>
                            <span>لیست ارزها</span>
                        </a>
                        <a href="{{ route('admin.coins.create') }}"
                           class="collapse-item {{ request()->routeIs('admin.coins.create') ? 'active' : '' }}">
                            <i class="fas fa-plus"></i>
                            <span>افزودن ارز جدید</span>
                        </a>
                        <a href="{{ route('admin.networks.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.networks.*') ? 'active' : '' }}">
                            <i class="fas fa-network-wired"></i>
                            <span>شبکه‌های ارزی</span>
                        </a>
                        <a href="{{ route('admin.getCoinNetworkList') }}"
                           class="collapse-item {{ request()->routeIs('admin.getCoinNetworkList') || request()->routeIs('admin.createCoinNetwork') ? 'active' : '' }}">
                            <i class="fas fa-link"></i>
                            <span>ارز و شبکه</span>
                        </a>

                    </div>
                </div>
            </div>
        </div>

        <!-- کیف پول -->
        <div class="nav-item">
            <a href="{{ route('admin.wallet.index') }}"
               class="nav-link {{ request()->routeIs('admin.wallet.*') ? 'active' : '' }}">
                <i class="fas fa-wallet"></i>
                <span>کیف پول‌ها</span>
            </a>
        </div>
        <div class="nav-item">
            <a href="{{ route('admin.system-wallets.index') }}"
               class="nav-link {{ request()->routeIs('admin.system-wallets.index') ? 'active' : '' }}">
                <i class="fas fa-wallet"></i>
                <span>کیف پول مادر</span>
            </a>
        </div>
        <div class="nav-item">
            <a href="{{ route('admin.system-wallets.user-wallets') }}"
               class="nav-link {{ request()->routeIs('admin.system-wallets.user-wallets') ? 'active' : '' }}">
                <i class="fas fa-users"></i>
                <span>کیف پول‌های کاربران</span>
            </a>
        </div>

        <!-- تراکنش‌ها -->
        <div class="nav-item">
            <a href="{{ route('admin.transaction.index') }}"
               class="nav-link {{ request()->routeIs('admin.transaction.*') ? 'active' : '' }}">
                <i class="fas fa-exchange-alt"></i>
                <span>تراکنش‌ها</span>
            </a>
        </div>

        <!-- Referral Management -->
        <div class="nav-item">
            <a href="{{ route('admin.referrals.index') }}"
               class="nav-link {{ request()->routeIs('admin.referrals.*') ? 'active' : '' }}">
                <i class="fas fa-user-friends"></i>
                <span>مدیریت زیرمجموعه‌ها</span>
            </a>
        </div>

        <!-- بانک و کارت بانکی -->
        <div class="nav-item">
            <a href="#" class="nav-link {{ request()->routeIs('admin.banks.*') || request()->routeIs('admin.card.*') ? 'active' : '' }}"
               data-bs-toggle="collapse"
               data-bs-target="#collapseBanking">
                <i class="fas fa-university"></i>
                <span>مدیریت بانکی</span>
                <i class="fas fa-chevron-left ms-auto"></i>
            </a>
            <div id="collapseBanking" class="collapse {{ request()->routeIs('admin.banks.*') || request()->routeIs('admin.card.*') ? 'show' : '' }}">
                <div class="collapse-inner">
                    <a href="{{ route('admin.banks.index') }}"
                       class="collapse-item {{ request()->routeIs('admin.banks.*') ? 'active' : '' }}">
                        <i class="fas fa-university"></i>
                        <span>مدیریت بانک‌ها</span>
                    </a>
                    <a href="{{ route('admin.card.index') }}"
                       class="collapse-item {{ request()->routeIs('admin.card.*') ? 'active' : '' }}">
                        <i class="fas fa-credit-card"></i>
                        <span>کارت‌های بانکی</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- مدیریت اسناد -->
        <div class="nav-item">
            <a href="{{ route('admin.document.index') }}"
               class="nav-link {{ request()->routeIs('admin.document.*') ? 'active' : '' }}">
                <i class="fas fa-file-alt"></i>
                <span>مدیریت اسناد</span>
            </a>
        </div>

        <!-- پشتیبانی -->
        <div class="nav-item">
            <a href="{{ route('admin.tickets.index') }}"
               class="nav-link {{ request()->routeIs('admin.tickets.*') ? 'active' : '' }}">
                <i class="fas fa-headset"></i>
                <span>پشتیبانی</span>
            </a>
        </div>

        <!-- تنظیمات -->
        <div class="nav-item">
            <a href="#" class="nav-link {{ request()->routeIs('admin.setting.*') ? 'active' : '' }}"
               data-bs-toggle="collapse" data-bs-target="#collapseSettings">
                <i class="fas fa-cog"></i>
                <span>تنظیمات</span>
                <i class="fas fa-chevron-left ms-auto"></i>
            </a>
            <div id="collapseSettings" class="collapse {{ request()->routeIs('admin.setting.*') ? 'show' : '' }}">
                <div class="collapse-inner">
                    <a href="{{ route('admin.setting.level.index') }}"
                       class="collapse-item {{ request()->routeIs('admin.setting.level.*') ? 'active' : '' }}">
                        <i class="fas fa-layer-group"></i>
                        <span>سطوح</span>
                    </a>
                    <a href="{{ route('admin.setting.index') }}"
                       class="collapse-item {{ request()->routeIs('admin.setting.index') ? 'active' : '' }}">
                        <i class="fas fa-sliders-h"></i>
                        <span>تنظیمات کلی</span>
                    </a>
                    <a href="{{ route('admin.networks.index') }}"
                       class="collapse-item {{ request()->routeIs('admin.networks.*') ? 'active' : '' }}">
                        <i class="fas fa-network-wired"></i>
                        <span>شبکه‌ها</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- سوالات متداول -->
        <div class="nav-item">
            <a href=""
               class="nav-link">
                <i class="fas fa-question-circle"></i>
                <span>سوالات متداول</span>
            </a>
        </div>

        <!-- لاگ فعالیت‌ها -->
        <div class="nav-item">
            <a href="{{ route('admin.activities.index') }}"
               class="nav-link {{ request()->routeIs('admin.activities.*') ? 'active' : '' }}">
                <i class="fas fa-history"></i>
                <span>لاگ فعالیت‌ها</span>
            </a>
        </div>

        <!-- Jibit -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.jibit.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseJibit">
                    <i class="fas fa-credit-card"></i>
                    <span>جیبیت</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseJibit" class="collapse {{ request()->routeIs('admin.jibit.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.jibit.waiting_labeled_deposits') }}" class="collapse-item {{ request()->routeIs('admin.jibit.waiting_labeled_deposits') ? 'active' : '' }}">
                            <i class="far fa-circle nav-icon"></i>
                            <span>لیست واریزهای شناسه‌دار در انتظار لیبل</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Blog Management -->
        <div class="nav-section">
            <div class="nav-item">
                <a href="#" class="nav-link {{ request()->routeIs('admin.blog.posts.*') || request()->routeIs('admin.blog.categories.*') || request()->routeIs('admin.blog.tags.*') ? 'active' : '' }}"
                   data-bs-toggle="collapse" data-bs-target="#collapseBlog">
                    <i class="fas fa-blog"></i>
                    <span>مدیریت وبلاگ</span>
                    <i class="fas fa-chevron-left ms-auto"></i>
                </a>
                <div id="collapseBlog" class="collapse {{ request()->routeIs('admin.blog.posts.*') || request()->routeIs('admin.blog.categories.*') || request()->routeIs('admin.blog.tags.*') ? 'show' : '' }}">
                    <div class="collapse-inner">
                        <a href="{{ route('admin.blog.posts.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.blog.posts.*') ? 'active' : '' }}">
                            <i class="fas fa-file-alt"></i>
                            <span>پست‌ها</span>
                        </a>
                        <a href="{{ route('admin.blog.categories.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.blog.categories.*') ? 'active' : '' }}">
                            <i class="fas fa-folder-open"></i>
                            <span>دسته‌بندی‌ها</span>
                        </a>
                        <a href="{{ route('admin.blog.tags.index') }}"
                           class="collapse-item {{ request()->routeIs('admin.blog.tags.*') ? 'active' : '' }}">
                            <i class="fas fa-tags"></i>
                            <span>تگ‌ها</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    width: 280px;
    background: #1E1E2D;
    color: #9899ac;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    padding: 1.25rem;
    overflow-y: auto;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.sidebar-brand {
    margin-bottom: 2rem;
}

.brand-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
}

.logo-wrapper {
    width: 40px;
    height: 40px;
    background: #3699FF;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-wrapper i {
    color: white;
    font-size: 1.25rem;
}

.brand-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin: 0;
}

.nav-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #2B2B40;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #9899ac;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.nav-link:hover {
    background: #1b1b28;
    color: #fff;
}

.nav-link.active {
    background: #1b1b28;
    color: #3699FF;
}

.nav-link i:first-child {
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
}

.collapse-inner {
    padding: 0.5rem 0 0.5rem 2rem;
}

.collapse-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #9899ac;
    border-radius: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.collapse-item:hover {
    color: #fff;
}

.collapse-item.active {
    color: #3699FF;
}

.collapse-item i {
    margin-left: 0.75rem;
    width: 16px;
    text-align: center;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: #1E1E2D;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #2B2B40;
    border-radius: 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }
}
</style>
