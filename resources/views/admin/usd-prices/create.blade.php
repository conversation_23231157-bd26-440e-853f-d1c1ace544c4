@extends('admin.layouts.app')

@section('title', 'افزودن قیمت دلار جدید')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">افزودن قیمت دلار جدید</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.usd-prices.store') }}" method="POST">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="buy_price" class="form-label">قیمت خرید دلار (تومان)</label>
                            <input type="number" class="form-control @error('buy_price') is-invalid @enderror" id="buy_price" name="buy_price" value="{{ old('buy_price') }}" required>
                            @error('buy_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">قیمتی که سیستم با آن دلار را از کاربر می‌خرد</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sell_price" class="form-label">قیمت فروش دلار (تومان)</label>
                            <input type="number" class="form-control @error('sell_price') is-invalid @enderror" id="sell_price" name="sell_price" value="{{ old('sell_price') }}" required>
                            @error('sell_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">قیمتی که سیستم با آن دلار را به کاربر می‌فروشد</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="site_profit_per_usd" class="form-label">سود سایت به ازای هر دلار (تومان)</label>
                            <input type="number" class="form-control @error('site_profit_per_usd') is-invalid @enderror" id="site_profit_per_usd" name="site_profit_per_usd" value="{{ old('site_profit_per_usd') }}" step="0.01">
                            @error('site_profit_per_usd')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">مقدار سود سایت به ازای هر دلار معامله (مثلاً ۲۰۰ تومان)</small>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {{ old('is_active') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">فعال</label>
                            <small class="form-text text-muted d-block">اگر این گزینه را انتخاب کنید، این قیمت به عنوان قیمت فعال تنظیم می‌شود و قیمت‌های فعال قبلی غیرفعال می‌شوند</small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">ذخیره</button>
                            <a href="{{ route('admin.usd-prices.index') }}" class="btn btn-secondary">انصراف</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
