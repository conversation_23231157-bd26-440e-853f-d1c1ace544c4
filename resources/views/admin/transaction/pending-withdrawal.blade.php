@extends('admin.layouts.app')

@section('styles')
<style>
    @import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;700&display=swap');
    body, .container-fluid, .card, .table, .btn {
        font-family: 'Vazirmatn', Tahoma, Arial, sans-serif !important;
    }
    .user-info-item {
        margin-bottom: 15px;
    }
    .user-info-item label {
        font-weight: bold;
        color: #6c757d;
        margin-bottom: 5px;
        display: block;
    }
    .user-info-item p {
        margin: 0;
        font-size: 0.95rem;
    }
    .glass-card {
        background: rgba(255,255,255,0.18);
        box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-radius: 22px;
        border: 1.5px solid rgba(255,255,255,0.25);
        overflow: hidden;
        position: relative;
    }
    .stats-card {
        border: none;
        border-radius: 22px;
        box-shadow: 0 8px 32px 0 rgba(31,38,135,0.18);
        background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
    }
    .stats-card .card-body {
        padding: 2.2rem 1.5rem 1.5rem 1.5rem;
        position: relative;
        z-index: 2;
    }
    .stats-card .icon-bg {
        position: absolute;
        left: 50%;
        top: -30px;
        transform: translateX(-50%);
        opacity: 0.13;
        font-size: 6rem;
        z-index: 1;
    }
    .stats-card:hover {
        transform: translateY(-10px) scale(1.04);
        box-shadow: 0 16px 48px rgba(41,121,255,0.13);
    }
    .filter-card {
        background: rgba(255,255,255,0.22);
        border-radius: 18px;
        padding: 28px 24px 18px 24px;
        margin-bottom: 32px;
        box-shadow: 0 2px 16px rgba(41,121,255,0.07);
        border: 1.5px solid rgba(41,121,255,0.08);
    }
    .table {
        border-radius: 18px;
        overflow: hidden;
        background: transparent;
        box-shadow: none;
    }
    .table thead th {
        background: linear-gradient(90deg, #e3f0ff 60%, #f8fafc 100%);
        color: #1976d2;
        font-weight: 800;
        font-size: 17px;
        border-bottom: none;
    }
    .table-hover tbody tr {
        transition: box-shadow 0.2s, background 0.2s;
    }
    .table-hover tbody tr:hover {
        background: rgba(41,121,255,0.07);
        box-shadow: 0 4px 24px rgba(41,121,255,0.09);
        border-radius: 16px;
    }
    .table tbody tr {
        background: rgba(255,255,255,0.85);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(41,121,255,0.04);
        margin-bottom: 8px;
    }
    .table td, .table th {
        vertical-align: middle !important;
        font-size: 16px;
        border: none;
    }
    .amount-col {
        font-family: 'Courier New', monospace;
        font-weight: 800;
        color: #1976d2;
        font-size: 1.12rem;
        letter-spacing: 0.5px;
    }
    .coin-icon-glow {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #fff;
        box-shadow: 0 0 0 4px #e3f0ff, 0 0 16px 2px #1976d2;
        margin-left: 8px;
        transition: box-shadow 0.3s;
    }
    .badge-soft-primary {
        background: linear-gradient(90deg, #e3f0ff 60%, #f8fafc 100%);
        color: #000 !important;
        font-weight: 900;
        font-size: 17px;
        padding: 8px 20px;
        border-radius: 22px;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        box-shadow: 0 2px 12px rgba(41,121,255,0.13), 0 0 8px #fff;
        letter-spacing: 0.5px;
        transition: color 0.2s;
    }
    .badge-soft-info {
        background: linear-gradient(90deg, #e0f7fa 60%, #f8fafc 100%);
        color: #00838f;
        font-weight: 700;
        font-size: 16px;
        padding: 8px 18px;
        border-radius: 20px;
    }
    .action-buttons .btn {
        border-radius: 50%;
        width: 42px;
        height: 42px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin: 0 4px;
        background: linear-gradient(135deg, #e3f0ff 60%, #f8fafc 100%);
        color: #1976d2;
        box-shadow: 0 2px 8px rgba(41,121,255,0.09);
        border: none;
        transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.15s;
        position: relative;
    }
    .action-buttons .btn:hover {
        background: linear-gradient(135deg, #1976d2 60%, #1de9b6 100%);
        color: #fff;
        box-shadow: 0 4px 16px rgba(41,121,255,0.18);
        transform: scale(1.13) rotate(-6deg);
    }
    .action-buttons .btn[title]:hover:after {
        content: attr(title);
        position: absolute;
        right: 50%;
        top: -38px;
        background: #222;
        color: #fff;
        padding: 6px 16px;
        border-radius: 10px;
        font-size: 13px;
        white-space: nowrap;
        z-index: 10;
        opacity: 0.97;
        box-shadow: 0 2px 8px rgba(0,0,0,0.13);
    }
    .btn-link.p-0 {
        color: #1976d2;
        font-size: 20px;
        margin-right: 2px;
        border-radius: 50%;
        background: #f8fafc;
        transition: background 0.2s, color 0.2s;
    }
    .btn-link.p-0:hover {
        color: #0d47a1;
        background: #e3f0ff;
    }
    .code.ml-2 {
        font-size: 15px;
        color: #37474f;
        background: #f5f5f5;
        border-radius: 8px;
        padding: 3px 10px;
        margin-left: 10px;
        font-weight: 600;
    }
    .table td .d-flex.align-items-center {
        gap: 4px;
    }
    @media (max-width: 900px) {
        .stats-card .icon-bg { font-size: 3.5rem; }
        .stats-card .card-body { padding: 1.2rem; }
        .table td, .table th { font-size: 14px; }
        .badge-soft-primary, .badge-soft-info { font-size: 13px; padding: 6px 10px; }
    }
</style>
@endsection

@section('content')
@php
    use SimpleSoftwareIO\SimpleQrcode\Facades\QrCode;
@endphp
<div class="container-fluid">
    <!-- کارت‌های آمار -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد در انتظار') }}</h5>
                    <h2 class="mb-0">{{ $stats['total_pending'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-success text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($stats['total_amount'] ?? 0, 2) }}</h2>
                    <div class="small text-white-50">تتر</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-warning text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('میانگین مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($stats['avg_amount'] ?? 0, 2) }}</h2>
                    <div class="small text-white-50">تتر</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-info text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('کاربران منحصر به فرد') }}</h5>
                    <h2 class="mb-0">{{ $stats['unique_users'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- اطلاعات کاربر -->
    @if(isset($user))
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">{{ __('اطلاعات کاربر') }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('نام و نام خانوادگی') }}:</label>
                        <p>{{ $user->firstname }} {{ $user->lastname }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('کد ملی') }}:</label>
                        <p>{{ $user->national_id }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('ایمیل') }}:</label>
                        <p>{{ $user->email }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('شماره تماس') }}:</label>
                        <p>{{ $user->phone }}</p>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('جنسیت') }}:</label>
                        <p>{{ __($user->gender) }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('تاریخ تولد') }}:</label>
                        <p>{{ $user->birth_date ? \Hekmatinasser\Verta\Verta::instance($user->birth_date)->format('Y/m/d') : '-' }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('وضعیت') }}:</label>
                        <p><span class="badge badge-{{ $user->status === 'active' ? 'success' : 'warning' }}">{{ __($user->status) }}</span></p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('تاریخ عضویت') }}:</label>
                        <p>{{ \Hekmatinasser\Verta\Verta::instance($user->created_at)->format('Y/m/d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- فیلترها -->
    <div class="card filter-card">
        <form method="GET" action="{{ route('admin.withdrawal.adminPendingWithdrawal') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('نوع ارز') }}</label>
                        <select name="coin_type" class="form-control">
                            <option value="">{{ __('همه') }}</option>
                            @foreach($coinTypes as $coin)
                                <option value="{{ $coin }}" {{ request('coin_type') == $coin ? 'selected' : '' }}>
                                    {{ $coin }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('شبکه') }}</label>
                        <select name="network" class="form-control">
                            <option value="">{{ __('همه') }}</option>
                            @foreach($networks as $network)
                                <option value="{{ $network }}" {{ request('network') == $network ? 'selected' : '' }}>
                                    {{ $network }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('حداقل مبلغ') }}</label>
                        <input type="number" name="min_amount" class="form-control" value="{{ request('min_amount') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('حداکثر مبلغ') }}</label>
                        <input type="number" name="max_amount" class="form-control" value="{{ request('max_amount') }}">
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-left">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> {{ __('فیلتر') }}
                    </button>
                    <a href="{{ route('admin.withdrawal.adminPendingWithdrawal') }}" class="btn btn-secondary">
                        <i class="fas fa-redo"></i> {{ __('بازنشانی') }}
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- جدول اصلی -->
    <div class="card">
        <div class="card-header bg-white">
            <div class="row align-items-center">
                <div class="col">
                    <h4 class="mb-0">{{ __('برداشت‌های در انتظار') }}</h4>
                </div>
                <div class="col text-left">
                    <button class="btn btn-sm btn-success" onclick="return confirm('{{ __('آیا از تایید همه موارد انتخاب شده مطمئن هستید؟') }}')">
                        <i class="fas fa-check-circle"></i> {{ __('تایید انتخاب شده‌ها') }}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="return confirm('{{ __('آیا از رد همه موارد انتخاب شده مطمئن هستید؟') }}')">
                        <i class="fas fa-times-circle"></i> {{ __('رد انتخاب شده‌ها') }}
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th width="20">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>{{ __('کاربر') }}</th>
                            <th>{{ __('مبلغ') }}</th>
                            <th>{{ __('ارز') }}</th>
                            <th>{{ __('آدرس') }}</th>
                            <th>{{ __('تاریخ ایجاد') }}</th>
                            <th>{{ __('عملیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>
                                    <input type="checkbox" class="withdrawal-checkbox" value="{{ $withdrawal->id }}">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong>{{ $withdrawal->user->email }}</strong>
                                            <div class="small text-muted">شناسه: {{ $withdrawal->user->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="amount-col">
                                    {{ number_format($withdrawal->amount, 8) }}
                                </td>
                                <td>
                                    <span class="badge badge-soft-primary d-flex align-items-center">
                                        @if($withdrawal->coin && $withdrawal->coin->coin_icon)
                                            <img src="{{ asset('storage/' . $withdrawal->coin->coin_icon) }}"  class="coin-icon-glow">
                                        @endif
                                    </span>
                                        {{ $withdrawal->coin_type }}
                                </td>
                               
                                <td>
                                    <div class="d-flex align-items-center">
                                        <code class="ml-2">{{ Str::limit($withdrawal->address, 20) }}</code>
                                        <button class="btn btn-sm btn-link p-0" onclick="copyToClipboard('{{ $withdrawal->address }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</div>
                                    <div class="small text-muted">
                                        {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->formatDifference() }}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons" style="position:relative;">
                                        <form action="{{ route('admin.withdrawal.adminAcceptPendingWithdrawal', $withdrawal->id) }}"
                                              method="GET"
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit"
                                                    class="btn btn-sm btn-success"
                                                    onclick="return confirm('{{ __('آیا از تایید این برداشت مطمئن هستید؟') }}')"
                                                    title="تایید خودکار">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>

                                        <form action="{{ route('admin.withdrawal.adminRejectPendingWithdrawal', encrypt($withdrawal->id)) }}"
                                              method="GET"
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit"
                                                    class="btn btn-sm btn-danger"
                                                    onclick="return confirm('{{ __('آیا از رد این برداشت مطمئن هستید؟') }}')"
                                                    title="رد">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>

                                        <form action="{{ route('admin.withdrawal.manualAcceptPendingWithdrawal', $withdrawal->id) }}"
                                              method="GET"
                                              style="display: inline;" class="manual-accept-form">
                                            @csrf
                                            <button type="button"
                                                    class="btn btn-sm btn-warning manual-accept-btn"
                                                    data-id="{{ $withdrawal->id }}"
                                                    data-amount="{{ number_format($withdrawal->amount, 8) }}"
                                                    data-address="{{ $withdrawal->address }}"
                                                    data-coin="{{ $withdrawal->coin_type }}"
                                                    title="تایید و واریز دستی">
                                                <i class="fas fa-hand-holding-usd"></i>
                                            </button>
                                        </form>

                                        <a href="{{ route('admin.withdrawal.adminWithdrawalDetails', $withdrawal->id) }}"
                                           class="btn btn-sm btn-info" title="جزئیات">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                    <div class="user-tooltip-box mt-2 text-center" style="background:rgba(41,121,255,0.07);border-radius:14px;box-shadow:0 4px 24px rgba(41,121,255,0.13);padding:10px 18px 8px 18px;min-width:180px;display:inline-block;text-align:right;">
                                        <div style="color:#1976d2;font-weight:700;font-size:15px;"><i class="fas fa-user-circle mr-1"></i>{{ $withdrawal->user->firstname ?? '' }} {{ $withdrawal->user->lastname ?? '' }}</div>
                                        <div style="color:#37474f;font-size:13px;direction:ltr;margin-top:2px;">{{ $withdrawal->user->email }}</div>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ برداشت در انتظاری یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($withdrawals->hasPages())
            <div class="card-footer bg-white">
                {{ $withdrawals->links() }}
            </div>
        @endif
    </div>
</div>


@endsection

@push('scripts')
<script>
document.getElementById('select-all').addEventListener('change', function() {
    document.querySelectorAll('.withdrawal-checkbox').forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('کپی شد!');
    });
}

// Modal for manual accept
if (!document.getElementById('manualAcceptModal')) {
    const modalHtml = `
    <div class="modal fade" id="manualAcceptModal" tabindex="-1" role="dialog" aria-labelledby="manualAcceptModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="manualAcceptModalLabel">تایید و واریز دستی</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="$('#manualAcceptModal').modal('hide')">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <strong>مقدار برداشت:</strong>
              <span id="manual-amount" style="font-family:monospace;font-size:1.1rem;"></span>
              <span id="manual-coin"></span>
            </div>
            <div class="mb-3">
              <strong>آدرس مقصد:</strong>
              <span style="display:flex;align-items:center;gap:8px;">
                <code id="manual-address" style="font-size:1rem;"></code>
                <button type="button" id="copy-manual-address" class="btn btn-sm btn-outline-primary" title="کپی آدرس"><i class="fas fa-copy"></i></button>
              </span>
              <div class="text-center mt-3">
                <div id="manual-address-qr-wrapper"></div>
                <div class="small text-muted mt-1">بارکد آدرس جهت اسکن</div>
              </div>
            </div>
            <div class="alert alert-warning mb-0">لطفاً پس از واریز دستی، تایید را بزنید.</div>
          </div>
          <div class="modal-footer">
            <form id="manual-accept-final-form" method="GET">
              @csrf
              <button type="submit" class="btn btn-warning">تایید واریز دستی</button>
              <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="$('#manualAcceptModal').modal('hide')">انصراف</button>
            </form>
          </div>
        </div>
      </div>
    </div>`;
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// jQuery required for modal (Bootstrap)
document.querySelectorAll('.manual-accept-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const id = this.getAttribute('data-id');
        const amount = this.getAttribute('data-amount');
        const address = this.getAttribute('data-address');
        const coin = this.getAttribute('data-coin');
        document.getElementById('manual-amount').innerText = amount;
        document.getElementById('manual-address').innerText = address;
        document.getElementById('manual-coin').innerText = coin;
        document.getElementById('manual-accept-final-form').action = `/admin/withdrawal/manual-accept/${id}`;
        // Set QR code using ajax to a blade route
        const qrWrapper = document.getElementById('manual-address-qr-wrapper');
        qrWrapper.innerHTML = '<div class="text-muted">در حال ساخت بارکد...</div>';
        fetch(`/admin/withdrawal/qr-code?address=${encodeURIComponent(address)}`)
            .then(res => res.text())
            .then(svg => {
                qrWrapper.innerHTML = svg;
            });
        $('#manualAcceptModal').modal('show');
    });
});

// Copy address button
if (document.getElementById('copy-manual-address')) {
    document.getElementById('copy-manual-address').onclick = function() {
        const address = document.getElementById('manual-address').innerText;
        navigator.clipboard.writeText(address).then(() => {
            this.innerHTML = '<i class="fas fa-check"></i>';
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-success');
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-copy"></i>';
                this.classList.remove('btn-success');
                this.classList.add('btn-outline-primary');
            }, 1200);
        });
    };
}

// User info tooltip on hover
const tooltipParents = document.querySelectorAll('.user-tooltip-parent');
tooltipParents.forEach(parent => {
    parent.addEventListener('mouseenter', function() {
        const tooltip = this.querySelector('.user-tooltip-box');
        if(tooltip) tooltip.style.display = 'block';
    });
    parent.addEventListener('mouseleave', function() {
        const tooltip = this.querySelector('.user-tooltip-box');
        if(tooltip) tooltip.style.display = 'none';
    });
});
</script>
@endpush
