# سیستم مدیریت ادمین‌ها

این سیستم امکان مدیریت کامل ادمین‌ها و دسترسی‌هایشان را در پنل مدیریت فراهم می‌کند.

## ویژگی‌ها

### 🔐 مدیریت ادمین‌ها
- **لیست ادمین‌ها**: مشاهده تمام ادمین‌های سیستم با امکان جستجو و فیلتر
- **افزودن ادمین جدید**: ایجاد حساب کاربری جدید برای ادمین‌ها
- **ویرایش ادمین**: بروزرسانی اطلاعات ادمین‌ها
- **حذف ادمین**: حذف ادمین‌ها (به جز سوپر ادمین‌ها)
- **فعال/غیرفعال کردن**: تغییر وضعیت ادمین‌ها

### 🔑 مدیریت دسترسی‌ها
- **تنظیم دسترسی‌ها**: اختصاص دسترسی‌های مختلف به ادمین‌ها
- **دسته‌بندی دسترسی‌ها**: دسترسی‌ها بر اساس بخش‌های مختلف دسته‌بندی شده‌اند
- **انتخاب گروهی**: امکان انتخاب/لغو انتخاب دسترسی‌ها به صورت گروهی
- **نمایش وضعیت**: نمایش وضعیت فعلی دسترسی‌های هر ادمین

### 🎨 رابط کاربری
- **طراحی ریسپانسیو**: سازگار با تمام دستگاه‌ها
- **رابط فارسی**: کاملاً فارسی‌سازی شده
- **آیکون‌ها و بج‌ها**: استفاده از آیکون‌ها و بج‌های رنگی برای بهتر نمایش اطلاعات
- **جستجوی پیشرفته**: امکان جستجو بر اساس نام، ایمیل، تلفن و کد ملی

## فایل‌های ایجاد شده

### کنترلرها
- `app/Http/Controllers/Admin/AdminManagementController.php`

### مدل‌ها
- `app/Models/Permission.php`
- `app/Models/PermissionFromData.php`

### ویوها
- `resources/views/admin/admin-management/index.blade.php`
- `resources/views/admin/admin-management/create.blade.php`
- `resources/views/admin/admin-management/edit.blade.php`
- `resources/views/admin/admin-management/show.blade.php`
- `resources/views/admin/admin-management/permissions.blade.php`

### مایگریشن‌ها
- `database/migrations/2024_12_24_000000_create_permission_from_data_table.php`
- `database/migrations/2024_12_24_000001_create_custom_permissions_table.php`

### سیدرها
- `database/seeders/DefaultRolesSeeder.php`
- `database/seeders/PermissionFromDataSeeder.php`

## نصب و راه‌اندازی

### 1. اجرای مایگریشن‌ها
```bash
php artisan migrate
```

### 2. اجرای سیدرها
```bash
php artisan db:seed --class=DefaultRolesSeeder
php artisan db:seed --class=PermissionFromDataSeeder
```

### 3. دسترسی به سیستم
پس از نصب، می‌توانید از طریق منوی "مدیریت ادمین‌ها" در پنل مدیریت به این بخش دسترسی پیدا کنید.

## استفاده

### افزودن ادمین جدید
1. به بخش "مدیریت ادمین‌ها" بروید
2. روی "افزودن ادمین جدید" کلیک کنید
3. اطلاعات مورد نیاز را وارد کنید
4. نقش مناسب را انتخاب کنید
5. روی "ذخیره ادمین" کلیک کنید

### تنظیم دسترسی‌ها
1. در لیست ادمین‌ها، روی آیکون "کلید" کلیک کنید
2. دسترسی‌های مورد نیاز را انتخاب کنید
3. روی "ذخیره دسترسی‌ها" کلیک کنید

### ویرایش ادمین
1. در لیست ادمین‌ها، روی آیکون "ویرایش" کلیک کنید
2. اطلاعات مورد نظر را تغییر دهید
3. روی "بروزرسانی اطلاعات" کلیک کنید

## امنیت

### محافظت از سوپر ادمین‌ها
- سوپر ادمین‌ها قابل حذف نیستند
- نقش و وضعیت سوپر ادمین‌ها قابل تغییر نیست
- سوپر ادمین‌ها به همه بخش‌ها دسترسی کامل دارند

### اعتبارسنجی
- تمام ورودی‌ها اعتبارسنجی می‌شوند
- ایمیل و شماره تلفن باید منحصر به فرد باشند
- رمز عبور باید حداقل ۸ کاراکتر باشد

## دسترسی‌های تعریف شده

سیستم شامل دسترسی‌های زیر است:

- **داشبورد**: مشاهده داشبورد
- **مدیریت کاربران**: مشاهده، ایجاد، ویرایش و حذف کاربران
- **مدیریت ادمین‌ها**: مدیریت کامل ادمین‌ها و دسترسی‌هایشان
- **مدیریت مالی**: مشاهده تسویه حساب، واریزی‌ها و برداشت‌ها
- **مدیریت تراکنش‌ها**: مشاهده و ویرایش تراکنش‌ها
- **پشتیبانی**: مشاهده و پاسخ به تیکت‌ها
- **تنظیمات**: مشاهده و ویرایش تنظیمات سیستم
- **مدیریت ارزها**: مدیریت کامل ارزهای دیجیتال
- **مدیریت شبکه‌ها**: مدیریت شبکه‌های بلاک‌چین
- **مدیریت مدارک**: تأیید و رد مدارک کاربران
- **گزارش‌ها**: مشاهده و خروجی گزارش‌ها
- **مدیریت بلاگ**: مدیریت پست‌های بلاگ
- **قالب‌های پیامک**: مدیریت قالب‌های پیامک
- **زیرمجموعه‌ها**: مشاهده سیستم ارجاع

## پشتیبانی

در صورت بروز مشکل یا نیاز به راهنمایی بیشتر، با تیم توسعه تماس بگیرید.

## نکات مهم

1. **پشتیبان‌گیری**: قبل از هر تغییری، از دیتابیس پشتیبان بگیرید
2. **تست**: تمام عملیات را در محیط تست آزمایش کنید
3. **دسترسی‌ها**: دسترسی‌ها را با دقت تنظیم کنید
4. **رمز عبور**: از رمزهای عبور قوی استفاده کنید
5. **نظارت**: فعالیت ادمین‌ها را مرتب بررسی کنید
