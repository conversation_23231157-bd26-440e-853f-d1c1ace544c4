<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'level_id',
        'previous_level_id',
        'upgraded_at',
        'total_purchases_at_upgrade',
        'upgrade_reason',
        'is_automatic'
    ];

    protected $casts = [
        'upgraded_at' => 'datetime',
        'total_purchases_at_upgrade' => 'decimal:2',
        'is_automatic' => 'boolean',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function previousLevel()
    {
        return $this->belongsTo(Level::class, 'previous_level_id');
    }

    // Helper methods
    public function getDaysSinceUpgradeAttribute()
    {
        return $this->upgraded_at->diffInDays(now());
    }

    public function getHoursSinceUpgradeAttribute()
    {
        return $this->upgraded_at->diffInHours(now());
    }

    public function canUpgradeToNextLevel()
    {
        $currentLevel = $this->level;
        
        if (!$currentLevel) {
            return false; // سطح فعلی وجود ندارد
        }
        
        $nextLevel = Level::where('priority', $currentLevel->priority + 1)->first();
        
        if (!$nextLevel) {
            return false; // سطح بعدی وجود ندارد
        }

        // بررسی شرایط ارتقا
        $levelUpgradeService = app(\App\Services\LevelUpgradeService::class);
        return $levelUpgradeService->canUserUpgrade($this->user, $nextLevel);
    }
}
