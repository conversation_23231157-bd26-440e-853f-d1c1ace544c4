<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Services\BlockchainBalanceService;

class AdminWalletKey extends Model
{
    use HasFactory;
    protected $fillable = [
        "uid",
        "network_id",
        "address",
        "pv",
        "creation_type",
        "status",
    ];

    protected $appends = ['balance'];

    public function network()
    {
        return $this->belongsTo(Network::class, "network_id");
    }

    /**
     * Get wallet balance from blockchain
     */
    public function getBalanceAttribute()
    {
        try {
            if (!$this->attributes['address'] || !$this->network) {
                return '0.00000000';
            }

            $address = $this->attributes['address'];
            $networkType = $this->network->base_type ?? 'evm';
            $networkId = $this->attributes['network_id'];

            return BlockchainBalanceService::getBalance($address, $networkType, $networkId);
        } catch (\Exception $e) {
            \Log::error('Error getting wallet balance: ' . $e->getMessage());
            return '0.00000000';
        }
    }


}
