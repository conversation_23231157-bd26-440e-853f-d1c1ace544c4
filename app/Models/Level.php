<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Level extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'daily_buy_limit' => 'decimal:2',
        'daily_sell_limit' => 'decimal:2',
        'daily_withdrawal_limit' => 'decimal:2',
        'total_purchase_requirement' => 'decimal:2',
        'is_unlimited_buy' => 'boolean',
        'is_unlimited_sell' => 'boolean',
        'active' => 'boolean',
    ];

    public function levelItems()
    {
        return $this->belongsToMany(
            related: LevelItem::class,
            table: 'level_type_values',
            foreignPivotKey: 'level_id',
            relatedPivotKey: 'level_item_id'
        )->distinct();
    }

    // Helper methods
    public function getFormattedDailyBuyLimitAttribute()
    {
        if ($this->is_unlimited_buy) {
            return 'نامحدود';
        }
        return $this->daily_buy_limit ? number_format($this->daily_buy_limit) . ' تومان' : '-';
    }

    public function getFormattedDailySellLimitAttribute()
    {
        if ($this->is_unlimited_sell) {
            return 'نامحدود';
        }
        return $this->daily_sell_limit ? number_format($this->daily_sell_limit) . ' تومان' : '-';
    }

    public function getFormattedDailyWithdrawalLimitAttribute()
    {
        return $this->daily_withdrawal_limit ? number_format($this->daily_withdrawal_limit) . ' تومان' : 'نامحدود';
    }

    public function getFormattedTotalPurchaseRequirementAttribute()
    {
        return $this->total_purchase_requirement ? number_format($this->total_purchase_requirement) . ' تومان' : '-';
    }

    public function getFormattedDaysFromPreviousLevelAttribute()
    {
        if ($this->days_from_previous_level == 0) {
            return '-';
        }
        $days = $this->days_from_previous_level;
        if ($days >= 24) {
            return floor($days / 24) . ' روز';
        }
        return $days . ' ساعت';
    }
}
