<?php

namespace App\CurrencyProviders;

use App\Contracts\CurrencyProvider;
use App\Models\UserWallet;
use Illuminate\Support\Facades\Log;
use PavloDotDev\LaravelTronModule\Api\Api;
use PavloDotDev\LaravelTronModule\Models\TronAddress;
use PavloDotDev\LaravelTronModule\Models\TronTRC20;
use PavloDotDev\LaravelTronModule\Models\TronWallet;
use PavloDotDev\LaravelTronModule\Tron;
use Illuminate\Validation\ValidationException;

class TronProvider implements CurrencyProvider
{

    // protected $walletId;

    public function __construct(
        // $walletId
    ){
        // $this->walletId = $walletId;
    }

    public function buy(array $data): array|false
    {
        return [];
    }

    public function sell(array $data): array|false
    {
        return [];
    }

    public function balance(): float
    {
        return (float)1;
    }

    public function deposit(array $data): array|false
    {
        // $userId = $data['user_id'];
        // $mnemonic = Tron::mnemonicGenerate(15);
        // $isValid = Tron::mnemonicValidate($mnemonic);
        // if($isValid){
        //     $tronWallet = $this->wallet($userId);

        //     $tronAddress = $this->address($tronWallet);

        //     return [
        //         'address' => $tronAddress->address,
        //         'trc20Balance' => $this->trc20Balance($tronAddress->address),
        //         'trxBalance' => $this->trxBalance($tronAddress->address),

        //     ];

        // }
        return [];
    }

    public function withdraw(array $data): array|false
    {
        return [];
    }

    public function transfer($data){
        $userId = $data['user_id'];


        $wallet = $this->wallet($userId);
        $address = $this->address($wallet);

        $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        $from = $address->address;
        $to = $data['to'];
        $amount = $data['amount'];


        $privateKey = $wallet->encrypted()->decode($address->private_key);

        $api = new Api();
        $tron = new Tron($api);
        $transfer = $tron->api()->transferTRC20($contractAddress, $from, $to, $amount);

        // $preview = $transfer->preview();
        // $swap = $this->swapTrc20ToTrx($from, $address->private_key, $amount);

        $fromAccount = $tron->api()->getAccount(address: $from);
        if (!$fromAccount->activated) {
            throw ValidationException::withMessages(["آدرس فرستنده فعال نیست. لطفاً مقداری TRX به این آدرس ارسال کنید تا فعال شود."]);
        }

        $send = $transfer->send($privateKey);
        print_r($send->toArray());

    }

    public function wallet($userId){
        // $mnemonic = Tron::mnemonicGenerate(15);
        // $name = "user-$userId-wallet";
        // $password = "user-$userId-wallet-password-**********";
        // $userWallet = UserWallet::where('user_id', $userId)->where('currency_id',5)->first();
        // if(!$userWallet){
        //     $mnemonicPassphrase = null;
        //     $tronWallet = Tron::createWallet($name, $password, $mnemonic, $mnemonicPassphrase);
        //     $tronWallet->save();
        //     UserWallet::create([
        //         'user_id' => $userId,
        //         'wallet_id' => $tronWallet->id,
        //         'currency_id' => 5,
        //     ]);
        // } else {
        //     $tronWallet = TronWallet::find($userWallet->wallet_id);
        // }
        // $tronWallet->encrypted()->unlock($password);
        // return $tronWallet;
    }

    public function address($tronWallet){
        $tronAddress = TronAddress::where('wallet_id', $tronWallet->id)->first();
        $index = 0; // Address index (if null - automatic)
        if(!$tronAddress){
            $tronAddress = Tron::createAddress($tronWallet, $index);
            $tronAddress->save();
        }
        return $tronAddress;
    }

    public function trc20Balance($address){
        $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // Contract Address Tether USDT
        $tronTRC20 = TronTRC20::where('address', $contractAddress)->first();
        if(!$tronTRC20) {
            $tronTRC20 = Tron::createTRC20($contractAddress);
            $tronTRC20->save();
        }
        return $tronTRC20->contract()->balanceOf($address);
    }

    public function trxBalance($address) {
        try {
            $api = new Api();
            $tron = new Tron($api);

            $accountInfo = $tron->api()->getAccount($address);

            // Convert from SUN to TRX (1 TRX = 1,000,000 SUN)
            $balanceInTrx = isset($accountInfo->balance) ? $accountInfo->balance / 1000000 : 0;

            return number_format($balanceInTrx, 6, '.', '');
        } catch (\Exception $e) {
            \Log::error('Error getting TRX balance: ' . $e->getMessage());
            return 0;
        }
    }

    private function wesite_transfer($data){


    }

    public function swapTrc20ToTrx($fromAddress, $privateKey, $amount) {
        // آدرس قرارداد SunSwap
        $sunSwapContractAddress = 'TXk8rQSAvPvBBNtqSoY6nCfsXWCSSpTVQF';

        // آدرس قرارداد USDT (یا هر توکن TRC20 دیگر)
        $trc20ContractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // آدرس قرارداد USDT

        // تنظیمات Api
        $api = new Api();
        $tron = new Tron($api);

        // ایجاد شیء برای قرارداد SunSwap
        $sunSwap = $tron->api()->getTRC20Contract($sunSwapContractAddress);

        // ایجاد شیء برای قرارداد TRC20
        $usdt = $tron->api()->getTRC20Contract($trc20ContractAddress);

        // تأیید اینکه آدرس از قبل فعال است
        $fromAccount = $tron->api()->getAccount($fromAddress);

        if (!$fromAccount->activated) {
            return "آدرس فعال نیست.";
        }
        // تایید مجوز برای قرارداد صرافی (SunSwap)
        // $approve = $usdt->approve($sunSwapContractAddress, 0.1);
        // $approve->send($privateKey);

        // // انجام مبادله (تبدیل TRC20 به TRX)
        // $swap = $sunSwap->swapExactTokensForTRX($amount, $fromAddress, $privateKey);
        // $result = $swap->send($privateKey);

        // return $result->toArray(); // خروجی تراکنش
    }

    public function checkResources($address) {
        $api = new Api();
        $tron = new Tron($api);

        $resources = $tron->api()->getAccountResources($address);

        return $resources; // نمایش پهنای باند و انرژی
    }

    public function trxTransfer(){
        $from = 'THPvaUhoh2Qn2y9THCZML3H815hhFhn5YC';
        $to = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        $amount = 1;

        $walletPassword = 'here wallet password';
        $wallet = TronWallet::first();
        $wallet->encrypted()->unlock($walletPassword);

        $address = $wallet->addresses->first();
        $privateKey = $wallet->encrypted()->decode($address->private_key);
        $api = new Api();
        $tron = new Tron($api);
        $transfer = $tron->api()->transfer($from, $to, $amount);
        $preview = $transfer->preview();
        print_r($preview->toArray());

        $send = $transfer->send($privateKey);
        print_r($send->toArray());
    }

    public function trc20Transfer(){
        $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        $from = 'THPvaUhoh2Qn2y9THCZML3H815hhFhn5YC';
        $to = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
        $amount = 1;

        $walletPassword = 'here wallet password';
        $wallet = TronWallet::first();
        $wallet->encrypted()->unlock($walletPassword);

        $address = $wallet->addresses->first();
        $privateKey = $wallet->encrypted()->decode($address->private_key);

        $api = new Api();
        $tron = new Tron($api);
        $transfer = $tron->api()->transferTRC20($contractAddress, $from, $to, $amount);
        $preview = $transfer->preview();
        print_r($preview->toArray());

        $send = $transfer->send($privateKey);
        print_r($send->toArray());
    }

}
