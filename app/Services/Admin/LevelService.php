<?php

namespace App\Services\Admin;

use App\Services\Service;
use App\Services\ServicesContract;
use App\Repositories\Admin\LevelRepo;

class LevelService extends Service implements ServicesContract
{
    
    public function __construct(
        protected LevelRepo $repo
    ){}

    public function levelsList($request){
        return $this->repo->getAllRecords($request);
    }

    public function storeLevels($data){
        try {
            $this->repo->createRecord($data);
            return [
                'success' => true,
                'message' => 'سطوح با موفقیت بروزرسانی شدند'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در بروزرسانی سطوح: ' . $e->getMessage()
            ];
        }
    }

    public function getLevel($id) {
        return $this->repo->getRecordById($id);
    }

    public function updateLevel($id, $data) {
        try {
            $level = $this->repo->updateRecord($id, $data);
            return [
                'success' => true,
                'message' => 'سطح با موفقیت بروزرسانی شد',
                'data' => $level
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در بروزرسانی سطح: ' . $e->getMessage()
            ];
        }
    }

    public function deleteLevel($id) {
        try {
            $this->repo->deleteRecordById($id);
            return [
                'success' => true,
                'message' => 'سطح با موفقیت حذف شد'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در حذف سطح: ' . $e->getMessage()
            ];
        }
    }
}
