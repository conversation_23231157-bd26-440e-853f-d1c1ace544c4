<?php

namespace App\Services\Evm;

use Illuminate\Support\Facades\Auth;

class EvmWalletService
{
    private static $host;
    private static $api_secret;

    public function __construct(){
        self::$host = env('EVM_APP_HOST');
        self::$api_secret = env('EVM_APP_SECRET');
    }

    public function checkSystemWalletAddress(int $network_id, string $pk)
    {
        return $this->__send('/evm/check-address', [ 'network' => $network_id, 'private_key' => $pk ], 'post');
    }

    public function getWalletBalance(int $network_id, string $address)
    {
        return $this->__send('/evm/get-balance', [ 'network' => $network_id, 'address' => $address ], 'post');
    }

    public function createSystemWallet(int $network_id)
    {
        return $this->__send('/evm/create-system-wallet', [ 'network' => $network_id ], 'post');
    }

    public function callDepositCommand()
    {
        return $this->__send('/evm/check-deposit', 'get');
    }

    public function callBlockDepositCommand()
    {
        return $this->__send('/evm/block-deposit-check', 'get');
    }

    public function withdrawalApproveProcess(array $request_data)
    {
        return $this->__send('/evm/withdrawal-approve-by-admin', $request_data, 'post');
    }

    public function acceptDepositFromUser(int $transaction_id)
    {
        return $this->__send('/evm/receive-deposit-coin', ['transaction_id' => $transaction_id], 'post');
    }

    public function checkDepositByTx(array $request_data)
    {
        return $this->__send('/evm/check-deposit-coin', $request_data, 'post');
    }

    public function checkCurrentBlockNumber(array $request_data)
    {
        return $this->__send('/evm/current-block', $request_data, 'post');
    }

    public function checkContractAddress(array $request_data)
    {
        return $this->__send('/evm/check-contract-address', $request_data, 'post');
    }

    private function __send($url, $body = [], $method = 'GET')
    {
        try {
            $header = [
                "Accept: application/json;",
                "evmapisecret: " . self::$api_secret,
                "token: " . session()->get('evm_token'),
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, self::$host.$url);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

            if($method != 'GET') {
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($body));
            }

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $res = curl_exec($ch);
            curl_close($ch);

            return json_decode($res,1);
        } catch (\Exception $e) {
            storeException("EvmWalletService __send", $e->getMessage());
            return responseData(false, __("Something went wrong"));
        }
    }

}
