<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class RSAService
{
    protected string $privateKey;
    protected string $publicKey;

    public function __construct()
    {
        $this->privateKey = Storage::get('keys/private.pem');
        $this->publicKey = Storage::get('keys/public.pem');
    }

    public function decrypt(string $encryptedBase64): array
    {
        $encrypted = base64_decode($encryptedBase64);
        $privateKeyResource = openssl_pkey_get_private($this->privateKey);

        if (!$privateKeyResource) {
            throw new \Exception("Invalid private key");
        }

        openssl_private_decrypt($encrypted, $decrypted, $privateKeyResource, OPENSSL_PKCS1_OAEP_PADDING);

      
        $result = json_decode($decrypted, true);
       
        return is_array($result) ? $result : [];
    }

    public function encrypt(array $data): string
    {
        $publicKeyResource = openssl_pkey_get_public($this->publicKey);

        if (!$publicKeyResource) {
            throw new \Exception("Invalid public key");
        }

        $json = json_encode($data);
        openssl_public_encrypt($json, $encrypted, $publicKeyResource, OPENSSL_PKCS1_OAEP_PADDING);

        return base64_encode($encrypted);
    }

    public function hybridDecrypt(string $encryptedKeyBase64, string $encryptedDataBase64, string $ivBase64): array
    {
        $privateKeyResource = openssl_pkey_get_private($this->privateKey);
        if (!$privateKeyResource) {
           
            return [];
        }

        // 1. RSA decrypt the AES key
        $encryptedKey = base64_decode($encryptedKeyBase64);
        $aesKey = '';
        $success = openssl_private_decrypt($encryptedKey, $aesKey, $privateKeyResource, OPENSSL_PKCS1_OAEP_PADDING);
     
        if (!$success) {
            return [];
        }

        // 2. AES decrypt the data
        $encryptedData = base64_decode($encryptedDataBase64);
        $iv = base64_decode($ivBase64);
        $decrypted = openssl_decrypt($encryptedData, 'AES-256-CBC', $aesKey, OPENSSL_RAW_DATA, $iv);
        if ($decrypted === false) {
            \Log::error('AES decrypt failed');
            return [];
        }

        $result = json_decode($decrypted, true);
        return is_array($result) ? $result : [];
    }
}