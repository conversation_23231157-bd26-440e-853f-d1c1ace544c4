<?php

namespace App\Services;

use App\Models\User;
use App\Models\Level;
use App\Models\UserLevel;
use App\Models\Document;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LevelUpgradeService
{
    /**
     * بررسی اینکه آیا کاربر می‌تواند به سطح بعدی ارتقا یابد
     */
    public function canUserUpgrade(User $user, Level $targetLevel): bool
    {
        $currentLevel = $user->currentLevel;
        
        // اگر کاربر سطح فعلی ندارد، می‌تواند ارتقا یابد
        if (!$currentLevel) {
            return true;
        }
        
        // اگر کاربر در حال حاضر در سطح هدف یا بالاتر است
        if ($currentLevel->priority >= $targetLevel->priority) {
            return false;
        }

        // بررسی شرایط ارتقا
        $conditions = $this->checkUpgradeConditions($user, $targetLevel);
        
        return $conditions['can_upgrade'];
    }

    /**
     * بررسی تمام شرایط ارتقا
     */
    public function checkUpgradeConditions(User $user, Level $targetLevel): array
    {
        $currentLevel = $user->currentLevel;
        $userLevel = $user->userLevel;
        
        $conditions = [
            'can_upgrade' => true,
            'reasons' => [],
            'details' => [],
            'requires_manual_approval' => false
        ];

        // اگر کاربر سطح فعلی ندارد، بررسی شرایط خاص
        if (!$currentLevel) {
            // اگر هدف سطح برنزی (priority = 1) است، می‌تواند ارتقا یابد
            if ($targetLevel->priority == 1) {
                return $conditions;
            }
            // برای سطوح بالاتر، نیاز به بررسی بیشتر دارد
            $conditions['can_upgrade'] = false;
            $conditions['reasons'][] = "ابتدا باید به سطح برنزی ارتقا یابید";
            return $conditions;
        }

        // بررسی نیاز به تایید دستی (برنزی به نقره‌ای)
        if ($currentLevel->priority == 1 && $targetLevel->priority == 2) {
            $conditions['requires_manual_approval'] = true;
            $conditions['can_upgrade'] = false;
            $conditions['reasons'][] = "برای ارتقا به سطح نقره‌ای، ابتدا مدارک خود را ارسال کنید تا توسط تیم پشتیبانی بررسی شود";
            return $conditions;
        }

        // 1. بررسی حداقل خرید کل
        if ($targetLevel->total_purchase_requirement > 0) {
            $totalPurchases = $user->total_purchases;
            $conditions['details']['total_purchases'] = [
                'required' => $targetLevel->total_purchase_requirement,
                'current' => $totalPurchases,
                'met' => $totalPurchases >= $targetLevel->total_purchase_requirement
            ];
            
            if ($totalPurchases < $targetLevel->total_purchase_requirement) {
                $conditions['can_upgrade'] = false;
                $conditions['reasons'][] = "حداقل خرید کل {$targetLevel->formatted_total_purchase_requirement} لازم است. خرید فعلی: " . number_format($totalPurchases) . " تومان";
            }
        }

        // 2. بررسی زمان لازم از سطح قبلی
        if ($targetLevel->days_from_previous_level > 0 && $userLevel) {
            $hoursSinceUpgrade = $userLevel->hours_since_upgrade;
            $requiredHours = $targetLevel->days_from_previous_level;
            
            $conditions['details']['time_requirement'] = [
                'required_hours' => $requiredHours,
                'current_hours' => $hoursSinceUpgrade,
                'met' => $hoursSinceUpgrade >= $requiredHours
            ];
            
            if ($hoursSinceUpgrade < $requiredHours) {
                $conditions['can_upgrade'] = false;
                $remainingHours = $requiredHours - $hoursSinceUpgrade;
                $remainingDays = floor($remainingHours / 24);
                $remainingHours = $remainingHours % 24;
                
                $timeText = '';
                if ($remainingDays > 0) {
                    $timeText .= "{$remainingDays} روز ";
                }
                if ($remainingHours > 0) {
                    $timeText .= "{$remainingHours} ساعت";
                }
                
                $conditions['reasons'][] = "هنوز {$timeText} تا ارتقا به سطح {$targetLevel->name} باقی مانده است";
            }
        }

        // 3. بررسی اینکه سطح هدف فعال است
        if (!$targetLevel->active) {
            $conditions['can_upgrade'] = false;
            $conditions['reasons'][] = "سطح {$targetLevel->name} در حال حاضر غیرفعال است";
        }

        return $conditions;
    }

    /**
     * ارتقای سطح کاربر
     */
    public function upgradeUserLevel(User $user, Level $targetLevel, bool $isAutomatic = true): array
    {
        try {
            DB::beginTransaction();

            $currentLevel = $user->currentLevel;
            $userLevel = $user->userLevel;

            // بررسی شرایط ارتقا
            $conditions = $this->checkUpgradeConditions($user, $targetLevel);
            
            if (!$conditions['can_upgrade']) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'شرایط ارتقا برآورده نشده است',
                    'reasons' => $conditions['reasons'],
                    'requires_manual_approval' => $conditions['requires_manual_approval'] ?? false
                ];
            }

            // ایجاد یا بروزرسانی رکورد سطح کاربر
            $currentLevelName = $currentLevel ? $currentLevel->name : 'بدون سطح';
            $upgradeReason = "ارتقا از سطح {$currentLevelName} به سطح {$targetLevel->name}";
            if ($isAutomatic) {
                $upgradeReason .= " (خودکار)";
            }

            UserLevel::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'level_id' => $targetLevel->id,
                    'previous_level_id' => $currentLevel ? $currentLevel->id : null,
                    'upgraded_at' => now(),
                    'total_purchases_at_upgrade' => $user->total_purchases,
                    'upgrade_reason' => $upgradeReason,
                    'is_automatic' => $isAutomatic
                ]
            );

            // بروزرسانی سطح کاربر در جدول users
            $user->update(['level' => $targetLevel->priority]);

            DB::commit();

            // ارسال اعلان به کاربر
            $this->sendUpgradeNotification($user, $targetLevel);

            $currentLevelName = $currentLevel ? $currentLevel->name : 'بدون سطح';
            Log::info("User {$user->id} upgraded from level {$currentLevelName} to {$targetLevel->name}");

            return [
                'success' => true,
                'message' => "سطح شما با موفقیت به {$targetLevel->name} ارتقا یافت",
                'new_level' => $targetLevel,
                'previous_level' => $currentLevel
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error upgrading user level: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'خطا در ارتقای سطح: ' . $e->getMessage()
            ];
        }
    }

    /**
     * ارتقای دستی سطح کاربر (توسط ادمین - برای تایید مدارک)
     */
    public function manualUpgradeUserLevel(User $user, Level $targetLevel, string $reason = null): array
    {
        try {
            DB::beginTransaction();

            $currentLevel = $user->currentLevel;
            $userLevel = $user->userLevel;

            // ایجاد یا بروزرسانی رکورد سطح کاربر
            $currentLevelName = $currentLevel ? $currentLevel->name : 'بدون سطح';
            $upgradeReason = $reason ?? "ارتقا از سطح {$currentLevelName} به سطح {$targetLevel->name} (تایید مدارک)";

            UserLevel::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'level_id' => $targetLevel->id,
                    'previous_level_id' => $currentLevel ? $currentLevel->id : null,
                    'upgraded_at' => now(),
                    'total_purchases_at_upgrade' => $user->total_purchases,
                    'upgrade_reason' => $upgradeReason,
                    'is_automatic' => false
                ]
            );

            // بروزرسانی سطح کاربر در جدول users
            $user->update(['level' => $targetLevel->priority]);

            DB::commit();

            // ارسال اعلان به کاربر
            $this->sendUpgradeNotification($user, $targetLevel);

            $currentLevelName = $currentLevel ? $currentLevel->name : 'بدون سطح';
            Log::info("User {$user->id} manually upgraded from level {$currentLevelName} to {$targetLevel->name}");

            return [
                'success' => true,
                'message' => "سطح کاربر با موفقیت به {$targetLevel->name} ارتقا یافت",
                'new_level' => $targetLevel,
                'previous_level' => $currentLevel
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error manually upgrading user level: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'خطا در ارتقای سطح: ' . $e->getMessage()
            ];
        }
    }

    /**
     * بررسی خودکار ارتقای سطح برای کاربر
     */
    public function checkAndUpgradeUserLevel(User $user): array
    {
        $currentLevel = $user->currentLevel;
        
        // اگر کاربر سطح فعلی ندارد، سطح بعدی سطح برنزی است
        if (!$currentLevel) {
            $nextLevel = Level::where('priority', 1)->first();
        } else {
            $nextLevel = Level::where('priority', $currentLevel->priority + 1)->first();
        }

        if (!$nextLevel) {
            return [
                'success' => false,
                'message' => 'سطح بعدی وجود ندارد'
            ];
        }

        if ($this->canUserUpgrade($user, $nextLevel)) {
            return $this->upgradeUserLevel($user, $nextLevel, true);
        }

        return [
            'success' => false,
            'message' => 'شرایط ارتقا برآورده نشده است',
            'conditions' => $this->checkUpgradeConditions($user, $nextLevel)
        ];
    }

    /**
     * بررسی وضعیت مدارک کاربر برای ارتقا به سطح نقره‌ای
     */
    public function checkDocumentStatusForSilverUpgrade(User $user): array
    {
        $documents = Document::where('user_id', $user->id)->get();
        
        $status = [
            'has_documents' => $documents->count() > 0,
            'all_approved' => $documents->count() > 0 && $documents->every(fn($doc) => $doc->status === 'approved'),
            'has_pending' => $documents->where('status', 'pending')->count() > 0,
            'has_rejected' => $documents->where('status', 'rejected')->count() > 0,
            'documents' => $documents
        ];

        return $status;
    }

    /**
     * ارتقای سطح کاربر پس از تایید مدارک (برای ادمین)
     */
    public function upgradeUserAfterDocumentApproval(User $user): array
    {
        $currentLevel = $user->currentLevel;
        $silverLevel = Level::where('priority', 2)->first(); // سطح نقره‌ای

        if (!$silverLevel) {
            return [
                'success' => false,
                'message' => 'سطح نقره‌ای یافت نشد'
            ];
        }

        // اگر کاربر سطح فعلی ندارد یا در سطح پایین‌تر از نقره‌ای است
        if (!$currentLevel || $currentLevel->priority < 2) {
            // ادامه می‌دهد
        } else {
            return [
                'success' => false,
                'message' => 'کاربر قبلاً در سطح نقره‌ای یا بالاتر است'
            ];
        }

        return $this->manualUpgradeUserLevel($user, $silverLevel, 'تایید مدارک و ارتقا به سطح نقره‌ای');
    }

    /**
     * ارسال اعلان ارتقای سطح
     */
    private function sendUpgradeNotification(User $user, Level $newLevel): void
    {
        try {
            // ایجاد اعلان در سیستم
            $user->alerts()->create([
                'type' => 'success',
                'message' => "تبریک! سطح شما به {$newLevel->name} ارتقا یافت. ویژگی‌های جدید: {$newLevel->features}",
                'read' => false
            ]);

            // اینجا می‌توانید اعلان‌های دیگر مثل ایمیل یا پیامک اضافه کنید
        } catch (\Exception $e) {
            Log::error("Error sending upgrade notification: " . $e->getMessage());
        }
    }

    /**
     * دریافت اطلاعات ارتقای سطح کاربر
     */
    public function getUserUpgradeInfo(User $user): array
    {
        if (!$user->currentLevel || $user->level == 0) {
            return [
                'current_level' => null,
                'next_level' => null,
                'can_upgrade' => false,
                'upgrade_conditions' => null,
                'time_since_upgrade' => null,
                'requires_manual_approval' => false,
                'document_status' => null
            ];
        }

        $currentLevel = $user->currentLevel;
        
        // اگر کاربر سطح فعلی ندارد، سطح بعدی سطح برنزی است
        if (!$currentLevel) {
            $nextLevel = Level::where('priority', 1)->first();
        } else {
            $nextLevel = Level::where('priority', $currentLevel->priority + 1)->first();
        }
        $userLevel = $user->userLevel;

        $info = [
            'current_level' => $currentLevel,
            'next_level' => $nextLevel,
            'can_upgrade' => false,
            'upgrade_conditions' => null,
            'time_since_upgrade' => null,
            'requires_manual_approval' => false,
            'document_status' => null
        ];

        if ($nextLevel) {
            $conditions = $this->checkUpgradeConditions($user, $nextLevel);
            $info['can_upgrade'] = $conditions['can_upgrade'];
            $info['upgrade_conditions'] = $conditions;
            $info['requires_manual_approval'] = $conditions['requires_manual_approval'] ?? false;

            // اگر نیاز به تایید دستی دارد، وضعیت مدارک را بررسی کن
            if ($info['requires_manual_approval']) {
                $info['document_status'] = $this->checkDocumentStatusForSilverUpgrade($user);
            }
        }

        if ($userLevel) {
            $info['time_since_upgrade'] = [
                'days' => $userLevel->days_since_upgrade,
                'hours' => $userLevel->hours_since_upgrade
            ];
        }

        return $info;
    }
}
