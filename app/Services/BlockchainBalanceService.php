<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BlockchainBalanceService
{
    /**
     * Get wallet balance from various blockchain networks
     */
    public static function getBalance($address, $networkType, $networkId = null)
    {
        try {
            switch (strtolower($networkType)) {
                case 'tronmainnet':
                case 'trx':
                    return self::getTronBalance($address);
                    
                case 'ethereum':
                case 'eth':
                    return self::getEthereumBalance($address);
                    
                case 'bsc':
                case 'bnbmainnet':
                    return self::getBscBalance($address);
                    
                case 'polygon':
                case 'matic':
                    return self::getPolygonBalance($address);
                case 'ton':
                    return self::getTonBalance($address);
                    
                case 'evm':
                default:
                    return self::getGenericEvmBalance($address, $networkId);
            }
        } catch (\Exception $e) {
            Log::error('Error getting blockchain balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get Tron balance from TronScan API
     */
    private static function getTonBalance($address)
    {
        try {
            $response = Http::timeout(10)->get('https://toncenter.com/api/v2/getAddressBalance', [
                'address' => $address,
                'api_key' => env('TON_API_KEY', '') // Optional
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['result']) && is_numeric($data['result'])) {
                    // Convert from nanoTON to TON (1 TON = 10^9 nanoTON)
                    return bcdiv($data['result'], '1********0', 8);
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('TON balance error: ' . $e->getMessage());
            return '0.********';
        }
    }
     private static function getTronBalance($address)
    {
        try {
            $response = Http::timeout(10)->get("https://apilist.tronscanapi.com/api/account", [
                'address' => $address
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['balance'])) {
                    // Convert from SUN to TRX (1 TRX = 1,000,000 SUN)
                    $balanceInTrx = $data['balance'] / 1000000;
                    return number_format($balanceInTrx, 6, '.', '');
                }
            }
            
            return '0.000000';
        } catch (\Exception $e) {
            Log::error('Error getting Tron balance from TronScan: ' . $e->getMessage());
            return '0.000000';
        }
    }

    /**
     * Get Ethereum balance from Etherscan
     */
    private static function getEthereumBalance($address)
    {
        try {
            $apiKey = env('ETHERSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.etherscan.io/api", [
                'module' => 'account',
                'action' => 'balance',
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    // Convert from Wei to ETH (1 ETH = 10^18 Wei)
                    $balanceInEth = bcdiv($data['result'], '1****************00', 8);
                    return $balanceInEth;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting Ethereum balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get BSC balance from BscScan
     */
    private static function getBscBalance($address)
    {
        try {
            $apiKey = env('BSCSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.bscscan.com/api", [
                'module' => 'account',
                'action' => 'balance',
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    // Convert from Wei to BNB (1 BNB = 10^18 Wei)
                    $balanceInBnb = bcdiv($data['result'], '1****************00', 8);
                    return $balanceInBnb;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting BSC balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get Polygon balance from PolygonScan
     */
    private static function getPolygonBalance($address)
    {
        try {
            $apiKey = env('POLYGONSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.polygonscan.com/api", [
                'module' => 'account',
                'action' => 'balance',
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    // Convert from Wei to MATIC (1 MATIC = 10^18 Wei)
                    $balanceInMatic = bcdiv($data['result'], '1****************00', 8);
                    return $balanceInMatic;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting Polygon balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get balance for other EVM networks using RPC
     */
    private static function getGenericEvmBalance($address, $networkId)
    {
        try {
            // Get network RPC URL from database
            $network = \App\Models\Network::find($networkId);
            if (!$network || !$network->rpc_url) {
                return '0.********';
            }

            $response = Http::timeout(10)->post($network->rpc_url, [
                'jsonrpc' => '2.0',
                'method' => 'eth_getBalance',
                'params' => [$address, 'latest'],
                'id' => 1
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['result'])) {
                    // Convert hex to decimal, then Wei to ETH
                    $balanceWei = hexdec($data['result']);
                    $balanceInEth = bcdiv($balanceWei, '1****************00', 8);
                    return $balanceInEth;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting generic EVM balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get multiple token balances for EVM networks
     */
    public static function getTokenBalance($address, $contractAddress, $networkType, $networkId = null)
    {
        try {
            switch (strtolower($networkType)) {
                case 'ethereum':
                case 'eth':
                    return self::getEthereumTokenBalance($address, $contractAddress);
                    
                case 'bsc':
                case 'binance':
                    return self::getBscTokenBalance($address, $contractAddress);
                    
                case 'polygon':
                case 'matic':
                    return self::getPolygonTokenBalance($address, $contractAddress);
                    
                case 'tron':
                case 'trx':
                    return self::getTronTokenBalance($address, $contractAddress);
                    
                default:
                    return self::getGenericEvmTokenBalance($address, $contractAddress, $networkId);
            }
        } catch (\Exception $e) {
            Log::error('Error getting token balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get Ethereum token balance
     */
    private static function getEthereumTokenBalance($address, $contractAddress)
    {
        try {
            $apiKey = env('ETHERSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.etherscan.io/api", [
                'module' => 'account',
                'action' => 'tokenbalance',
                'contractaddress' => $contractAddress,
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    // Convert based on token decimals (usually 18)
                    $balance = bcdiv($data['result'], '1****************00', 8);
                    return $balance;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting Ethereum token balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get BSC token balance
     */
    private static function getBscTokenBalance($address, $contractAddress)
    {
        try {
            $apiKey = env('BSCSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.bscscan.com/api", [
                'module' => 'account',
                'action' => 'tokenbalance',
                'contractaddress' => $contractAddress,
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    $balance = bcdiv($data['result'], '1****************00', 8);
                    return $balance;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting BSC token balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get Polygon token balance
     */
    private static function getPolygonTokenBalance($address, $contractAddress)
    {
        try {
            $apiKey = env('POLYGONSCAN_API_KEY', '');
            $response = Http::timeout(10)->get("https://api.polygonscan.com/api", [
                'module' => 'account',
                'action' => 'tokenbalance',
                'contractaddress' => $contractAddress,
                'address' => $address,
                'tag' => 'latest',
                'apikey' => $apiKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] == '1' && isset($data['result'])) {
                    $balance = bcdiv($data['result'], '1****************00', 8);
                    return $balance;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting Polygon token balance: ' . $e->getMessage());
            return '0.********';
        }
    }

    /**
     * Get Tron token balance (TRC20)
     */
    private static function getTronTokenBalance($address, $contractAddress)
    {
        try {
            $response = Http::timeout(10)->get("https://apilist.tronscanapi.com/api/account/tokens", [
                'address' => $address,
                'start' => 0,
                'limit' => 50
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data'])) {
                    foreach ($data['data'] as $token) {
                        if (isset($token['tokenId']) && $token['tokenId'] === $contractAddress) {
                            $balance = $token['balance'] ?? 0;
                            $decimals = $token['tokenDecimal'] ?? 6;
                            $divisor = pow(10, $decimals);
                            return number_format($balance / $divisor, $decimals, '.', '');
                        }
                    }
                }
            }

            return '0.000000';
        } catch (\Exception $e) {
            Log::error('Error getting Tron token balance: ' . $e->getMessage());
            return '0.000000';
        }
    }

    /**
     * Get generic EVM token balance using RPC
     */
    private static function getGenericEvmTokenBalance($address, $contractAddress, $networkId)
    {
        try {
            $network = \App\Models\Network::find($networkId);
            if (!$network || !$network->rpc_url) {
                return '0.********';
            }

            // ERC20 balanceOf function signature
            $functionSignature = '0x70a08231';
            $paddedAddress = str_pad(substr($address, 2), 64, '0', STR_PAD_LEFT);
            $data = $functionSignature . $paddedAddress;

            $response = Http::timeout(10)->post($network->rpc_url, [
                'jsonrpc' => '2.0',
                'method' => 'eth_call',
                'params' => [
                    [
                        'to' => $contractAddress,
                        'data' => $data
                    ],
                    'latest'
                ],
                'id' => 1
            ]);

            if ($response->successful()) {
                $result = $response->json();
                if (isset($result['result'])) {
                    $balanceHex = $result['result'];
                    $balanceWei = hexdec($balanceHex);
                    $balance = bcdiv($balanceWei, '1****************00', 8);
                    return $balance;
                }
            }

            return '0.********';
        } catch (\Exception $e) {
            Log::error('Error getting generic EVM token balance: ' . $e->getMessage());
            return '0.********';
        }
    }
}
