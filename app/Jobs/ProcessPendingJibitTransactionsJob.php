<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\JibitTransaction;
use App\Services\JibitPaymentService;
use Illuminate\Support\Facades\Log;
use Cryptommer\Smsir\Objects\Parameters;
use Cryptommer\Smsir\Classes\Smsir;
class ProcessPendingJibitTransactionsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $pendingTransactions = JibitTransaction::where('status', 'pending')->get();
        // dd($pendingTransactions);
        $service = new JibitPaymentService();
        foreach ($pendingTransactions as $transaction) {
            $iban = $transaction->iban;
            $referenceNumber = $transaction->reference_number;
            $result = $service->fetchAugStatementForTransaction($iban, $referenceNumber);
        //   dd($result);
            // ذخیره پاسخ در details
            $transaction->details = $result['data'] ?? null;
            $transaction->save();

            if ($result['success'] && isset($result['data']['sourceIban'])) {
                $sourceIban = $result['data']['sourceIban'];
                $payId = $result['data']['payId'] ?? null;
                $creditAmount = $result['data']['creditAmount'] / 10;

                // پیدا کردن کارت با شماره شبا
                $card = \App\Models\Card::where('iban', $sourceIban)->first();
                if ($card && $card->user_id) {
                    $user = \App\Models\User::find($card->user_id);
                    $balanceBefore = $user->toman_balance;
                   // dd($user);
                    if ($user && $user->national_id) {
                        // شارژ موجودی
                        $user->toman_balance = ($user->toman_balance ?? 0) + $creditAmount;
                        $user->save();
                        // ثبت تراکنش
                       $user->transactions()->create([
                            'type' => 'deposit',
                            'amount' => $creditAmount,
                            'currency_id' => 6, // Toman/IRR currency ID
                            'status' => 'done', // Admin needs to approve
                            'description' => 'واریز شناسه دار جیبیت',
                            'details' => [
                                'jibit_transaction_id' => $transaction->id,
                                'reference_number' => $referenceNumber,
                                'iban' => $iban,
                                'source_iban' => $sourceIban,
                            ],
                        
                            'balance_before' => $balanceBefore,
                            'balance_after' => $user->toman_balance,
                        ]);
                        // دریافت قالب پیامک از جدول sms_templates
                        $smsTemplate = \App\Models\SmsTemplate::where('code', 'deposit-toman-payid')->first();
                        if ($smsTemplate) {
                            $sms = new Smsir;
                            $sms->Send()->Verify(
                                $user->phone,
                                $smsTemplate->template_id,
                                [new Parameters('COIN', 'کیف پول تومانی شما')]
                            );
                        }
                        // تغییر وضعیت تراکنش جیبیت
                        $transaction->status = 'approved';
                        $transaction->save();
                    }
                }
            }

            Log::info('Jibit fetchAugStatementForTransaction result', [
                'reference_number' => $referenceNumber,
                'iban' => $iban,
                'result' => $result
            ]);
        }
    }
}
