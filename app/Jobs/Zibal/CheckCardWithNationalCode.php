<?php

namespace App\Jobs\Zibal;

use App\Models\Alert;
use App\Models\Card;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Log;
use Cryptommer\Smsir\Objects\Parameters;
use Cryptommer\Smsir\Classes\Smsir;
class CheckCardWithNationalCode implements ShouldQueue
{
    use Queueable;

    protected $userId;

    protected $cardId;

    /**
     * Create a new job instance.
     */
    public function __construct($userId, $cardId)
    {
        $this->userId = $userId;
        $this->cardId = $cardId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = User::findOrFail($this->userId);
        $card = Card::findOrFail($this->cardId);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.env('ZIBAL_API'),
        ])->post('https://api.zibal.ir/v1/facility/cardToIban/', [
            'nationalCode' => $user->national_id,
            'birthDate' => $user->birth_date,
            'cardNumber' => $card->number,
        ]);
        if ($response['result'] == 1) {
            $sms = new Smsir;
            $smsTemplate = \App\Models\SmsTemplate::where('code', 'BANKCARD')->first();
            switch ($response['data']) {
                case true:
                    $card->update([
                        'iban' => $response['data']['IBAN'],
                        'sheba' => $response['data']['IBAN'],
                        'status' => 'approved',
                    ]);
                    Alert::firstOrCreate([
                        'message' => "کارت بانکی به شماره {$card->number} با موفقیت ثبت شد.",
                    ],[
                        'type' => 'success',
                        'user_id' => $user->id,
                    ]);
                 
                    if ($smsTemplate) {
                    $sms->Send()->Verify($user->phone, $smsTemplate->template_id, [new Parameters('CARDNUMBER', $card->number),new Parameters('MESSAGE', 'باموقیت تایید')]);
                    }
                    break;
                default:
                    $card->delete();
                    if ($smsTemplate) {
                        $sms->Send()->Verify($user->phone, $smsTemplate->template_id, [new Parameters('CARDNUMBER', $card->number),new Parameters('MESSAGE', 'به دلیل تطابق نداشتن با کد ملی رد')]);
                        }
                    Alert::firstOrCreate([
                        'message' => "کارت بانکی ثبت شده به شماره {$card->number} با کدملی / تاریخ تولد شما تطابق ندارد.",
                    ],[
                        'type' => 'error',
                        'user_id' => $user->id,
                    ]);
                    // TODO: add queue for remove this card
                    break;
            }
        }
    }
}
