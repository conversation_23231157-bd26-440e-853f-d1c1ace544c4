<?php

namespace App\Repositories\Admin;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class TransactionRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($filter, User|Wallet|null $userWallet = null): LengthAwarePaginator
    {
        $transactions = Transaction::query();

        // Apply filters
        if (isset($filter['type'])) {
            if ($filter['type'] == 'orders') {
                $transactions->whereNot('currency_id', 3);
            } else {
                $transactions->whereIn('type', explode(',', $filter['type']));
            }
        }

        if (isset($filter['status'])) {
            $transactions->whereIn('status', explode(',', $filter['status']));
        }

        if (isset($filter['user_id'])) {
            $transactions->whereIn('user_id', explode(',', $filter['user_id']));
        }

        if (isset($filter['currency_id'])) {
            $transactions->where('currency_id', $filter['currency_id']);
        }

        if (isset($filter['wallet_id'])) {
            $transactions->where('wallet_id', $filter['wallet_id']);
        }

        if (isset($filter['from_date'])) {
            $transactions->whereDate('created_at', '>=', $filter['from_date']);
        }

        if (isset($filter['to_date'])) {
            $transactions->whereDate('created_at', '<=', $filter['to_date']);
        }

        if (isset($filter['amount_min'])) {
            $transactions->where('amount', '>=', $filter['amount_min']);
        }

        if (isset($filter['amount_max'])) {
            $transactions->where('amount', '<=', $filter['amount_max']);
        }

        if (isset($userWallet)) {
            $transactions->where($userWallet instanceof User ? 'user_id' : 'wallet_id', $userWallet->id);
        }

        // Include relationships
        $transactions->with(['user', 'currency', 'wallet', 'registrar']);

        // Order by
        $orderBy = $filter['order_by'] ?? 'created_at';
        $orderDirection = $filter['order_direction'] ?? 'desc';
        $transactions->orderBy($orderBy, $orderDirection);

        // Paginate results
        $pagination = $transactions->paginate(
            perPage: $filter['per_page'] ?? 20,
            page: $filter['page'] ?? 1,
        );

        // Transform each transaction to include detailed information
        $pagination->getCollection()->transform(function ($transaction) {
            // Add user details
            if ($transaction->user) {
                $transaction->user_details = [
                    'id' => $transaction->user->id,
                    'name' => $transaction->user->name,
                    'email' => $transaction->user->email,
                ];
            }

            // Add currency details
            if ($transaction->currency) {
                $transaction->currency_details = [
                    'id' => $transaction->currency->id,
                    'name' => $transaction->currency->name,
                    'coin_type' => $transaction->currency->coin_type,
                    'coin_price' => $transaction->currency->coin_price
                ];
            }

            // Add wallet details
            if ($transaction->wallet) {
                $transaction->wallet_details = [
                    'id' => $transaction->wallet->id,
                    'name' => $transaction->wallet->name,
                    'balance' => $transaction->wallet->balance
                ];
            }

            // Add registrar details
            if ($transaction->registrar && is_object($transaction->registrar)) {
                $transaction->registrar_details = [
                    'id' => $transaction->registrar->id,
                    'name' => $transaction->registrar->name,
                    'email' => $transaction->registrar->email,
                ];
            }

            // Process details based on transaction type
            if($transaction->currency_id != 6){
// dd($transaction);
            
            if ($transaction->details) {
                $details = json_decode($transaction->details, true);

                if ($transaction->type == 'swap_out' || $transaction->type == 'swap_in') {
                    $transaction->swap_details = [
                        'from_currency' => $details['from_currency'] ?? null,
                        'to_currency' => $details['to_currency'] ?? null,
                        'from_amount' => $details['from_amount'] ?? null,
                        'to_amount' => $details['to_amount'] ?? null,
                        'usd_value' => $details['usd_value'] ?? null,
                        'fee_percentage' => $details['fee_percentage'] ?? null,
                        'fee_amount' => $details['fee_amount'] ?? null
                    ];
                } else if ($transaction->type == 'buy') {
                    $transaction->buy_details = [
                        'toman_amount' => $details['toman_amount'] ?? null,
                        'usd_amount' => $details['usd_amount'] ?? null,
                        'usd_rate' => $details['usd_rate'] ?? null,
                        'crypto_amount' => $details['crypto_amount'] ?? null
                    ];
                } else if ($transaction->type == 'sell') {
                    $transaction->sell_details = [
                        'crypto_amount' => $details['crypto_amount'] ?? null,
                        'usd_amount' => $details['usd_amount'] ?? null,
                        'usd_rate' => $details['usd_rate'] ?? null,
                        'toman_amount' => $details['toman_amount'] ?? null
                    ];
                } else {
                    // For other transaction types, just decode the details
                    $transaction->transaction_details = $details;
                }
            }
        }
            // Add human-readable transaction type description
            $transaction->type_description = $this->getTransactionTypeDescription($transaction->type);

            return $transaction;
        });

        return $pagination;
    }

    /**
     * Get human-readable description for transaction types
     *
     * @param string $type
     * @return string
     */
    private function getTransactionTypeDescription($type)
    {
        $descriptions = [
            'deposit' => 'واریز',
            'withdraw' => 'برداشت',
            'gift' => 'هدیه',
            'transfer' => 'انتقال',
            'buy' => 'خرید',
            'sell' => 'فروش',
            'increase' => 'افزایش موجودی',
            'decrease' => 'کاهش موجودی',
            'swap_out' => 'تبدیل ارز (خروجی)',
            'swap_in' => 'تبدیل ارز (ورودی)'
        ];

        return $descriptions[$type] ?? $type;
    }

    public function createRecord(array $data) {}

    public function getRecordById($id)
    {
        $transaction = Transaction::with(['wallet', 'user', 'registrar', 'currency'])->findOrFail($id);

        // Add user details
        if ($transaction->user) {
            $transaction->user_details = [
                'id' => $transaction->user->id,
                'name' => $transaction->user->name,
                'email' => $transaction->user->email,
            ];
        }

        // Add currency details
        if ($transaction->currency) {
            $transaction->currency_details = [
                'id' => $transaction->currency->id,
                'name' => $transaction->currency->name,
                'coin_type' => $transaction->currency->coin_type,
                'coin_price' => $transaction->currency->coin_price
            ];
        }

        // Add wallet details
        if ($transaction->wallet) {
            $transaction->wallet_details = [
                'id' => $transaction->wallet->id,
                'name' => $transaction->wallet->name,
                'balance' => $transaction->wallet->balance
            ];
        }

        // Add registrar details
        if ($transaction->registrar && is_object($transaction->registrar)) {
            $transaction->registrar_details = [
                'id' => $transaction->registrar->id,
                'name' => $transaction->registrar->name,
                'email' => $transaction->registrar->email,
            ];
        }

        // Process details based on transaction type
        if ($transaction->details) {
            $details = json_decode($transaction->details, true);

            if ($transaction->type == 'swap_out' || $transaction->type == 'swap_in') {
                $transaction->swap_details = [
                    'from_currency' => $details['from_currency'] ?? null,
                    'to_currency' => $details['to_currency'] ?? null,
                    'from_amount' => $details['from_amount'] ?? null,
                    'to_amount' => $details['to_amount'] ?? null,
                    'usd_value' => $details['usd_value'] ?? null,
                    'fee_percentage' => $details['fee_percentage'] ?? null,
                    'fee_amount' => $details['fee_amount'] ?? null
                ];
            } else if ($transaction->type == 'buy') {
                $transaction->buy_details = [
                    'toman_amount' => $details['toman_amount'] ?? null,
                    'usd_amount' => $details['usd_amount'] ?? null,
                    'usd_rate' => $details['usd_rate'] ?? null,
                    'crypto_amount' => $details['crypto_amount'] ?? null
                ];
            } else if ($transaction->type == 'sell') {
                $transaction->sell_details = [
                    'crypto_amount' => $details['crypto_amount'] ?? null,
                    'usd_amount' => $details['usd_amount'] ?? null,
                    'usd_rate' => $details['usd_rate'] ?? null,
                    'toman_amount' => $details['toman_amount'] ?? null
                ];
            } else {
                // For other transaction types, just decode the details
                $transaction->transaction_details = $details;
            }
        }

        // Add human-readable transaction type description
        $transaction->type_description = $this->getTransactionTypeDescription($transaction->type);

        return $transaction;
    }

    public function updateRecord($id, array $data)
    {
        $transaction = Transaction::findOrFail($id);
        $transaction->status = $data['status'];
        $transaction->description = $data['description'] ?? '';
        $transaction->save();

        if ($transaction->status == 'declined') {
            Transaction::create([
                'amount' => $transaction->amount,
                'registrar' => Auth::user() ? Auth::user()->id : null,
                'currency_id' => 3,
                'user_id' => $transaction->user_id,
                'type' => 'increase',
                'status' => 'done',
            ]);
        }

    }

    public function deleteRecordById($id) {}

    /**
     * Get all records for export
     *
     * @param array $filter
     * @param User|Wallet|null $userWallet
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllRecordsForExport(array $filter, User|Wallet|null $userWallet = null)
    {
        $transactions = Transaction::query();

        // Apply filters
        if (isset($filter['type'])) {
            if ($filter['type'] == 'orders') {
                $transactions->whereNot('currency_id', 3);
            } else {
                $transactions->whereIn('type', explode(',', $filter['type']));
            }
        }

        if (isset($filter['status'])) {
            $transactions->whereIn('status', explode(',', $filter['status']));
        }

        if (isset($filter['user_id'])) {
            $transactions->whereIn('user_id', explode(',', $filter['user_id']));
        }

        if (isset($filter['currency_id'])) {
            $transactions->where('currency_id', $filter['currency_id']);
        }

        if (isset($filter['wallet_id'])) {
            $transactions->where('wallet_id', $filter['wallet_id']);
        }

        if (isset($filter['from_date'])) {
            $transactions->whereDate('created_at', '>=', $filter['from_date']);
        }

        if (isset($filter['to_date'])) {
            $transactions->whereDate('created_at', '<=', $filter['to_date']);
        }

        if (isset($filter['amount_min'])) {
            $transactions->where('amount', '>=', $filter['amount_min']);
        }

        if (isset($filter['amount_max'])) {
            $transactions->where('amount', '<=', $filter['amount_max']);
        }

        if (isset($userWallet)) {
            $transactions->where($userWallet instanceof User ? 'user_id' : 'wallet_id', $userWallet->id);
        }

        // Include relationships
        $transactions->with(['user', 'currency', 'wallet', 'registrar']);

        // Order by
        $orderBy = $filter['order_by'] ?? 'created_at';
        $orderDirection = $filter['order_direction'] ?? 'desc';
        $transactions->orderBy($orderBy, $orderDirection);

        // Get all records without pagination
        return $transactions->get();
    }
}
