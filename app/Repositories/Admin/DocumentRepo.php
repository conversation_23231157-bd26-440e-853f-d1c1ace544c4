<?php

namespace App\Repositories\Admin;

use App\Models\Alert;
use App\Models\Document;
use App\Models\User;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class DocumentRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $docs = Document::query();
        if (isset($request['user_id'])) {
            $docs->where('user_id', $request['user_id']);
        }
        if (isset($request['status'])) {
            $docs->whereIn('status', explode(',', $request['status']));
        }

        return $docs->with('file', 'user')->paginate(
            perPage: $request['per_page'] ?? 30,
            page: $request['page'] ?? 1
        );

    }

    public function createRecord(array $data) {}

    public function getRecordById($id)
    {
        return Document::with('file')->findOrFail($id);
    }

    public function updateRecord($id, array $data)
    {
        $document = Document::findOrFail($id);
        $document->status = $data['status'];
        $document->description = $data['description'];
        $document->save();
        
        // بررسی اینکه آیا تمام مدارک کاربر تایید شده‌اند
        $documents = Document::where('user_id', $document->user_id)->get();
        if ($documents->every(function ($document) {
            return $document->status === 'approved';
        })) {
            // استفاده از سرویس جدید برای ارتقای سطح
            $levelUpgradeService = app(\App\Services\LevelUpgradeService::class);
            $result = $levelUpgradeService->upgradeUserAfterDocumentApproval(User::find($document->user_id));
            
            if ($result['success']) {
                Alert::create([
                    'user_id' => $document->user_id,
                    'type' => 'success',
                    'message' => "تبریک! سطح شما به {$result['new_level']->name} ارتقا یافت."
                ]);
            }
        }
    }

    public function bulkUpdateUserDocuments($userId, $status, $description = null)
    {
        // Only update documents that are currently pending to avoid overwriting already processed documents
        $updatedCount = Document::where('user_id', $userId)
            ->update([
                'status' => $status,
                'description' => $description,
                'updated_at' => now()
            ]);
            
        if ($status === 'approved') {
            // بررسی اینکه آیا تمام مدارک کاربر تایید شده‌اند
            $documents = Document::where('user_id', $userId)->get();
            if ($documents->every(function ($document) {
                return $document->status === 'approved';
            })) {
                // استفاده از سرویس جدید برای ارتقای سطح
                $levelUpgradeService = app(\App\Services\LevelUpgradeService::class);
                $result = $levelUpgradeService->upgradeUserAfterDocumentApproval(User::find($userId));
                
                if ($result['success']) {
                    $user = User::find($userId);
                    Alert::create([
                        'user_id' => $userId,
                        'type' => 'success',
                        'message' => "تبریک! سطح شما به {$result['new_level']->name} ارتقا یافت."
                    ]);
                }
            }
        }
        
        return ['updated_count' => $updatedCount];
    }

    public function deleteRecordById($id) {}
}
