<?php

namespace App\Repositories\Admin;

use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use App\Models\Level;
use App\Models\LevelItem;
use App\Models\LevelType;
use App\Models\LevelTypeValue;
use Illuminate\Support\Facades\DB;

class LevelRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request){
        $levels = Level::orderBy('priority')->get();
        $levelsArray = [];
        foreach ($levels as $level) {
            $itemsArray = [];
            $levelItems = LevelItem::get();
            foreach($levelItems as $levelItem){
                $levelTypes = LevelType::get();
                $typesArray = [];
                foreach($levelTypes as $levelType){
                    $levelTypeValue = LevelTypeValue::
                        where('level_id', $level->id)
                        ->where('level_item_id', $levelItem->id)
                        ->where('level_type_id', $levelType->id)
                        ->value('value');
                    $levelType['value'] = $levelTypeValue;
                    $typesArray[] = $levelType;
                }
                $levelItem['types'] = $typesArray;
                $itemsArray[] = $levelItem;
            }
            $level['items'] = $itemsArray;
            $levelsArray[] = $level;
        }
        return $levelsArray;
    }

    public function createRecord(array $data){
        DB::transaction(function () use ($data) {
            foreach ($data['levels'] as $levelData) {
                $level = Level::find($levelData['id']);
                
                if ($level) {
                    // Handle boolean fields
                    $levelData['is_unlimited_buy'] = isset($levelData['is_unlimited_buy']) ? true : false;
                    $levelData['is_unlimited_sell'] = isset($levelData['is_unlimited_sell']) ? true : false;
                    $levelData['active'] = isset($levelData['active']) ? true : false;
                    
                    // Handle null values for unlimited fields
                    if ($levelData['is_unlimited_buy']) {
                        $levelData['daily_buy_limit'] = null;
                    }
                    if ($levelData['is_unlimited_sell']) {
                        $levelData['daily_sell_limit'] = null;
                    }
                    
                    // Update the level
                    $level->update($levelData);
                }
            }
        });
        
        return response()->json([
            'status' => 'success',
            'message' => 'سطوح با موفقیت بروزرسانی شدند'
        ]);
    }

    public function getRecordById($id){
        return Level::findOrFail($id);
    }

    public function updateRecord($id, array $data){
        $level = Level::findOrFail($id);
        
        // Handle boolean fields
        $data['is_unlimited_buy'] = isset($data['is_unlimited_buy']) ? true : false;
        $data['is_unlimited_sell'] = isset($data['is_unlimited_sell']) ? true : false;
        $data['active'] = isset($data['active']) ? true : false;
        
        // Handle null values for unlimited fields
        if ($data['is_unlimited_buy']) {
            $data['daily_buy_limit'] = null;
        }
        if ($data['is_unlimited_sell']) {
            $data['daily_sell_limit'] = null;
        }
        
        $level->update($data);
        return $level;
    }

    public function deleteRecordById($id){
        $level = Level::findOrFail($id);
        return $level->delete();
    }
}

