<?php

namespace App\Repositories\User;

use App\Models\Support;
use App\Models\SupportTicket;
use Illuminate\Support\Facades\Auth;

class SupportRepo
{
    public function getAllRecords($request)
    {
        return Support::where('user_id', Auth::user()->id)->orderBy('updated_at')->paginate(
            perPage: $request->input('per_page', 30),
            page: $request->input('page', 1),
        );
    }

    public function createRecord(array $data)
    {
        $support = Support::create([
            'subject' => $data['subject'],
            'unit_id' => $data['unit_id'],
            'level_id' => $data['level_id'],
            'user_id' => Auth::id(),
            'status' => 'user_response',
        ]);
        $ticket = SupportTicket::create([
            'support_id' => $support->id,
            'user_id' => Auth::id(),
            'message' => $data['message'],
        ]);

        return $ticket;
    }

    public function getRecordById($id)
    {
        return Support::where('id', $id)->where('user_id', Auth::user()->id)->with('tickets')->firstOrFail();
    }

    public function updateRecord($id, array $data)
    {
        $support = Support::where('id', $id)->where('user_id', Auth::user()->id)->firstOrFail();
        $ticket = SupportTicket::create([
            'support_id' => $support->id,
            'user_id' => Auth::user()->id,
            'message' => $data['message'],
        ]);

        return $ticket;
    }
    public function closeRecord($id)
    {
        $support = Support::where('id', $id)->where('user_id', Auth::user()->id)->firstOrFail();
        $support->update([
            'status' => 'deactive',
        ]);
        return $support;
    }

    public function deleteRecordById($id)
    {
        return Support::findOrFail($id)->update([
            'status' => 'deactive',
        ]);
    }

    public function addReply($supportId, array $data)
    {
        $support = Support::where('user_id', Auth::id())
            ->findOrFail($supportId);
            
        $support->update([
            'status' => $data['status']
        ]);

        $ticket = SupportTicket::create([
            'support_id' => $support->id,
            'user_id' => Auth::id(),
            'message' => $data['message']
        ]);

        return $ticket;
    }
}
