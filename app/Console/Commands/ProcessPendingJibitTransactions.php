<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ProcessPendingJibitTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jibit:process-pending-transactions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch the ProcessPendingJibitTransactionsJob to process all pending Jibit transactions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        dispatch(new \App\Jobs\ProcessPendingJibitTransactionsJob())->handle();
        $this->info('ProcessPendingJibitTransactionsJob dispatched.');
    }
} 