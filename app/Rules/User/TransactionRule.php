<?php

namespace App\Rules\User;

use App\Models\Level;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class TransactionRule implements ValidationRule
{
    protected $type;
    protected $currency_id;
    protected $amount;

    public function __construct($amount, $type, $currency_id)
    {
        $this->amount = $amount;
        $this->type = $type;
        $this->currency_id = $currency_id;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user();
        
        // دریافت سطح کاربر از دیتابیس
        $currentLevel = Level::where('priority', $user->level)->first();
        if (!$currentLevel) {
            $fail('سطح کاربری شما تعریف نشده است.');
            return;
        }

        switch ($this->type) {
            case 'buy':
                $buyTransactions = $user->transactions()->where('type', 'buy')->where('currency_id', $this->currency_id)->where('status', 'done');

                // بررسی محدودیت سطح برنزی
                if ($currentLevel->priority == 0) {
                    $fail('برای خرید ارز، سطح کاربری خود را ارتقاء دهید.');
                }

                // بررسی محدودیت خرید روزانه بر اساس سطح
                if ($currentLevel->daily_buy_limit > 0) {
                    $todayBuyAmount = intval($buyTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailyBuy = intval($currentLevel->daily_buy_limit);

                    if ($todayBuyAmount + intval($this->amount) > $maxDailyBuy) {
                        $remainingToday = max(0, $maxDailyBuy - $todayBuyAmount);
                        $fail("شما به سقف خرید روزانه رسیده‌اید. محدودیت خرید در سطح {$currentLevel->name} تا سقف " . number_format($maxDailyBuy) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                // بررسی موجودی تومانی
                if (intval($this->amount) > $user->toman_balance) {
                    $fail("موجودی تومانی کافی نیست. موجودی فعلی: " . number_format($user->toman_balance) . " تومان");
                }
                break;

            case 'sell':
                $sellTransactions = $user->transactions()->where('type', 'sell')->where('currency_id', $this->currency_id)->where('status', 'done');

                // بررسی محدودیت سطح برنزی
                if ($currentLevel->priority == 0) {
                    $fail('شما شرایط فروش ندارید.');
                }

                // بررسی محدودیت فروش روزانه بر اساس سطح
                if ($currentLevel->daily_sell_limit > 0) {
                    $todaySellAmount = intval($sellTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailySell = intval($currentLevel->daily_sell_limit);

                    if ($todaySellAmount + intval($this->amount) > $maxDailySell) {
                        $remainingToday = max(0, $maxDailySell - $todaySellAmount);
                        $fail("شما به سقف فروش روزانه رسیده‌اید. محدودیت فروش در سطح {$currentLevel->name} تا سقف " . number_format($maxDailySell) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                // بررسی موجودی ارز
                $wallet = $user->wallets()->where('coin_id', $this->currency_id)->first();
                if (!$wallet || $wallet->balance < $this->amount) {
                    $currentBalance = $wallet ? $wallet->balance : 0;
                    $fail("موجودی ارز شما کافی نیست. موجودی فعلی: " . number_format($currentBalance, 8));
                }
                break;
        }
    }
}
