<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JibitPayment;
use Illuminate\Http\Request;
use App\Services\JibitPaymentService;

class JibitController extends Controller
{
    /**
     * نمایش لیست واریزهای شناسه‌دار در انتظار لیبل
     */
    public function waitingLabeledDeposits(Request $request, JibitPaymentService $jibitService)
    {
        $iban = '**************************';
        $referenceNumber = $request->get('referenceNumber', 'waitingForVerify');

        $result = $jibitService->fetchAugStatementWaitingForVerify($iban);
        $data = $result['data'] ?? [];
        $elements = $data['elements'] ?? [];
        $pagination = [
            'pageNumber' => $data['pageNumber'] ?? 1,
            'size' => $data['size'] ?? 50,
            'numberOfElements' => $data['numberOfElements'] ?? 0,
            'hasNext' => $data['hasNext'] ?? false,
            'hasPrevious' => $data['hasPrevious'] ?? false,
        ];

        return view('admin.jibit.waiting_deposits', compact('elements', 'pagination', 'iban', 'referenceNumber'));
    }

    public function approveAugTransaction(Request $request, JibitPaymentService $jibitService)
    {
        $iban = '**************************';
        $referenceNumber = $request->get('referenceNumber');
        if (!$referenceNumber) {
            return response()->json(['success' => false, 'message' => 'referenceNumber الزامی است.']);
        }
        $result = $jibitService->approveAugStatementTransaction($iban, $referenceNumber);
        return response()->json($result);
    }

    public function rejectAugTransaction(Request $request, JibitPaymentService $jibitService)
    {
        $iban = '**************************';
        $referenceNumber = $request->get('referenceNumber');
        if (!$referenceNumber) {
            return response()->json(['success' => false, 'message' => 'referenceNumber الزامی است.']);
        }
        $result = $jibitService->rejectAugStatementTransaction($iban, $referenceNumber);
        return response()->json($result);
    }

    public function bankAccountsPage(JibitPaymentService $jibitService)
    {
        $result = $jibitService->getBankAccounts();
        $accounts = $result['data'] ?? [];
        return view('admin.jibit.bank_accounts', compact('accounts'));
    }
} 