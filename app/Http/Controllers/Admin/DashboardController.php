<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TomanWithdrawal;
use App\Models\UsdPrice;
use App\Models\WithdrawHistory;
use App\Services\Admin\DashboardService;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Alert;
use App\Models\Transaction;

class DashboardController extends Controller
{
    public function __construct(
        protected ?DashboardService $service = null
    ) {}

    public function index()
    {
        // کاربران آنلاین - فقط کاربرانی که در 15 دقیقه اخیر فعال بوده‌اند
        $onlineUsers = User::whereHas('sessions', function($query) {
                $query->where('last_activity', '>=', now()->subMinutes(15)->timestamp);
            })

            ->select('id', 'firstname', 'lastname', 'email')
            ->limit(5)
            ->get();
         $countUsers = User::count();   
        // دریافت هشدارهای اخیر کاربران
        $userAlerts = Alert::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        $TomanBalances = User::select('toman_balance')->average('toman_balance');
        // dd($TomanBalances);
        $systemStatus = [
            'server' => [
                'status' => true, // یا false
                'message' => 'فعال', // یا 'غیرفعال'
                'uptime' => 100,
            ],
            'database' => [
                'status' => true,
                'message' => 'فعال',
                'uptime' => 1,
            ],
            'memory' => [
                'status' => $this->getMemoryStatus(),
                'usage' => $this->getMemoryUsage(),
            ],
            'cpu' => [
                'status' => $this->getCpuStatus(),
                'usage' => $this->getCpuUsage(),
            ],
        ];
        $Toman_Withdrawals = TomanWithdrawal::where('status','pending')->count();
        $UsdPrice = UsdPrice::getActive();
        
        // آمار روزانه خرید و فروش
        $dailyTradingStats = $this->getDailyTradingStats();
        
        // آخرین تراکنش‌ها
        $latestTransactions = Transaction::with(['user'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        return view('admin.dashboard.index', compact('systemStatus','TomanBalances', 'UsdPrice','Toman_Withdrawals','onlineUsers', 'userAlerts','countUsers', 'latestTransactions', 'dailyTradingStats'));
    }

    private function getMemoryStatus()
    {
        $usage = $this->getMemoryUsage();
        if ($usage > 90) return 'danger';
        if ($usage > 70) return 'warning';
        return 'success';
    }

    private function getMemoryUsage()
    {
        // اینجا کد واقعی برای دریافت مصرف حافظه
        return memory_get_usage(true) / 1024 / 1024;
    }

    private function getCpuStatus()
    {
        $usage = $this->getCpuUsage();
        if ($usage > 90) return 'danger';
        if ($usage > 70) return 'warning';
        return 'info';
    }

    private function getCpuUsage()
    {
        // اینجا کد واقعی برای دریافت مصرف CPU
        return sys_getloadavg()[0] * 100;
    }

    /**
     * محاسبه آمار روزانه خرید و فروش
     */
    private function getDailyTradingStats()
    {
        $today = date('Y-m-d');
        
        // تراکنش‌های امروز
        $todayTransactions = Transaction::where('status', 'done')
            ->whereDate('created_at', $today)
            ->get();

        // فیلتر کردن تراکنش‌های خرید و فروش برای محاسبه کل
        $buySellTransactions = $todayTransactions->whereIn('type', ['buy', 'sell']);
        $totalBuy = $buySellTransactions->where('type', 'buy')->sum('price');
        $totalSell = $buySellTransactions->where('type', 'sell')->sum('price');
        
        // محاسبه سود بر اساس تفاوت قیمت خرید و فروش
        $profit = $this->calculateTradingProfit($todayTransactions);
        
        // آمار به تفکیک ارز
        $currencyStats = $this->getCurrencyStats($todayTransactions);
        
        // آمار سواپ
        $swapStats = $this->getSwapStats($todayTransactions);
        
        return [
            'total_buy' => $totalBuy,
            'formatted_total_buy' => number_format($totalBuy) . ' تومان',
            'total_sell' => $totalSell,
            'formatted_total_sell' => number_format($totalSell) . ' تومان',
            'net_flow' => $totalBuy - $totalSell,
            'formatted_net_flow' => number_format($totalBuy - $totalSell) . ' تومان',
            'profit' => $profit['profit_toman'],
            'formatted_profit' => number_format($profit['profit_toman']) . ' تومان',
            'profit_percentage' => $totalSell > 0 ? round(($profit['profit_toman'] / $totalSell) * 100, 2) : 0,
            'currency_stats' => $currencyStats,
            'swap_stats' => $swapStats,
            'transaction_count' => [
                'buy' => $buySellTransactions->where('type', 'buy')->count(),
                'sell' => $buySellTransactions->where('type', 'sell')->count(),
                'swap' => $todayTransactions->whereIn('type', ['swap_in', 'swap_out'])->count(),
                'total' => $todayTransactions->count()
            ],
            'date' => $today
        ];
    }

    /**
     * محاسبه سود معاملات
     */
    public function calculateTradingProfit($transactions)
    {
        $profit = 0;
        $profitInUsd = 0;
        $currencyTransactions = [];
        
        // فیلتر کردن فقط تراکنش‌های خرید و فروش (بدون سواپ)
        $buySellTransactions = $transactions->whereIn('type', ['buy', 'sell']);
        
        // گروه‌بندی تراکنش‌ها بر اساس ارز
        foreach ($buySellTransactions as $transaction) {
            $currencyId = $transaction->currency_id;
            if (!isset($currencyTransactions[$currencyId])) {
                $currencyTransactions[$currencyId] = [
                    'buy_transactions' => [],
                    'sell_transactions' => []
                ];
            }
            
            if ($transaction->type === 'buy') {
                $currencyTransactions[$currencyId]['buy_transactions'][] = $transaction;
            } elseif ($transaction->type === 'sell') {
                $currencyTransactions[$currencyId]['sell_transactions'][] = $transaction;
            }
        }
        
        // محاسبه سود برای هر ارز
        foreach ($currencyTransactions as $currencyId => $currencyData) {
            $buyTransactions = $currencyData['buy_transactions'];
            $sellTransactions = $currencyData['sell_transactions'];
            
            // محاسبه میانگین قیمت خرید (قیمت واحد)
            $totalBuyAmount = 0;
            $totalBuyVolume = 0;
            foreach ($buyTransactions as $buyTx) {
                $totalBuyAmount += $buyTx->price; // کل مبلغ تومانی
                $totalBuyVolume += $buyTx->amount; // حجم ارز
            }
            
            $averageBuyPricePerUnit = $totalBuyVolume > 0 ? $totalBuyAmount / $totalBuyVolume : 0;
            
            // محاسبه سود برای فروش‌ها
            foreach ($sellTransactions as $sellTx) {
                if ($averageBuyPricePerUnit > 0) {
                    $sellPricePerUnit = $sellTx->price / $sellTx->amount; // قیمت واحد فروش
                    $quantity = $sellTx->amount;
                    
                    // سود به تومان = (قیمت فروش واحد - قیمت خرید واحد) × حجم
                    $transactionProfit = ($averageBuyPricePerUnit - $sellPricePerUnit) * $quantity;
                    $profit += $transactionProfit;
                    
                    // تبدیل سود به دلار با استفاده از قیمت دلار لحظه‌ای تراکنش
                    $sellUsdRate = $this->getUsdRateFromTransaction($sellTx);
                    if ($sellUsdRate > 0) {
                        $profitInUsd += $transactionProfit / $sellUsdRate;
                    }
                }
            }
        }
        
        return [
            'profit_toman' => $profit,
            'profit_usd' => $profitInUsd
        ];
    }

    /**
     * دریافت قیمت دلار از جزئیات تراکنش
     */
    private function getUsdRateFromTransaction($transaction)
    {
        if (!$transaction->details) {
            return 0;
        }
        
        $details = is_string($transaction->details) ? json_decode($transaction->details, true) : $transaction->details;
        
        // بررسی فیلدهای مختلف که ممکن است قیمت دلار را نگه دارند
        if (isset($details['usd_rate'])) {
            return (float) $details['usd_rate'];
        }
        
        if (isset($details['usd_price'])) {
            return (float) $details['usd_price'];
        }
        
        if (isset($details['dollar_rate'])) {
            return (float) $details['dollar_rate'];
        }
        
        return 0;
    }

    /**
     * آمار به تفکیک ارز
     */
    private function getCurrencyStats($transactions)
    {
        $currencyStats = [];
        $currencyTransactions = [];
        
        // فیلتر کردن فقط تراکنش‌های خرید و فروش (بدون سواپ)
        $buySellTransactions = $transactions->whereIn('type', ['buy', 'sell']);
        
        // گروه‌بندی تراکنش‌ها بر اساس ارز
        foreach ($buySellTransactions as $transaction) {
            $currencyId = $transaction->currency_id;
            if (!isset($currencyTransactions[$currencyId])) {
                $currencyTransactions[$currencyId] = [
                    'buy_transactions' => [],
                    'sell_transactions' => []
                ];
            }
            
            if ($transaction->type === 'buy') {
                $currencyTransactions[$currencyId]['buy_transactions'][] = $transaction;
            } elseif ($transaction->type === 'sell') {
                $currencyTransactions[$currencyId]['sell_transactions'][] = $transaction;
            }
        }
        
        foreach ($currencyTransactions as $currencyId => $currencyData) {
            $buyTransactions = $currencyData['buy_transactions'];
            $sellTransactions = $currencyData['sell_transactions'];
            
            // محاسبه آمار خرید
            $buyAmount = 0;
            $buyVolume = 0;
            $buyUsdRates = [];
            foreach ($buyTransactions as $buyTx) {
                $buyAmount += $buyTx->price; // کل مبلغ تومانی
                $buyVolume += $buyTx->amount; // حجم ارز
                $usdRate = $this->getUsdRateFromTransaction($buyTx);
                if ($usdRate > 0) {
                    $buyUsdRates[] = $usdRate;
                }
            }
            
            // محاسبه آمار فروش
            $sellAmount = 0;
            $sellVolume = 0;
            $sellUsdRates = [];
            foreach ($sellTransactions as $sellTx) {
                $sellAmount += $sellTx->price; // کل مبلغ تومانی
                $sellVolume += $sellTx->amount; // حجم ارز
                $usdRate = $this->getUsdRateFromTransaction($sellTx);
                if ($usdRate > 0) {
                    $sellUsdRates[] = $usdRate;
                }
            }
            
            // محاسبه میانگین قیمت خرید (قیمت واحد)
            $averageBuyPricePerUnit = $buyVolume > 0 ? $buyAmount / $buyVolume : 0;
            $averageSellPricePerUnit = $sellVolume > 0 ? $sellAmount / $sellVolume : 0;
            
            // محاسبه میانگین قیمت دلار
            $averageBuyUsdRate = !empty($buyUsdRates) ? array_sum($buyUsdRates) / count($buyUsdRates) : 0;
            $averageSellUsdRate = !empty($sellUsdRates) ? array_sum($sellUsdRates) / count($sellUsdRates) : 0;
            
            // محاسبه سود
            $profit = 0;
            $profitInUsd = 0;
            foreach ($sellTransactions as $sellTx) {
                if ($averageBuyPricePerUnit > 0) {
                    $sellPricePerUnit = $sellTx->price / $sellTx->amount;
                    $quantity = $sellTx->amount;
                    $transactionProfit = ($averageBuyPricePerUnit - $sellPricePerUnit) * $quantity;
                    $profit += $transactionProfit;
                    
                    // تبدیل سود به دلار با استفاده از قیمت دلار لحظه‌ای تراکنش
                    $sellUsdRate = $this->getUsdRateFromTransaction($sellTx);
                    if ($sellUsdRate > 0) {
                        $profitInUsd += $transactionProfit / $sellUsdRate;
                    }
                }
            }
            
            // نام و نماد ارز
            $currencyName = !empty($buyTransactions) ? $buyTransactions[0]->currency->name : 
                           (!empty($sellTransactions) ? $sellTransactions[0]->currency->name : 'نامشخص');
            $currencySymbol = !empty($buyTransactions) ? $buyTransactions[0]->currency->symbol : 
                             (!empty($sellTransactions) ? $sellTransactions[0]->currency->symbol : '');
            
            $currencyStats[$currencyId] = [
                'name' => $currencyName,
                'symbol' => $currencySymbol,
                'buy_amount' => $buyAmount,
                'sell_amount' => $sellAmount,
                'buy_volume' => $buyVolume,
                'sell_volume' => $sellVolume,
                'profit' => $profit,
                'profit_usd' => $profitInUsd,
                'average_buy_price' => $averageBuyPricePerUnit,
                'average_sell_price' => $averageSellPricePerUnit,
                'average_buy_usd_rate' => $averageBuyUsdRate,
                'average_sell_usd_rate' => $averageSellUsdRate
            ];
        }
        
        // فرمت کردن اعداد
        foreach ($currencyStats as &$stat) {
            $stat['formatted_buy_amount'] = number_format($stat['buy_amount']) . ' تومان';
            $stat['formatted_sell_amount'] = number_format($stat['sell_amount']) . ' تومان';
            $stat['formatted_profit'] = number_format($stat['profit']) . ' تومان';
            $stat['formatted_profit_usd'] = number_format($stat['profit_usd'], 2) . ' دلار';
            $stat['formatted_buy_volume'] = number_format($stat['buy_volume'], 8);
            $stat['formatted_sell_volume'] = number_format($stat['sell_volume'], 8);
            $stat['formatted_average_buy_price'] = number_format($stat['average_buy_price']) . ' تومان';
            $stat['formatted_average_sell_price'] = number_format($stat['average_sell_price']) . ' تومان';
            $stat['formatted_average_buy_usd_rate'] = number_format($stat['average_buy_usd_rate']) . ' تومان';
            $stat['formatted_average_sell_usd_rate'] = number_format($stat['average_sell_usd_rate']) . ' تومان';
        }
        
        return array_values($currencyStats);
    }

    /**
     * محاسبه آمار سواپ
     */
    private function getSwapStats($transactions)
    {
        // فقط تراکنش‌های سواپ را در نظر بگیر
        $swapTransactions = $transactions->whereIn('type', ['swap_in', 'swap_out']);
        
        if ($swapTransactions->isEmpty()) {
            return [
                'total_swaps' => 0,
                'total_swap_volume' => 0,
                'total_swap_fees' => 0,
                'total_swap_usd_value' => 0,
                'formatted_total_swaps' => '0',
                'formatted_total_swap_volume' => '0',
                'formatted_total_swap_fees' => '0',
                'formatted_total_swap_usd_value' => '$0',
                'swap_pairs' => []
            ];
        }
        
        $totalSwaps = $swapTransactions->where('type', 'swap_out')->count();
        $totalSwapVolume = 0;
        $totalSwapFees = 0;
        $totalSwapUsdValue = 0;
        $swapPairs = [];
        
        // گروه‌بندی سواپ‌ها بر اساس جفت ارز
        foreach ($swapTransactions->where('type', 'swap_out') as $swapOut) {
            $details = json_decode($swapOut->details, true);
            
            if ($details) {
                $fromCurrency = $details['from_currency'] ?? 'نامشخص';
                $toCurrency = $details['to_currency'] ?? 'نامشخص';
                $pairKey = $fromCurrency . '_' . $toCurrency;
                
                if (!isset($swapPairs[$pairKey])) {
                    $swapPairs[$pairKey] = [
                        'from_currency' => $fromCurrency,
                        'to_currency' => $toCurrency,
                        'count' => 0,
                        'total_volume' => 0,
                        'total_fees' => 0,
                        'total_usd_value' => 0
                    ];
                }
                
                $swapPairs[$pairKey]['count']++;
                $swapPairs[$pairKey]['total_volume'] += $details['from_amount'] ?? 0;
                $swapPairs[$pairKey]['total_fees'] += $details['fee_amount'] ?? 0;
                $swapPairs[$pairKey]['total_usd_value'] += $details['usd_value'] ?? 0;
                
                $totalSwapVolume += $details['from_amount'] ?? 0;
                $totalSwapFees += $details['fee_amount'] ?? 0;
                $totalSwapUsdValue += $details['usd_value'] ?? 0;
            }
        }
        
        // فرمت کردن آمار جفت ارزها
        foreach ($swapPairs as &$pair) {
            $pair['formatted_total_volume'] = number_format($pair['total_volume'], 8);
            $pair['formatted_total_fees'] = number_format($pair['total_fees'], 8);
            $pair['formatted_total_usd_value'] = '$' . number_format($pair['total_usd_value'], 2);
        }
        
        return [
            'total_swaps' => $totalSwaps,
            'total_swap_volume' => $totalSwapVolume,
            'total_swap_fees' => $totalSwapFees,
            'total_swap_usd_value' => $totalSwapUsdValue,
            'formatted_total_swaps' => number_format($totalSwaps),
            'formatted_total_swap_volume' => number_format($totalSwapVolume, 8),
            'formatted_total_swap_fees' => number_format($totalSwapFees, 8),
            'formatted_total_swap_usd_value' => '$' . number_format($totalSwapUsdValue, 2),
            'swap_pairs' => array_values($swapPairs)
        ];
    }

    /**
     * دریافت آمار روزانه خرید و فروش به صورت API
     */
    public function getDailyTradingStatsApi(Request $request)
    {
        try {
            $date = $request->get('date', date('Y-m-d'));
            
            // تراکنش‌های روز مشخص شده
            $transactions = Transaction::where('status', 'done')
                ->whereDate('created_at', $date)
                ->with('currency')
                ->get();

            // فیلتر کردن تراکنش‌های خرید و فروش برای محاسبه کل
            $buySellTransactions = $transactions->whereIn('type', ['buy', 'sell']);
            $totalBuy = $buySellTransactions->where('type', 'buy')->sum('price');
            $totalSell = $buySellTransactions->where('type', 'sell')->sum('price');
            
            // محاسبه سود
            $profit = $this->calculateTradingProfit($transactions);
            
            // آمار به تفکیک ارز
            $currencyStats = $this->getCurrencyStats($transactions);
            
            // آمار سواپ
            $swapStats = $this->getSwapStats($transactions);
            
            $stats = [
                'date' => $date,
                'total_buy' => $totalBuy,
                'formatted_total_buy' => number_format($totalBuy) . ' تومان',
                'total_sell' => $totalSell,
                'formatted_total_sell' => number_format($totalSell) . ' تومان',
                'net_flow' => $totalBuy - $totalSell,
                'formatted_net_flow' => number_format($totalBuy - $totalSell) . ' تومان',
                'profit' => $profit['profit_toman'],
                'formatted_profit' => number_format($profit['profit_toman']) . ' تومان',
                'profit_usd' => $profit['profit_usd'],
                'formatted_profit_usd' => number_format($profit['profit_usd'], 2) . ' دلار',
                'profit_percentage' => $totalSell > 0 ? round(($profit['profit_toman'] / $totalSell) * 100, 2) : 0,
                'currency_stats' => $currencyStats,
                'swap_stats' => $swapStats,
                'transaction_count' => [
                    'buy' => $buySellTransactions->where('type', 'buy')->count(),
                    'sell' => $buySellTransactions->where('type', 'sell')->count(),
                    'swap' => $transactions->whereIn('type', ['swap_in', 'swap_out'])->count(),
                    'total' => $transactions->count()
                ],
                'summary' => [
                    'total_volume' => $transactions->sum('amount'),
                    'formatted_total_volume' => number_format($transactions->sum('amount'), 8),
                    'average_transaction_size' => $transactions->count() > 0 ? $transactions->sum('price') / $transactions->count() : 0,
                    'formatted_average_transaction_size' => $transactions->count() > 0 ? number_format($transactions->sum('price') / $transactions->count()) . ' تومان' : '0 تومان'
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'آمار روزانه با موفقیت دریافت شد'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت آمار: ' . $e->getMessage()
            ], 500);
        }
    }
}
