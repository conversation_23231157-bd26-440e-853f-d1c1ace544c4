<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Http\Repositories\AuthRepositories;
use App\Models\Activity;
use App\Models\Coin;
use App\Models\Level;
use App\Models\Network;
use App\Models\Role;
use App\Models\User;
use App\Models\Wallet;
use App\Services\LevelUpgradeService;
use Illuminate\Http\Request;

class UserController extends Controller
{
    protected AuthRepositories $repository;

    public function __construct()
    {
        $this->repository = new AuthRepositories;
    }
    /**
     * Display a listing of the resource.
     */
    public function CreateWalletbyCoin(Request $request, $id)
    {
        $this->repository->createUserWalletByCoin($id, $request->coin_type);
        return redirect()->back();
    }
    public function index()
    {
        $users = User::with(['roles', 'documents', 'wallets', 'transactions'])->paginate(15);
        $activities = Activity::with('user')
            ->orderByDesc('created_at')
            ->paginate(20);

        // Add these statistics
        $totalUsers = User::count();
        $activeUsers = User::where('status', 'approved')->count();
        $newUsers = User::whereMonth('created_at', now()->month)->count();
        $onlineUsers = User::whereHas('sessions', function($query) {
            $query->where('last_activity', '>=', now()->subMinutes(15)->timestamp);
        })->count();

        // Get all roles for the search dropdown
        $roles = Role::all();

        // Get user registration data for charts
        $monthlyRegistrations = $this->getMonthlyRegistrations();
        $weeklyRegistrations = $this->getWeeklyRegistrations();
        $dailyRegistrations = $this->getDailyRegistrations();

        // Get verification status data for charts
        $verificationStats = $this->getVerificationStats();

        return view('admin.users.index', compact(
            'users',
            'activities',
            'totalUsers',
            'activeUsers',
            'newUsers',
            'onlineUsers',
            'roles',
            'monthlyRegistrations',
            'weeklyRegistrations',
            'dailyRegistrations',
            'verificationStats'
        ));
    }

    /**
     * جستجوی سریع کاربران
     */
    public function quickSearch(Request $request)
    {
        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json(['users' => []]);
        }

        $users = User::where(function ($q) use ($query) {
            $q->where('firstname', 'LIKE', "%{$query}%")
              ->orWhere('lastname', 'LIKE', "%{$query}%")
              ->orWhere('email', 'LIKE', "%{$query}%")
              ->orWhere('phone', 'LIKE', "%{$query}%")
              ->orWhere('national_id', 'LIKE', "%{$query}%")
              ->orWhereRaw("CONCAT(firstname, ' ', lastname) LIKE ?", ["%{$query}%"]);
        })
        ->select('id', 'firstname', 'lastname', 'email', 'phone', 'national_id', 'status')
        ->limit(5)
        ->get();

        return response()->json(['users' => $users]);
    }



    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'phone' => 'required|string|unique:users',
            'roles' => 'required|array',
        ]);

        $user = User::create($validated);
        $user->roles()->sync($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت ایجاد شد.');
    }

    public function edit($id)
    {
        $user = User::findOrFail($id);
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,'.$id,
            'phone' => 'required|string|unique:users,phone,'.$id,
            'roles' => 'required|array',
        ]);

        $user->update($validated);
        $user->roles()->sync($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت بروزرسانی شد.');
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت حذف شد.');
    }

    /**
     * Get monthly user registrations for the last 12 months
     */
    private function getMonthlyRegistrations()
    {
        $data = [];
        $labels = [];

        // Get data for the last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = User::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();

            $data[] = $count;
            $labels[] = $month->format('F');
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get weekly user registrations for the last 7 days
     */
    private function getWeeklyRegistrations()
    {
        $data = [];
        $labels = [
            'شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه'
        ];

        // Get start of the week (Saturday in Iranian calendar)
        $startOfWeek = now()->startOfWeek(6); // 6 is Saturday in Carbon

        // Get data for each day of the week
        for ($i = 0; $i < 7; $i++) {
            $day = $startOfWeek->copy()->addDays($i);
            $count = User::whereDate('created_at', $day->toDateString())->count();
            $data[] = $count;
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get hourly user registrations for the last 24 hours
     */
    private function getDailyRegistrations()
    {
        $data = [];
        $labels = [];

        // Get data for the last 24 hours
        for ($i = 0; $i < 24; $i++) {
            $hour = sprintf('%02d', $i);
            $labels[] = $hour;

            $count = User::whereDate('created_at', today())
                ->whereRaw('HOUR(created_at) = ?', [$i])
                ->count();

            $data[] = $count;
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get verification status statistics
     */
    private function getVerificationStats()
    {
        // Count users with all required documents approved
        $fullyVerified = User::whereHas('documents', function($query) {
            $query->where('name', 'id')->where('status', 'approved');
        })->whereHas('documents', function($query) {
            $query->where('name', 'selfie')->where('status', 'approved');
        })->whereHas('documents', function($query) {
            $query->where('name', 'consent')->where('status', 'approved');
        })->count();

        // Count users with pending documents
        $pendingVerification = User::whereHas('documents', function($query) {
            $query->where('status', 'pending');
        })->count();

        // Count users with no documents or rejected documents
        $notVerified = $this->getTotalUsers() - $fullyVerified - $pendingVerification;

        return [
            'notVerified' => $notVerified,
            'fullyVerified' => $fullyVerified,
            'pendingVerification' => $pendingVerification
        ];
    }

    /**
     * Get total number of users
     */
    private function getTotalUsers()
    {
        return User::count();
    }

    public function show($id)
    {
        $user = User::findOrFail($id);

        // Get all active coins first
        $coins = Coin::where('status', 1)
            ->where(function($query) {
                $query->where('is_withdrawal', 1)
                      ->orWhere('is_deposit', 1);
            })
            ->get();

        // Get user wallets with coin information
        $wallets = Wallet::where('user_id', $id)
            ->join('coins', 'wallets.coin_id', '=', 'coins.id')
            ->select(
                'wallets.*',
                'coins.coin_type',
                'coins.coin_icon',
                'coins.is_withdrawal',
                'coins.is_deposit'
            )
            ->get();

        $networks = Network::where('status', 1)->get();

        // Calculate total balance in USD
        $totalBalanceUSD = 0;
        foreach($wallets as $wallet) {
            $balance = $wallet->balance ?? 0;
            $coinType = $wallet->coin_type;
            $totalBalanceUSD += get_coin_usd_value($balance, $coinType);
        }

        // Load other relationships
        $user->load([
            'cards.bank',
            'transactions.currency',
            'documents',
            'tickets.unit',
            'tickets.level',
            'roles'
        ]);

        return view('admin.users.show', compact(
            'user',
            'wallets',
            'coins',
            'networks',
            'totalBalanceUSD'
        ));
    }

    /**
     * ارتقای دستی سطح کاربر
     */
    public function upgradeLevel(Request $request, $id)
    {
        $request->validate([
            'level_id' => 'required|exists:levels,id',
            'reason' => 'nullable|string|max:500'
        ]);

        $user = User::findOrFail($id);
        $targetLevel = Level::findOrFail($request->level_id);
        $levelUpgradeService = app(LevelUpgradeService::class);

        // بررسی اینکه سطح هدف بالاتر از سطح فعلی باشد
        if ($targetLevel->priority <= $user->level) {
            return response()->json([
                'success' => false,
                'message' => 'سطح انتخاب شده باید بالاتر از سطح فعلی کاربر باشد'
            ], 400);
        }

        $result = $levelUpgradeService->manualUpgradeUserLevel($user, $targetLevel, $request->reason);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => "سطح کاربر با موفقیت به {$targetLevel->name} ارتقا یافت",
                'data' => [
                    'user_id' => $user->id,
                    'new_level' => [
                        'id' => $targetLevel->id,
                        'name' => $targetLevel->name,
                        'title' => $targetLevel->title,
                        'priority' => $targetLevel->priority
                    ],
                    'previous_level' => [
                        'id' => $result['previous_level']->id,
                        'name' => $result['previous_level']->name,
                        'title' => $result['previous_level']->title
                    ]
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * تنزل دستی سطح کاربر
     */
    public function downgradeLevel(Request $request, $id)
    {
        $request->validate([
            'level_id' => 'required|exists:levels,id',
            'reason' => 'nullable|string|max:500'
        ]);

        $user = User::findOrFail($id);
        $targetLevel = Level::findOrFail($request->level_id);
        $levelUpgradeService = app(LevelUpgradeService::class);

        // بررسی اینکه سطح هدف پایین‌تر از سطح فعلی باشد
        if ($targetLevel->priority >= $user->level) {
            return response()->json([
                'success' => false,
                'message' => 'سطح انتخاب شده باید پایین‌تر از سطح فعلی کاربر باشد'
            ], 400);
        }

        $result = $levelUpgradeService->manualUpgradeUserLevel($user, $targetLevel, $request->reason);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => "سطح کاربر با موفقیت به {$targetLevel->name} تنزل یافت",
                'data' => [
                    'user_id' => $user->id,
                    'new_level' => [
                        'id' => $targetLevel->id,
                        'name' => $targetLevel->name,
                        'title' => $targetLevel->title,
                        'priority' => $targetLevel->priority
                    ],
                    'previous_level' => [
                        'id' => $result['previous_level']->id,
                        'name' => $result['previous_level']->name,
                        'title' => $result['previous_level']->title
                    ]
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * دریافت اطلاعات سطح کاربر
     */
    public function getUserLevelInfo($id)
    {
        $user = User::findOrFail($id);
        $levelUpgradeService = app(LevelUpgradeService::class);
        $upgradeInfo = $levelUpgradeService->getUserUpgradeInfo($user);
        $allLevels = Level::orderBy('priority')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->firstname . ' ' . $user->lastname,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'current_level' => $user->level,
                    'total_purchases' => $user->total_purchases
                ],
                'current_level' => [
                    'id' => $upgradeInfo['current_level']->id,
                    'name' => $upgradeInfo['current_level']->name,
                    'title' => $upgradeInfo['current_level']->title,
                    'color' => $upgradeInfo['current_level']->color,
                    'priority' => $upgradeInfo['current_level']->priority,
                    'features' => $upgradeInfo['current_level']->features,
                    'restrictions' => $upgradeInfo['current_level']->restrictions
                ],
                'available_levels' => $allLevels->map(function ($level) use ($user) {
                    return [
                        'id' => $level->id,
                        'name' => $level->name,
                        'title' => $level->title,
                        'color' => $level->color,
                        'priority' => $level->priority,
                        'can_upgrade_to' => $level->priority > $user->level,
                        'can_downgrade_to' => $level->priority < $user->level,
                        'is_current' => $level->priority == $user->level
                    ];
                }),
                'upgrade_info' => [
                    'can_upgrade' => $upgradeInfo['can_upgrade'],
                    'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                    'upgrade_conditions' => $upgradeInfo['upgrade_conditions']
                ]
            ]
        ]);
    }
}
