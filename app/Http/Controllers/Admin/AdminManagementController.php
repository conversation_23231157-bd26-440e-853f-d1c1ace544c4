<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\ModelHasRole;
use App\Models\Permission;
use App\Models\PermissionFromData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class AdminManagementController extends Controller
{
    /**
     * Display a listing of admin users.
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        
        // Get admin role IDs (assuming admin role has ID 1 or name 'admin')
        $adminRoleIds = Role::whereIn('name', ['admin', 'support'])->pluck('id');
        
        // Get users with admin roles
        $adminUserIds = ModelHasRole::whereIn('role_id', $adminRoleIds)
            ->where('model_type', 'App\Models\User')
            ->pluck('model_id');

        $admins = User::whereIn('id', $adminUserIds)
            ->with('roles')
            ->when($search, function ($query, $search) {
                return $query->where(function ($q) use ($search) {
                    $q->where('firstname', 'LIKE', "%{$search}%")
                      ->orWhere('lastname', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%")
                      ->orWhere('phone', 'LIKE', "%{$search}%")
                      ->orWhere('national_id', 'LIKE', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.admin-management.index', compact('admins', 'search'));
    }

    /**
     * Show the form for creating a new admin.
     */
    public function create()
    {
        $roles = Role::whereIn('name', ['admin', 'support'])->get();
        return view('admin.admin-management.create', compact('roles'));
    }

    /**
     * Store a newly created admin in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'required|string|unique:users,phone',
            'national_id' => 'nullable|string|unique:users,national_id',
            'password' => 'required|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'status' => 'required|in:active,inactive',
        ]);

        DB::beginTransaction();
        try {
            // Create user
            $user = User::create([
                'firstname' => $validated['firstname'],
                'lastname' => $validated['lastname'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'national_id' => $validated['national_id'],
                'password' => Hash::make($validated['password']),
                'status' => $validated['status'],
                'is_verified' => 1,
                'role' => 1, // Admin role
            ]);

            // Assign role
            ModelHasRole::create([
                'role_id' => $validated['role_id'],
                'model_type' => 'App\Models\User',
                'model_id' => $user->id,
            ]);

            DB::commit();

            return redirect()->route('admin.admin-management.index')
                ->with('success', 'ادمین جدید با موفقیت ایجاد شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در ایجاد ادمین: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified admin.
     */
    public function show($id)
    {
        $admin = User::with('roles')->findOrFail($id);
        return view('admin.admin-management.show', compact('admin'));
    }

    /**
     * Show the form for editing the specified admin.
     */
    public function edit($id)
    {
        $admin = User::with('roles')->findOrFail($id);
        $roles = Role::whereIn('name', ['admin', 'support'])->get();
        $currentRoleId = $admin->roles->first()->id ?? null;
        
        return view('admin.admin-management.edit', compact('admin', 'roles', 'currentRoleId'));
    }

    /**
     * Update the specified admin in storage.
     */
    public function update(Request $request, $id)
    {
        $admin = User::findOrFail($id);
        
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'phone' => 'required|string|unique:users,phone,' . $id,
            'national_id' => 'nullable|string|unique:users,national_id,' . $id,
            'password' => 'nullable|string|min:8|confirmed',
            'role_id' => 'required|exists:roles,id',
            'status' => 'required|in:active,inactive',
        ]);

        DB::beginTransaction();
        try {
            // Update user data
            $updateData = [
                'firstname' => $validated['firstname'],
                'lastname' => $validated['lastname'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'national_id' => $validated['national_id'],
                'status' => $validated['status'],
            ];

            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }

            $admin->update($updateData);

            // Update role
            ModelHasRole::where('model_type', 'App\Models\User')
                ->where('model_id', $admin->id)
                ->delete();

            ModelHasRole::create([
                'role_id' => $validated['role_id'],
                'model_type' => 'App\Models\User',
                'model_id' => $admin->id,
            ]);

            DB::commit();

            return redirect()->route('admin.admin-management.index')
                ->with('success', 'اطلاعات ادمین با موفقیت بروزرسانی شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در بروزرسانی ادمین: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified admin from storage.
     */
    public function destroy($id)
    {
        $admin = User::findOrFail($id);
        
        // Prevent deleting super admin
        if ($admin->super_admin) {
            return back()->withErrors(['error' => 'امکان حذف سوپر ادمین وجود ندارد.']);
        }

        DB::beginTransaction();
        try {
            // Remove role assignments
            ModelHasRole::where('model_type', 'App\Models\User')
                ->where('model_id', $admin->id)
                ->delete();

            // Delete user
            $admin->delete();

            DB::commit();

            return redirect()->route('admin.admin-management.index')
                ->with('success', 'ادمین با موفقیت حذف شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در حذف ادمین: ' . $e->getMessage()]);
        }
    }

    /**
     * Show permissions management for admin.
     */
    public function permissions($id)
    {
        $admin = User::with('roles')->findOrFail($id);
        $roles = Role::all();
        $permissions = PermissionFromData::all()->groupBy('group');
        $userPermissions = Permission::where('role_id', $admin->roles->first()->id ?? 0)->pluck('action_id')->toArray();
        
        return view('admin.admin-management.permissions', compact('admin', 'roles', 'permissions', 'userPermissions'));
    }

    /**
     * Update admin permissions.
     */
    public function updatePermissions(Request $request, $id)
    {
        $admin = User::findOrFail($id);
        $roleId = $admin->roles->first()->id ?? null;
        
        if (!$roleId) {
            return back()->withErrors(['error' => 'کاربر نقشی ندارد.']);
        }

        $permissions = $request->get('permissions', []);

        DB::beginTransaction();
        try {
            // Remove existing permissions
            Permission::where('role_id', $roleId)->delete();

            // Add new permissions
            foreach ($permissions as $permissionId) {
                $permissionData = PermissionFromData::find($permissionId);
                if ($permissionData) {
                    Permission::create([
                        'role_id' => $roleId,
                        'action_id' => $permissionId,
                        'group' => $permissionData->group,
                        'action' => $permissionData->action,
                        'route' => $permissionData->route,
                    ]);
                }
            }

            DB::commit();

            return back()->with('success', 'دسترسی‌ها با موفقیت بروزرسانی شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در بروزرسانی دسترسی‌ها: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle admin status.
     */
    public function toggleStatus($id)
    {
        $admin = User::findOrFail($id);
        
        $admin->update([
            'status' => $admin->status === 'active' ? 'inactive' : 'active'
        ]);

        $status = $admin->status === 'active' ? 'فعال' : 'غیرفعال';
        
        return back()->with('success', "وضعیت ادمین به {$status} تغییر یافت.");
    }
}
