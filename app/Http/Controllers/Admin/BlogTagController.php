<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogTag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogTagController extends Controller
{
    public function index()
    {
        $tags = BlogTag::orderByDesc('created_at')->paginate(20);
        return view('admin.blog.tags.index', compact('tags'));
    }
    public function create()
    {
        return view('admin.blog.tags.create');
    }
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
        ]);
        $data['slug'] = Str::slug($data['name']);
        BlogTag::create($data);
        return redirect()->route('admin.blog.tags.index')->with('success', 'تگ ایجاد شد.');
    }
    public function edit(BlogTag $tag)
    {
        return view('admin.blog.tags.edit', compact('tag'));
    }
    public function update(Request $request, BlogTag $tag)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
        ]);
        $data['slug'] = Str::slug($data['name']);
        $tag->update($data);
        return redirect()->route('admin.blog.tags.index')->with('success', 'تگ ویرایش شد.');
    }
    public function destroy(BlogTag $tag)
    {
        $tag->delete();
        return redirect()->route('admin.blog.tags.index')->with('success', 'تگ حذف شد.');
    }
} 