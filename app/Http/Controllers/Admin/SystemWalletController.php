<?php

namespace App\Http\Controllers\Admin;

use App\Models\Network;
use App\Models\AdminWalletKey;
use App\Models\WalletAddressHistory;
use App\Models\Transaction;
use App\Models\DepositeTransaction;
use App\Models\WithdrawHistory;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SystemWalletService;
use App\Http\Requests\Admin\CreateSystemWalletRequest;
use Illuminate\Support\Facades\Log;
use App\Services\BlockchainBalanceService;
use Illuminate\Support\Facades\Response;

class SystemWalletController extends Controller
{
    public function __construct(
        protected SystemWalletService $service
    ) {}

    public function index(Request $request)
    {
        $wallets = AdminWalletKey::with('network')->paginate(15);

        // Load balances for each wallet
        $wallets->getCollection()->transform(function ($wallet) {
            $wallet->current_balance = $wallet->balance;
            return $wallet;
        });

        return view('admin.system_wallet.index', compact('wallets'));
    }

    public function create()
    {
        $networks = Network::where('status', 1)
            ->whereDoesntHave('admin_wallet')
            ->get();
        return view('admin.system_wallet.create', compact('networks'));
    }

    public function store(CreateSystemWalletRequest $request)
    {
        try {
            $result = $this->service->createSystemWalletProccess($request);
            
            if ($result['success']) {
                return redirect()
                    ->route('admin.system-wallets.index')
                    ->with('success', $result['message']);
            }

            return redirect()
                ->back()
                ->withInput()
                ->with('error', $result['message']);

        } catch (\Exception $e) {
            Log::error('Wallet creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'خطا در ایجاد کیف پول: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $wallet = AdminWalletKey::findOrFail($id);
        $networks = Network::where('status', 1)->get();
        return view('admin.system_wallet.edit', compact('wallet', 'networks'));
    }

    public function update(Request $request, $id)
    {
        $result = $this->service->updateWallet($id, $request->validated());
        
        if ($result['success']) {
            return redirect()
                ->route('admin.system-wallets.index')
                ->with('success', 'کیف پول سیستمی با موفقیت بروزرسانی شد');
        }

        return back()->with('error', $result['message']);
    }

    public function toggleStatus(Request $request)
    {
        $wallet = AdminWalletKey::findOrFail($request->id);
        $wallet->status = !$wallet->status;
        $wallet->save();

        return response()->json([
            'success' => true,
            'message' => 'وضعیت کیف پول با موفقیت تغییر کرد'
        ]);
    }

    public function getBalance(Request $request)
    {
        try {
            $wallet = AdminWalletKey::findOrFail($request->wallet_id);
            $networkSlug = $wallet->network->slug;
    
            // $networkType = 
            // $balance = $wallet->balance;
            $balance = BlockchainBalanceService::getBalance($wallet->address, $networkSlug, $wallet->networkId);
            // $balance_usd = get_coin_usd_value($balance, $wallet->network->coin_type);
            return response()->json([
                'success' => true,
                'balance' => number_format($balance, 8, '.', ''),
                'message' => 'موجودی با موفقیت دریافت شد'
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting wallet balance: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'balance' => '0.00000000',
                'message' => 'خطا در دریافت موجودی'
            ]);
        }
    }

    public function userWallets(Request $request)
    {
        // Handle AJAX requests for operations
        if ($request->ajax() || $request->has('action')) {
            return $this->handleWalletOperations($request);
        }

        $query = WalletAddressHistory::with(['user', 'coin', 'network'])
            ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%");
            })->orWhere('address', 'like', "%{$search}%");
        }

        // Filter by coin type
        if ($request->filled('coin_type')) {
            $query->where('coin_type', $request->coin_type);
        }

        // Filter by network
        if ($request->filled('network_id')) {
            $query->where('network_id', $request->network_id);
        }

        $wallets = $query->paginate(20);

        // Get unique coin types and networks for filters
        $coinTypes = WalletAddressHistory::distinct()->pluck('coin_type')->filter();
        $networks = Network::where('status', 1)->get();

        // Add blocked wallets count to the collection for statistics
        $wallets->getCollection()->transform(function ($wallet) {
            $wallet->is_blocked = str_contains($wallet->memo ?? '', 'BLOCKED');
            return $wallet;
        });

        return view('admin.system_wallet.user_wallets', compact('wallets', 'coinTypes', 'networks'));
    }

    private function handleWalletOperations(Request $request)
    {
        $action = $request->input('action');

        switch ($action) {
            case 'get_transactions':
                return $this->getTransactions($request);

            case 'check_balance':
                return $this->checkWalletBalance($request);

            case 'block_wallet':
                return $this->blockWallet($request);

            case 'unblock_wallet':
                return $this->unblockWallet($request);

            case 'export':
                return $this->exportWalletInfo($request);

            case 'export_all':
                return $this->exportAllWallets($request);

            default:
                return response()->json(['success' => false, 'message' => 'عملیات نامشخص']);
        }
    }

    private function getTransactions(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $coinType = $request->input('coin_type');
            $address = $request->input('address');

            // Get transactions from multiple sources
            $transactions = collect();

            // Get from transactions table
            $userTransactions = Transaction::where('user_id', $userId)
                ->whereHas('currency', function($q) use ($coinType) {
                    $q->where('code', $coinType);
                })
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get()
                ->map(function($transaction) {
                    return [
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'status' => $transaction->status,
                        'description' => $transaction->description,
                        'created_at' => jdate($transaction->created_at)->format('Y/m/d H:i'),
                    ];
                });

            // Get from deposit transactions
            $deposits = DepositeTransaction::where('receiver_wallet_id', function($query) use ($userId, $coinType) {
                $query->select('id')
                    ->from('wallets')
                    ->where('user_id', $userId)
                    ->whereHas('currency', function($q) use ($coinType) {
                        $q->where('code', $coinType);
                    });
            })
            ->orderBy('created_at', 'desc')
            ->limit(25)
            ->get()
            ->map(function($deposit) {
                return [
                    'type' => 'deposit',
                    'amount' => $deposit->amount,
                    'status' => $deposit->status == 1 ? 'done' : 'pending',
                    'description' => 'واریز از آدرس: ' . substr($deposit->from_address ?? '', 0, 20) . '...',
                    'created_at' => jdate($deposit->created_at)->format('Y/m/d H:i'),
                ];
            });

            // Get from withdraw history
            $withdrawals = WithdrawHistory::where('user_id', $userId)
                ->where('coin_type', $coinType)
                ->orderBy('created_at', 'desc')
                ->limit(25)
                ->get()
                ->map(function($withdrawal) {
                    return [
                        'type' => 'withdrawal',
                        'amount' => $withdrawal->amount,
                        'status' => $withdrawal->status == 1 ? 'done' : ($withdrawal->status == 0 ? 'pending' : 'rejected'),
                        'description' => 'برداشت به آدرس: ' . substr($withdrawal->address, 0, 20) . '...',
                        'created_at' => jdate($withdrawal->created_at)->format('Y/m/d H:i'),
                    ];
                });

            $transactions = $userTransactions->concat($deposits)->concat($withdrawals)
                ->sortByDesc('created_at')
                ->take(50)
                ->values();

            return response()->json([
                'success' => true,
                'data' => $transactions
            ]);

        } catch (\Exception $e) {
            Log::error('Error getting transactions: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت تراکنش‌ها'
            ]);
        }
    }

    private function checkWalletBalance(Request $request)
    {
        try {
            $address = $request->input('address');
            $coinType = $request->input('coin_type');
            $networkId = $request->input('network_id');

            if (!$networkId) {
                return response()->json([
                    'success' => false,
                    'message' => 'شبکه مشخص نشده است'
                ]);
            }

            $network = Network::find($networkId);
            if (!$network) {
                return response()->json([
                    'success' => false,
                    'message' => 'شبکه یافت نشد'
                ]);
            }

            // Use blockchain service to get balance
            $balance = BlockchainBalanceService::getBalance($address, $network->slug, $networkId);

            return response()->json([
                'success' => true,
                'balance' => number_format($balance, 8, '.', ''),
                'message' => 'موجودی با موفقیت دریافت شد'
            ]);

        } catch (\Exception $e) {
            Log::error('Error checking balance: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در بررسی موجودی'
            ]);
        }
    }

    private function blockWallet(Request $request)
    {
        try {
            $walletId = $request->input('wallet_id');
            $wallet = WalletAddressHistory::find($walletId);

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'کیف پول یافت نشد'
                ]);
            }

            // Add a blocked flag or move to blocked table
            // For now, we'll add a note to the memo field
            $wallet->memo = ($wallet->memo ? $wallet->memo . ' | ' : '') . 'BLOCKED_BY_ADMIN_' . now()->format('Y-m-d_H:i:s');
            $wallet->save();

            Log::info('Wallet blocked by admin', [
                'wallet_id' => $walletId,
                'address' => $wallet->address,
                'admin_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'آدرس کیف پول با موفقیت مسدود شد'
            ]);

        } catch (\Exception $e) {
            Log::error('Error blocking wallet: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در مسدود کردن آدرس'
            ]);
        }
    }

    private function unblockWallet(Request $request)
    {
        try {
            $walletId = $request->input('wallet_id');
            $wallet = WalletAddressHistory::find($walletId);

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'کیف پول یافت نشد'
                ]);
            }

            // Remove blocked flag from memo
            $memo = $wallet->memo ?? '';
            $memo = preg_replace('/\s*\|\s*BLOCKED_BY_ADMIN_[0-9\-_:]+/', '', $memo);
            $memo = preg_replace('/^BLOCKED_BY_ADMIN_[0-9\-_:]+\s*\|\s*/', '', $memo);
            $memo = preg_replace('/^BLOCKED_BY_ADMIN_[0-9\-_:]+$/', '', $memo);

            $wallet->memo = trim($memo);
            $wallet->save();

            Log::info('Wallet unblocked by admin', [
                'wallet_id' => $walletId,
                'address' => $wallet->address,
                'admin_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'مسدودیت آدرس کیف پول با موفقیت رفع شد'
            ]);

        } catch (\Exception $e) {
            Log::error('Error unblocking wallet: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در رفع مسدودیت آدرس'
            ]);
        }
    }

    private function exportWalletInfo(Request $request)
    {
        try {
            $walletId = $request->input('wallet_id');
            $wallet = WalletAddressHistory::with(['user', 'coin', 'network'])->find($walletId);

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'کیف پول یافت نشد'
                ]);
            }

            $data = [
                'شناسه کیف پول' => $wallet->id,
                'نام کاربر' => $wallet->user->first_name . ' ' . $wallet->user->last_name,
                'موبایل' => $wallet->user->mobile,
                'کد ملی' => $wallet->user->national_id,
                'آدرس کیف پول' => $wallet->address,
                'نوع ارز' => $wallet->coin_type,
                'نام ارز' => $wallet->coin?->name,
                'شبکه' => $wallet->network?->name,
                'Memo' => $wallet->memo,
                'تاریخ ایجاد' => jdate($wallet->created_at)->format('Y/m/d H:i:s'),
            ];

            $filename = 'wallet_info_' . $wallet->id . '_' . now()->format('Y_m_d_H_i_s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($data) {
                $file = fopen('php://output', 'w');
                fputcsv($file, array_keys($data));
                fputcsv($file, array_values($data));
                fclose($file);
            };

            return Response::stream($callback, 200, $headers);

        } catch (\Exception $e) {
            Log::error('Error exporting wallet info: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در دانلود اطلاعات'
            ]);
        }
    }

    private function exportAllWallets(Request $request)
    {
        try {
            $query = WalletAddressHistory::with(['user', 'coin', 'network'])
                ->orderBy('created_at', 'desc');

            // Apply same filters as the main view
            if ($request->filled('search')) {
                $search = $request->search;
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('mobile', 'like', "%{$search}%")
                      ->orWhere('national_id', 'like', "%{$search}%");
                })->orWhere('address', 'like', "%{$search}%");
            }

            if ($request->filled('coin_type')) {
                $query->where('coin_type', $request->coin_type);
            }

            if ($request->filled('network_id')) {
                $query->where('network_id', $request->network_id);
            }

            $wallets = $query->get();

            $filename = 'all_user_wallets_' . now()->format('Y_m_d_H_i_s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($wallets) {
                $file = fopen('php://output', 'w');

                // Add BOM for UTF-8
                fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

                // Headers
                fputcsv($file, [
                    'شناسه',
                    'نام کاربر',
                    'موبایل',
                    'کد ملی',
                    'آدرس کیف پول',
                    'نوع ارز',
                    'نام ارز',
                    'شبکه',
                    'Memo',
                    'تاریخ ایجاد'
                ]);

                // Data
                foreach ($wallets as $wallet) {
                    fputcsv($file, [
                        $wallet->id,
                        $wallet->user->first_name . ' ' . $wallet->user->last_name,
                        $wallet->user->mobile,
                        $wallet->user->national_id,
                        $wallet->address,
                        $wallet->coin_type,
                        $wallet->coin?->name,
                        $wallet->network?->name,
                        $wallet->memo,
                        jdate($wallet->created_at)->format('Y/m/d H:i:s'),
                    ]);
                }

                fclose($file);
            };

            return Response::stream($callback, 200, $headers);

        } catch (\Exception $e) {
            Log::error('Error exporting all wallets: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطا در دانلود فایل'
            ]);
        }
    }
}
