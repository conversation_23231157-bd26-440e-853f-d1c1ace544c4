<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Support;
use App\Services\Admin\SupportService;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function __construct(
        protected SupportService $service
    ) {}

    public function index(Request $request)
    {
        $query = Support::with(['user', 'unit', 'level', 'tickets'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by unit
        if ($request->has('unit') && $request->unit !== '') {
            $query->where('unit_id', $request->unit);
        }

        // Filter by level
        if ($request->has('level') && $request->level !== '') {
            $query->where('level_id', $request->level);
        }

        // Search by subject or user
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $tickets = $query->paginate(20);

        // Static filter options
        $units = [
            1 => 'احراز هویت',
            2 => 'مالی',
            3 => 'سایر'
        ];
        
        $levels = [
            1 => 'عادی',
            2 => 'متوسط',
            3 => 'فوری'
        ];

        return view('admin.tickets.index', compact('tickets', 'units', 'levels'));
    }

    public function show($id)
    {
        $ticket = Support::with([
            'tickets',
            'user.roles',
            'user.transactions',
            'user.tickets',
            'unit',
            'level'
        ])->findOrFail($id);
        
        return view('admin.tickets.show', compact('ticket'));
    }

    public function reply(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string'
        ]);

        $ticket = Support::findOrFail($id);
        
        $ticket->update([
            'status' => 'admin_response'
        ]);
// dd(auth()->user());
        $ticket->tickets()->create([
            'message' => $request->message,
            'user_id' => auth()->id(),
        ]);

        return back()->with('success', 'پاسخ شما با موفقیت ثبت شد.');
    }
}
