<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\PermissionFromData;
use App\Models\Permission;
use App\Models\ModelHasRole;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RoleController extends Controller
{
    /**
     * Display a listing of roles.
     */
    public function index()
    {
        $roles = Role::withCount(['users' => function($query) {
            $query->where('model_type', 'App\Models\User');
        }])->get();

        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        return view('admin.roles.create');
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'guard_name' => 'required|string|max:255',
        ]);

        Role::create($validated);

        return redirect()->route('admin.roles.index')
            ->with('success', 'نقش جدید با موفقیت ایجاد شد.');
    }

    /**
     * Display the specified role.
     */
    public function show($id)
    {
        $role = Role::findOrFail($id);
        $users = ModelHasRole::where('role_id', $id)
            ->where('model_type', 'App\Models\User')
            ->with('user')
            ->paginate(20);

        return view('admin.roles.show', compact('role', 'users'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit($id)
    {
        $role = Role::findOrFail($id);
        return view('admin.roles.edit', compact('role'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, $id)
    {
        $role = Role::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $id,
            'guard_name' => 'required|string|max:255',
        ]);

        $role->update($validated);

        return redirect()->route('admin.roles.index')
            ->with('success', 'نقش با موفقیت بروزرسانی شد.');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy($id)
    {
        $role = Role::findOrFail($id);

        // Check if role is being used
        $usersCount = ModelHasRole::where('role_id', $id)
            ->where('model_type', 'App\Models\User')
            ->count();

        if ($usersCount > 0) {
            return back()->withErrors(['error' => 'این نقش در حال استفاده است و قابل حذف نیست.']);
        }

        DB::beginTransaction();
        try {
            // Remove permissions
            Permission::where('role_id', $id)->delete();

            // Remove role
            $role->delete();

            DB::commit();

            return redirect()->route('admin.roles.index')
                ->with('success', 'نقش با موفقیت حذف شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در حذف نقش: ' . $e->getMessage()]);
        }
    }

    /**
     * Show permissions for role.
     */
    public function permissions($id)
    {
        $role = Role::findOrFail($id);
        $permissions = PermissionFromData::all()->groupBy('group');
        $rolePermissions = Permission::where('role_id', $id)->pluck('action_id')->toArray();

        return view('admin.roles.permissions', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update role permissions.
     */
    public function updatePermissions(Request $request, $id)
    {
        $role = Role::findOrFail($id);
        $permissions = $request->get('permissions', []);

        DB::beginTransaction();
        try {
            // Remove existing permissions
            Permission::where('role_id', $id)->delete();

            // Add new permissions
            foreach ($permissions as $permissionId) {
                $permissionData = PermissionFromData::find($permissionId);
                if ($permissionData) {
                    Permission::create([
                        'role_id' => $id,
                        'action_id' => $permissionId,
                        'group' => $permissionData->group,
                        'action' => $permissionData->action,
                        'route' => $permissionData->route,
                    ]);
                }
            }

            DB::commit();

            return back()->with('success', 'دسترسی‌های نقش با موفقیت بروزرسانی شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'خطا در بروزرسانی دسترسی‌ها: ' . $e->getMessage()]);
        }
    }
}
