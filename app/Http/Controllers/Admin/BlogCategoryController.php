<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogCategoryController extends Controller
{
    public function index()
    {
        $categories = BlogCategory::orderByDesc('created_at')->paginate(20);
        return view('admin.blog.categories.index', compact('categories'));
    }
    public function create()
    {
        return view('admin.blog.categories.create');
    }
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
        ]);
        $data['slug'] = Str::slug($data['name']);
        BlogCategory::create($data);
        return redirect()->route('admin.blog.categories.index')->with('success', 'دسته‌بندی ایجاد شد.');
    }
    public function edit(BlogCategory $category)
    {
        return view('admin.blog.categories.edit', compact('category'));
    }
    public function update(Request $request, BlogCategory $category)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
        ]);
        $data['slug'] = Str::slug($data['name']);
        $category->update($data);
        return redirect()->route('admin.blog.categories.index')->with('success', 'دسته‌بندی ویرایش شد.');
    }
    public function destroy(BlogCategory $category)
    {
        $category->delete();
        return redirect()->route('admin.blog.categories.index')->with('success', 'دسته‌بندی حذف شد.');
    }
} 