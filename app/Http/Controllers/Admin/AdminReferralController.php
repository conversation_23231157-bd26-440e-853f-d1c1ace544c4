<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Referral;
use App\Models\User;

class AdminReferralController extends Controller
{
    public function index(Request $request)
    {
        $referrals = Referral::with(['inviterUser', 'invitedUser', 'rewards'])->paginate(30);
        return view('admin.referrals.index', compact('referrals'));
    }

    public function show($userId)
    {
        $user = User::findOrFail($userId);
        $referrals = $user->invitedReferrals()->with(['invitedUser', 'rewards'])->get();
        return view('admin.referrals.show', compact('user', 'referrals'));
    }
} 