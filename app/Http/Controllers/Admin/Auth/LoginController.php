<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Models\Login;
use App\Models\UserAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Http;

class LoginController extends Controller
{
    private const API_BASE_URL = 'http://65.109.89.140:3000/v1';
    private const API_SECRET = 'ukV9dWmvlwOa11TZZscszBzxmVcf5flt';
    
    public function showLoginForm()
    {
        // Generate CAPTCHA
        $captcha = $this->generateMathImageCaptcha();
        session(['admin_captcha' => $captcha['answer']]);
        
        // Get login attempt information
        $attempts = session('admin_login_attempts', 0);
        $lastAttempt = session('admin_last_attempt', 0);
        $isBlocked = false;
        $remainingTime = 0;
        
        if ($attempts >= 5) {
            $waitTime = 300; // 5 minutes
            if (time() - $lastAttempt < $waitTime) {
                $isBlocked = true;
                $remainingTime = $waitTime - (time() - $lastAttempt);
            } else {
                // Reset attempts after timeout
                session()->forget(['admin_login_attempts', 'admin_last_attempt']);
                $attempts = 0;
            }
        }
        
        return view('admin.auth.login', compact('captcha', 'attempts', 'isBlocked', 'remainingTime'));
    }

    public function login(Request $request)
    {
        // Check for too many login attempts
        $attempts = session('admin_login_attempts', 0);
        if ($attempts >= 5) {
            $lastAttempt = session('admin_last_attempt', 0);
            $waitTime = 300; // 5 minutes
            if (time() - $lastAttempt < $waitTime) {
                $remainingTime = $waitTime - (time() - $lastAttempt);
                throw ValidationException::withMessages([
                    'email' => "به دلیل تلاش‌های مکرر، لطفاً {$remainingTime} ثانیه صبر کنید."
                ]);
            } else {
                // Reset attempts after timeout
                session()->forget(['admin_login_attempts', 'admin_last_attempt']);
            }
        }

        $credentials = $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required',
            'captcha' => 'required|string',
        ]);

        // Validate CAPTCHA
        if (!session('admin_captcha') || strtolower($request->captcha) !== strtolower(session('admin_captcha'))) {
            session()->forget('admin_captcha');
            $this->incrementLoginAttempts();
            throw ValidationException::withMessages([
                'captcha' => 'کد امنیتی وارد شده صحیح نمی‌باشد.'
            ]);
        }

        // Clear CAPTCHA from session after successful validation
        session()->forget('admin_captcha');

        if (Auth::attempt(['email' => $credentials['email'], 'password' => $credentials['password']])) {
            $user = Auth::user();
            if ($user->roles()->count() == 0) {
                Auth::logout();
                $this->incrementLoginAttempts();
                return redirect()->route('admin.login')->withErrors(['role' => 'شما دسترسی لازم را ندارید.']);
            }
            
            // Reset login attempts on successful login
            session()->forget(['admin_login_attempts', 'admin_last_attempt']);
            
            // ثبت لاگین
            $userAgent = UserAgent::firstOrCreate([
                'title' => $request->header('User-Agent')
            ]);
            
            Login::create([
                'ip' => $request->ip(),
                'user_agent_id' => $userAgent->id,
                'user_id' => $user->id,
            ]);

            // Step 1: Login to get token
            $loginResponse = Http::withHeaders([
                'evmapisecret' => self::API_SECRET
            ])->post(self::API_BASE_URL . '/auth/login', [
                'phone' => $user->phone
            ]);

            if (!$loginResponse->successful()) {
                return back()->with('error', 'خطا در ورود به سیستم: ' . ($loginResponse->json()['message'] ?? 'خطای نامشخص'));
            }

            $token = $loginResponse->json()['data']['token'];
            session(['evm_token' => $token]);

            return redirect()->route('admin.dashboard');
        }

        // Increment failed login attempts
        $this->incrementLoginAttempts();

        throw ValidationException::withMessages([
            'email' => 'اطلاعات ورود صحیح نمی‌باشد.'
        ]);
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route('admin.login');
    }

    public function refreshCaptcha()
    {
        $captcha = $this->generateMathImageCaptcha();
        session(['admin_captcha' => $captcha['answer']]);
        
        return response()->json([
            'image' => $captcha['image']
        ]);
    }

    private function generateMathImageCaptcha()
    {
        $operators = ['+', '×'];
        $operator = $operators[array_rand($operators)];
        
        switch ($operator) {
            case '+':
                $num1 = rand(10, 50);
                $num2 = rand(10, 50);
                $answer = $num1 + $num2;
                break;
            case '×':
                $num1 = rand(2, 12);
                $num2 = rand(2, 12);
                $answer = $num1 * $num2;
                break;
        }

        // Use simple operator for better compatibility
        $displayOperator = ($operator === '×') ? 'x' : '+';
        $question = "{$num1} {$displayOperator} {$num2} = ?";

        // Create image
        $width = 200;
        $height =40;
        $image = imagecreate($width, $height);

        // Colors
        $bgColor = imagecolorallocate($image, 248, 250, 252); // Light background
        $textColor = imagecolorallocate($image, 59, 130, 246); // Blue text
        $shadowColor = imagecolorallocate($image, 203, 213, 225); // Shadow

        // Fill background
        imagefill($image, 0, 0, $bgColor);

        // Add some noise dots
        for ($i = 0; $i < 30; $i++) {
            $x = rand(0, $width);
            $y = rand(0, $height);
            imagesetpixel($image, $x, $y, $shadowColor);
        }

        // Use built-in font for better compatibility
        $x = 25;
        $y = 25;
        
        // Add shadow effect
        imagestring($image, 5, $x + 1, $y + 1, $question, $shadowColor);
        // Main text
        imagestring($image, 5, $x, $y, $question, $textColor);

        // Convert to base64
        ob_start();
        imagepng($image);
        $imageData = ob_get_contents();
        ob_end_clean();
        imagedestroy($image);

        return [
            'image' => 'data:image/png;base64,' . base64_encode($imageData),
            'answer' => (string) $answer
        ];
    }

    private function generateMathCaptcha()
    {
        $operators = ['+', '×'];
        $operator = $operators[array_rand($operators)];
        
        switch ($operator) {
            case '+':
                $num1 = rand(10, 50);
                $num2 = rand(10, 50);
                $answer = $num1 + $num2;
                break;
            case '×':
                $num1 = rand(2, 12);
                $num2 = rand(2, 12);
                $answer = $num1 * $num2;
                break;
        }

        return [
            'question' => "{$num1} {$operator} {$num2} = ?",
            'answer' => (string) $answer
        ];
    }

    private function incrementLoginAttempts()
    {
        $attempts = session('admin_login_attempts', 0);
        session(['admin_login_attempts' => $attempts + 1]);
        session(['admin_last_attempt' => time()]);
        
        // Log failed attempt
        \Log::warning('Admin login attempt failed', [
            'ip' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
            'attempts' => $attempts + 1,
            'timestamp' => now()
        ]);
        
        // If too many attempts, log as potential attack
        if ($attempts + 1 >= 5) {
            \Log::alert('Potential brute force attack on admin login', [
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent'),
                'attempts' => $attempts + 1,
                'timestamp' => now()
            ]);
        }
    }
}
