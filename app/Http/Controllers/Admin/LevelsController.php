<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Level;
use App\Models\LevelTypeValue;
use Illuminate\Http\Request;
use App\Services\Admin\LevelService;
use App\Http\Requests\Admin\StoreLevelRequest;
use App\Models\User;
use App\Services\Admin\LevelUpgradeService;

class LevelsController extends Controller
{

    public function __construct(
        protected LevelService $service
    ){

    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $levels = Level::orderBy('priority')->get();
        return view('admin.setting.level.index', compact('levels'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLevelRequest $request)
    {
        try {
            $this->service->storeLevels($request->validated());
            return redirect()->route('admin.setting.level.index')
                ->with('success', 'سطوح با موفقیت بروزرسانی شدند');
        } catch (\Exception $e) {
            return redirect()->route('admin.setting.level.index')
                ->with('error', 'خطا در بروزرسانی سطوح: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $level = Level::findOrFail($id);
        return view('admin.setting.level.show', compact('level'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $level = Level::findOrFail($id);
            $level->update($request->all());
            return redirect()->route('admin.setting.level.index')
                ->with('success', 'سطح با موفقیت بروزرسانی شد');
        } catch (\Exception $e) {
            return redirect()->route('admin.setting.level.index')
                ->with('error', 'خطا در بروزرسانی سطح: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * ارتقای سطح کاربر پس از تایید مدارک
     */
    public function upgradeUserAfterDocumentApproval(Request $request, $userId)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        $user = User::findOrFail($userId);
        $levelUpgradeService = app(LevelUpgradeService::class);
        
        $result = $levelUpgradeService->upgradeUserAfterDocumentApproval($user);
        
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'user_id' => $user->id,
                    'new_level' => [
                        'id' => $result['new_level']->id,
                        'name' => $result['new_level']->name,
                        'title' => $result['new_level']->title
                    ],
                    'previous_level' => [
                        'id' => $result['previous_level']->id,
                        'name' => $result['previous_level']->name,
                        'title' => $result['previous_level']->title
                    ]
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        }
    }

    /**
     * بررسی وضعیت ارتقای سطح کاربر
     */
    public function getUserUpgradeStatus($userId)
    {
        $user = User::findOrFail($userId);
        $levelUpgradeService = app(LevelUpgradeService::class);
        
        $upgradeInfo = $levelUpgradeService->getUserUpgradeInfo($user);
        
        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $user->id,
                'current_level' => [
                    'id' => $upgradeInfo['current_level']->id,
                    'name' => $upgradeInfo['current_level']->name,
                    'title' => $upgradeInfo['current_level']->title,
                    'priority' => $upgradeInfo['current_level']->priority
                ],
                'next_level' => $upgradeInfo['next_level'] ? [
                    'id' => $upgradeInfo['next_level']->id,
                    'name' => $upgradeInfo['next_level']->name,
                    'title' => $upgradeInfo['next_level']->title,
                    'priority' => $upgradeInfo['next_level']->priority
                ] : null,
                'can_upgrade' => $upgradeInfo['can_upgrade'],
                'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                'upgrade_conditions' => $upgradeInfo['upgrade_conditions'],
                'document_status' => $upgradeInfo['document_status'],
                'total_purchases' => $user->total_purchases
            ]
        ]);
    }
}
