<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogTag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class BlogPostController extends Controller
{
    public function index(Request $request)
    {
        $query = BlogPost::with(['category', 'user', 'tags']);
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }
        $posts = $query->orderByDesc('created_at')->paginate(20);
        return view('admin.blog.posts.index', compact('posts'));
    }

    public function create()
    {
        $categories = BlogCategory::all();
        $tags = BlogTag::all();
        return view('admin.blog.posts.create', compact('categories', 'tags'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'body' => 'required|string',
            'cover' => 'nullable|image|max:2048',
            'status' => 'required|in:draft,published',
            'category_id' => 'nullable|exists:blog_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:blog_tags,id',
            'published_at' => 'nullable|date',
        ]);
        $data['user_id'] = Auth::id();
        if ($request->hasFile('cover')) {
            $data['cover'] = $request->file('cover')->store('blog/covers', 'public');
        }
        $post = BlogPost::create($data);
        if ($request->filled('tags')) {
            $post->tags()->sync($request->tags);
        }
        return redirect()->route('admin.blog.posts.index')->with('success', 'پست با موفقیت ایجاد شد.');
    }

    public function edit(BlogPost $post)
    {
        $categories = BlogCategory::all();
        $tags = BlogTag::all();
        $post->load('tags');
        return view('admin.blog.posts.edit', compact('post', 'categories', 'tags'));
    }

    public function update(Request $request, BlogPost $post)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'nullable|string|max:500',
            'body' => 'required|string',
            'cover' => 'nullable|image|max:2048',
            'status' => 'required|in:draft,published',
            'category_id' => 'nullable|exists:blog_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:blog_tags,id',
            'published_at' => 'nullable|date',
        ]);
        if ($request->hasFile('cover')) {
            $data['cover'] = $request->file('cover')->store('blog/covers', 'public');
        }
        $post->update($data);
        $post->tags()->sync($request->tags ?? []);
        return redirect()->route('admin.blog.posts.index')->with('success', 'پست با موفقیت ویرایش شد.');
    }

    public function destroy(BlogPost $post)
    {
        $post->tags()->detach();
        $post->delete();
        return redirect()->route('admin.blog.posts.index')->with('success', 'پست حذف شد.');
    }
} 