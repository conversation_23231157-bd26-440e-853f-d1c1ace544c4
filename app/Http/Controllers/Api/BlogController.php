<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use App\Models\BlogTag;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    // لیست پست‌ها با فیلتر و جستجو
    public function posts(Request $request)
    {
        $query = BlogPost::with(['category', 'user', 'tags'])
            ->where('status', 'published');
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        if ($request->filled('tag_id')) {
            $query->whereHas('tags', function($q) use ($request) {
                $q->where('blog_tags.id', $request->tag_id);
            });
        }
        $posts = $query->orderByDesc('published_at')->paginate(12);
        return response()->json($posts);
    }

    // نمایش یک پست
    public function show($id)
    {
        $post = BlogPost::with(['category', 'user', 'tags'])
            ->where('status', 'published')
            ->findOrFail($id);
        return response()->json($post);
    }

    // لیست دسته‌بندی‌ها
    public function categories()
    {
        $categories = BlogCategory::orderBy('name')->get();
        return response()->json($categories);
    }

    // لیست تگ‌ها
    public function tags()
    {
        $tags = BlogTag::orderBy('name')->get();
        return response()->json($tags);
    }
} 