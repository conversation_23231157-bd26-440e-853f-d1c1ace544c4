<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Level;
use App\Models\UserLevel;
use App\Services\LevelUpgradeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserLevelController extends Controller
{
    protected $levelUpgradeService;

    public function __construct(LevelUpgradeService $levelUpgradeService)
    {
        $this->levelUpgradeService = $levelUpgradeService;
    }

    /**
     * نمایش وضعیت سطح کاربر
     */
    public function index()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        
        if (!$upgradeInfo['current_level']) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'current_level' => [
                    'id' => $upgradeInfo['current_level']->id,
                    'name' => $upgradeInfo['current_level']->name,
                    'title' => $upgradeInfo['current_level']->title,
                    'color' => $upgradeInfo['current_level']->color,
                    'priority' => $upgradeInfo['current_level']->priority,
                    'features' => $upgradeInfo['current_level']->features,
                    'restrictions' => $upgradeInfo['current_level']->restrictions,
                    'daily_buy_limit' => $upgradeInfo['current_level']->formatted_daily_buy_limit,
                    'daily_sell_limit' => $upgradeInfo['current_level']->formatted_daily_sell_limit,
                    'daily_withdrawal_limit' => $upgradeInfo['current_level']->formatted_daily_withdrawal_limit,
                ],
                'next_level' => $upgradeInfo['next_level'] ? [
                    'id' => $upgradeInfo['next_level']->id,
                    'name' => $upgradeInfo['next_level']->name,
                    'title' => $upgradeInfo['next_level']->title,
                    'color' => $upgradeInfo['next_level']->color,
                    'priority' => $upgradeInfo['next_level']->priority,
                    'features' => $upgradeInfo['next_level']->features,
                    'restrictions' => $upgradeInfo['next_level']->restrictions,
                    'daily_buy_limit' => $upgradeInfo['next_level']->formatted_daily_buy_limit,
                    'daily_sell_limit' => $upgradeInfo['next_level']->formatted_daily_sell_limit,
                    'daily_withdrawal_limit' => $upgradeInfo['next_level']->formatted_daily_withdrawal_limit,
                ] : null,
                'can_upgrade' => $upgradeInfo['can_upgrade'],
                'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                'upgrade_conditions' => $upgradeInfo['upgrade_conditions'],
                'document_status' => $upgradeInfo['document_status'],
                'time_since_upgrade' => $upgradeInfo['time_since_upgrade'],
                'total_purchases' => $user->total_purchases
            ]
        ]);
    }

    /**
     * نمایش جزئیات سطح فعلی
     */
    public function showCurrentLevel()
    {
        $user = Auth::user();
        $currentLevel = $user->currentLevel;
        $userLevel = $user->userLevel;
        
        if (!$currentLevel) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        
        $data = [
            'current_level' => [
                'id' => $currentLevel->id,
                'name' => $currentLevel->name,
                'title' => $currentLevel->title,
                'color' => $currentLevel->color,
                'priority' => $currentLevel->priority,
                'features' => $currentLevel->features,
                'restrictions' => $currentLevel->restrictions,
                'daily_buy_limit' => $currentLevel->formatted_daily_buy_limit,
                'daily_sell_limit' => $currentLevel->formatted_daily_sell_limit,
                'daily_withdrawal_limit' => $currentLevel->formatted_daily_withdrawal_limit,
            ]
        ];

        if ($userLevel) {
            $data['upgrade_history'] = [
                'upgraded_at' => $userLevel->upgraded_at->format('Y/m/d H:i'),
                'days_since_upgrade' => $userLevel->days_since_upgrade,
                'hours_since_upgrade' => $userLevel->hours_since_upgrade,
                'previous_level' => $userLevel->previousLevel ? $userLevel->previousLevel->name : null,
                'upgrade_reason' => $userLevel->upgrade_reason,
                'is_automatic' => $userLevel->is_automatic,
                'total_purchases_at_upgrade' => $userLevel->total_purchases_at_upgrade
            ];
        }
        
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * نمایش اطلاعات سطح بعدی و شرایط ارتقا
     */
    public function showNextLevel()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        
        if (!$upgradeInfo['current_level']) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        
        if (!$upgradeInfo['next_level']) {
            return response()->json([
                'success' => false,
                'message' => 'شما در بالاترین سطح ممکن هستید'
            ]);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'next_level' => [
                    'id' => $upgradeInfo['next_level']->id,
                    'name' => $upgradeInfo['next_level']->name,
                    'title' => $upgradeInfo['next_level']->title,
                    'color' => $upgradeInfo['next_level']->color,
                    'priority' => $upgradeInfo['next_level']->priority,
                    'features' => $upgradeInfo['next_level']->features,
                    'restrictions' => $upgradeInfo['next_level']->restrictions,
                    'daily_buy_limit' => $upgradeInfo['next_level']->formatted_daily_buy_limit,
                    'daily_sell_limit' => $upgradeInfo['next_level']->formatted_daily_sell_limit,
                    'daily_withdrawal_limit' => $upgradeInfo['next_level']->formatted_daily_withdrawal_limit,
                ],
                'can_upgrade' => $upgradeInfo['can_upgrade'],
                'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                'upgrade_conditions' => $upgradeInfo['upgrade_conditions'],
                'document_status' => $upgradeInfo['document_status']
            ]
        ]);
    }

    /**
     * درخواست ارتقای سطح
     */
    public function requestUpgrade(Request $request)
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        
        if (!$upgradeInfo['current_level']) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        
        if (!$upgradeInfo['next_level']) {
            return response()->json([
                'success' => false,
                'message' => 'سطح بعدی وجود ندارد'
            ]);
        }

        // اگر نیاز به تایید دستی دارد (برنزی به نقره‌ای)
        if ($upgradeInfo['requires_manual_approval']) {
            $documentStatus = $upgradeInfo['document_status'];
            
            if (!$documentStatus['has_documents']) {
                return response()->json([
                    'success' => false,
                    'message' => 'برای ارتقا به سطح نقره‌ای، ابتدا مدارک خود را ارسال کنید',
                    'requires_documents' => true
                ]);
            }
            
            if ($documentStatus['has_pending']) {
                return response()->json([
                    'success' => false,
                    'message' => 'مدارک شما در حال بررسی است. لطفاً منتظر بمانید',
                    'documents_pending' => true
                ]);
            }
            
            if ($documentStatus['has_rejected']) {
                return response()->json([
                    'success' => false,
                    'message' => 'برخی از مدارک شما رد شده است. لطفاً مدارک جدید ارسال کنید',
                    'documents_rejected' => true
                ]);
            }
            
            if (!$documentStatus['all_approved']) {
                return response()->json([
                    'success' => false,
                    'message' => 'تمام مدارک شما باید تایید شوند',
                    'documents_not_approved' => true
                ]);
            }
            
            // اگر همه مدارک تایید شده، ارتقا انجام شود
            $result = $this->levelUpgradeService->upgradeUserAfterDocumentApproval($user);
        } else {
            // ارتقای خودکار برای سطوح بالاتر
            $result = $this->levelUpgradeService->upgradeUserLevel($user, $upgradeInfo['next_level']);
        }

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'new_level' => [
                        'id' => $result['new_level']->id,
                        'name' => $result['new_level']->name,
                        'title' => $result['new_level']->title,
                        'color' => $result['new_level']->color,
                        'priority' => $result['new_level']->priority
                    ],
                    'previous_level' => [
                        'id' => $result['previous_level']->id,
                        'name' => $result['previous_level']->name,
                        'title' => $result['previous_level']->title
                    ]
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
                'reasons' => $result['reasons'] ?? []
            ]);
        }
    }

    /**
     * بررسی وضعیت ارتقا
     */
    public function checkUpgradeStatus()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        
        if (!$upgradeInfo['current_level']) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'can_upgrade' => $upgradeInfo['can_upgrade'],
                'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                'conditions' => $upgradeInfo['upgrade_conditions'],
                'document_status' => $upgradeInfo['document_status']
            ]
        ]);
    }

    /**
     * نمایش تاریخچه ارتقا
     */
    public function upgradeHistory()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        if (!$upgradeInfo['current_level'] || $user->level == 0) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        $upgradeHistory = UserLevel::where('user_id', $user->id)
            ->with(['level', 'previousLevel'])
            ->orderBy('upgraded_at', 'desc')
            ->get();
        
        $history = $upgradeHistory->map(function ($upgrade) {
            return [
                'id' => $upgrade->id,
                'level' => [
                    'id' => $upgrade->level->id,
                    'name' => $upgrade->level->name,
                    'title' => $upgrade->level->title,
                    'color' => $upgrade->level->color
                ],
                'previous_level' => $upgrade->previousLevel ? [
                    'id' => $upgrade->previousLevel->id,
                    'name' => $upgrade->previousLevel->name,
                    'title' => $upgrade->previousLevel->title
                ] : null,
                'upgraded_at' => $upgrade->upgraded_at->format('Y/m/d H:i'),
                'upgrade_reason' => $upgrade->upgrade_reason,
                'is_automatic' => $upgrade->is_automatic,
                'total_purchases_at_upgrade' => $upgrade->total_purchases_at_upgrade
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => [
                'history' => $history,
                'total_upgrades' => $history->count()
            ]
        ]);
    }

    /**
     * نمایش وضعیت مدارک برای ارتقا به نقره‌ای
     */
    public function documentStatus()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        if (!$upgradeInfo['current_level'] || $user->level == 0) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        $documentStatus = $this->levelUpgradeService->checkDocumentStatusForSilverUpgrade($user);
        
        $documents = $documentStatus['documents']->map(function ($document) {
            return [
                'id' => $document->id,
                'status' => $document->status,
                'description' => $document->description,
                'created_at' => $document->created_at->format('Y/m/d H:i'),
                'updated_at' => $document->updated_at->format('Y/m/d H:i')
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => [
                'has_documents' => $documentStatus['has_documents'],
                'all_approved' => $documentStatus['all_approved'],
                'has_pending' => $documentStatus['has_pending'],
                'has_rejected' => $documentStatus['has_rejected'],
                'documents' => $documents,
                'total_documents' => $documents->count(),
                'approved_count' => $documents->where('status', 'approved')->count(),
                'pending_count' => $documents->where('status', 'pending')->count(),
                'rejected_count' => $documents->where('status', 'rejected')->count()
            ]
        ]);
    }

    /**
     * نمایش اطلاعات کامل سطح کاربر
     */
    public function levelDetails()
    {
        $user = Auth::user();
        $upgradeInfo = $this->levelUpgradeService->getUserUpgradeInfo($user);
        if (!$upgradeInfo['current_level'] || $user->level == 0) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }
        $allLevels = Level::orderBy('priority')->get();
        
        $levels = $allLevels->map(function ($level) use ($user) {
            $isCurrentLevel = $user->level !== null && $user->level == $level->priority;
            $isNextLevel = $user->level !== null && $user->level + 1 == $level->priority;
            
            return [
                'id' => $level->id,
                'name' => $level->name,
                'title' => $level->title,
                'color' => $level->color,
                'priority' => $level->priority,
                'features' => $level->features,
                'restrictions' => $level->restrictions,
                'daily_buy_limit' => $level->formatted_daily_buy_limit,
                'daily_sell_limit' => $level->formatted_daily_sell_limit,
                'daily_withdrawal_limit' => $level->formatted_daily_withdrawal_limit,
                'total_purchase_requirement' => $level->formatted_total_purchase_requirement,
                'days_from_previous_level' => $level->days_from_previous_level,
                'active' => $level->active,
                'is_current_level' => $isCurrentLevel,
                'is_next_level' => $isNextLevel
            ];
        });
        
        return response()->json([
            'success' => true,
            'data' => [
                'user_info' => [
                    'id' => $user->id,
                    'current_level' => $user->level,
                    'total_purchases' => $user->total_purchases
                ],
                'upgrade_info' => [
                    'can_upgrade' => $upgradeInfo['can_upgrade'],
                    'requires_manual_approval' => $upgradeInfo['requires_manual_approval'],
                    'upgrade_conditions' => $upgradeInfo['upgrade_conditions'],
                    'document_status' => $upgradeInfo['document_status']
                ],
                'levels' => $levels,
                'total_levels' => $levels->count()
            ]
        ]);
    }

    /**
     * نمایش وضعیت محدودیت‌های تراکنش روزانه
     */
    public function dailyTransactionLimits(Request $request)
    {
        $user = Auth::user();
        $currentLevel = $user->currentLevel;
        if (!$currentLevel || $user->level == 0) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }

        // محاسبه تراکنش‌های امروز یا تاریخ مشخص شده
        $date = $request->get('date', date('Y-m-d'));
        
        $todayBuyTransactions = $user->transactions()
            ->where('type', 'buy')
            ->where('status', 'done')
            ->whereDate('created_at', $date);
            
        $todaySellTransactions = $user->transactions()
            ->where('type', 'sell')
            ->where('status', 'done')
            ->whereDate('created_at', $date);

        $totalBuyToday = intval($todayBuyTransactions->sum('price'));
        $totalSellToday = intval($todaySellTransactions->sum('price'));

        // همچنین محاسبه کل تراکنش‌های انجام شده
        $allBuyTransactions = $user->transactions()
            ->where('type', 'buy')
            ->where('status', 'done');
            
        $allSellTransactions = $user->transactions()
            ->where('type', 'sell')
            ->where('status', 'done');

        $totalBuyAll = intval($allBuyTransactions->sum('price'));
        $totalSellAll = intval($allSellTransactions->sum('price'));

        $data = [
            'current_level' => [
                'id' => $currentLevel->id,
                'name' => $currentLevel->name,
                'title' => $currentLevel->title,
                'color' => $currentLevel->color,
                'priority' => $currentLevel->priority
            ],
            'buy_limits' => [
                'daily_limit' => $currentLevel->daily_buy_limit > 0 ? intval($currentLevel->daily_buy_limit) : 0,
                'formatted_daily_limit' => $currentLevel->daily_buy_limit > 0 ? number_format($currentLevel->daily_buy_limit) . ' تومان' : 'نامحدود',
                'used_today' => $totalBuyToday,
                'formatted_used_today' => number_format($totalBuyToday) . ' تومان',
                'remaining_today' => $currentLevel->daily_buy_limit > 0 ? max(0, $currentLevel->daily_buy_limit - $totalBuyToday) : 0,
                'formatted_remaining_today' => $currentLevel->daily_buy_limit > 0 ? number_format(max(0, $currentLevel->daily_buy_limit - $totalBuyToday)) . ' تومان' : 'نامحدود',
                'can_buy' => $currentLevel->priority > 1,
                'limit_reached' => $currentLevel->daily_buy_limit > 0 && $totalBuyToday >= $currentLevel->daily_buy_limit
            ],
            'sell_limits' => [
                'daily_limit' => $currentLevel->daily_sell_limit > 0 ? intval($currentLevel->daily_sell_limit) : 0,
                'formatted_daily_limit' => $currentLevel->daily_sell_limit > 0 ? number_format($currentLevel->daily_sell_limit) . ' تومان' : 'نامحدود',
                'used_today' => $totalSellToday,
                'formatted_used_today' => number_format($totalSellToday) . ' تومان',
                'remaining_today' => $currentLevel->daily_sell_limit > 0 ? max(0, $currentLevel->daily_sell_limit - $totalSellToday) : 0,
                'formatted_remaining_today' => $currentLevel->daily_sell_limit > 0 ? number_format(max(0, $currentLevel->daily_sell_limit - $totalSellToday)) . ' تومان' : 'نامحدود',
                'can_sell' => $currentLevel->priority > 1,
                'limit_reached' => $currentLevel->daily_sell_limit > 0 && $totalSellToday >= $currentLevel->daily_sell_limit
            ],
            'withdrawal_limits' => [
                'daily_limit' => $currentLevel->daily_withdrawal_limit > 0 ? intval($currentLevel->daily_withdrawal_limit) : 0,
                'formatted_daily_limit' => $currentLevel->daily_withdrawal_limit > 0 ? number_format($currentLevel->daily_withdrawal_limit) . ' تومان' : 'نامحدود'
            ],
            'today_transactions' => [
                'total_buy' => $totalBuyToday,
                'formatted_total_buy' => number_format($totalBuyToday) . ' تومان',
                'total_sell' => $totalSellToday,
                'formatted_total_sell' => number_format($totalSellToday) . ' تومان',
                'net_flow' => $totalBuyToday - $totalSellToday,
                'formatted_net_flow' => number_format($totalBuyToday - $totalSellToday) . ' تومان'
            ],
            'all_transactions' => [
                'total_buy' => $totalBuyAll,
                'formatted_total_buy' => number_format($totalBuyAll) . ' تومان',
                'total_sell' => $totalSellAll,
                'formatted_total_sell' => number_format($totalSellAll) . ' تومان',
                'net_flow' => $totalBuyAll - $totalSellAll,
                'formatted_net_flow' => number_format($totalBuyAll - $totalSellAll) . ' تومان'
            ],
            'date' => $date,
            'next_reset' => date('Y-m-d', strtotime($date . ' +1 day')),
            'total_transactions_count' => [
                'buy' => $allBuyTransactions->count(),
                'sell' => $allSellTransactions->count()
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * نمایش وضعیت محدودیت‌های تراکنش برای ارز خاص
     */
    public function currencyTransactionLimits(Request $request)
    {
        $request->validate([
            'currency_id' => 'required|integer|exists:coins,id'
        ]);

        $user = Auth::user();
        $currentLevel = $user->currentLevel;
        $currencyId = $request->currency_id;
        if (!$currentLevel || $user->level == 0) {
            return response()->json([
                'success' => false,
                'message' => 'کد ملی شما در حال استعلام می باشد و پس از چند لحضه در صورت هم خوانی تایید خواهد شد'
            ]);
        }

        // محاسبه تراکنش‌های امروز برای ارز خاص
        $today = date('Y-m-d');
        
        $todayBuyTransactions = $user->transactions()
            ->where('type', 'buy')
            ->where('currency_id', $currencyId)
            ->where('status', 'done')
            ->whereDate('created_at', $today);
            
        $todaySellTransactions = $user->transactions()
            ->where('type', 'sell')
            ->where('currency_id', $currencyId)
            ->where('status', 'done')
            ->whereDate('created_at', $today);

        $totalBuyToday = intval($todayBuyTransactions->sum('price'));
        $totalSellToday = intval($todaySellTransactions->sum('price'));

        // دریافت اطلاعات ارز
        $currency = \App\Models\Coin::find($currencyId);

        $data = [
            'currency' => [
                'id' => $currency->id,
                'name' => $currency->name,
                'symbol' => $currency->symbol,
                'icon' => $currency->icon
            ],
            'current_level' => [
                'name' => $currentLevel->name,
                'priority' => $currentLevel->priority
            ],
            'buy_limits' => [
                'daily_limit' => $currentLevel->daily_buy_limit > 0 ? intval($currentLevel->daily_buy_limit) : 0,
                'formatted_daily_limit' => $currentLevel->daily_buy_limit > 0 ? number_format($currentLevel->daily_buy_limit) . ' تومان' : 'نامحدود',
                'used_today' => $totalBuyToday,
                'formatted_used_today' => number_format($totalBuyToday) . ' تومان',
                'remaining_today' => $currentLevel->daily_buy_limit > 0 ? max(0, $currentLevel->daily_buy_limit - $totalBuyToday) : 0,
                'formatted_remaining_today' => $currentLevel->daily_buy_limit > 0 ? number_format(max(0, $currentLevel->daily_buy_limit - $totalBuyToday)) . ' تومان' : 'نامحدود',
                'can_buy' => $currentLevel->priority > 1,
                'limit_reached' => $currentLevel->daily_buy_limit > 0 && $totalBuyToday >= $currentLevel->daily_buy_limit
            ],
            'sell_limits' => [
                'daily_limit' => $currentLevel->daily_sell_limit > 0 ? intval($currentLevel->daily_sell_limit) : 0,
                'formatted_daily_limit' => $currentLevel->daily_sell_limit > 0 ? number_format($currentLevel->daily_sell_limit) . ' تومان' : 'نامحدود',
                'used_today' => $totalSellToday,
                'formatted_used_today' => number_format($totalSellToday) . ' تومان',
                'remaining_today' => $currentLevel->daily_sell_limit > 0 ? max(0, $currentLevel->daily_sell_limit - $totalSellToday) : 0,
                'formatted_remaining_today' => $currentLevel->daily_sell_limit > 0 ? number_format(max(0, $currentLevel->daily_sell_limit - $totalSellToday)) . ' تومان' : 'نامحدود',
                'can_sell' => $currentLevel->priority > 1,
                'limit_reached' => $currentLevel->daily_sell_limit > 0 && $totalSellToday >= $currentLevel->daily_sell_limit
            ],
            'today_transactions' => [
                'total_buy' => $totalBuyToday,
                'formatted_total_buy' => number_format($totalBuyToday) . ' تومان',
                'total_sell' => $totalSellToday,
                'formatted_total_sell' => number_format($totalSellToday) . ' تومان',
                'net_flow' => $totalBuyToday - $totalSellToday,
                'formatted_net_flow' => number_format($totalBuyToday - $totalSellToday) . ' تومان'
            ],
            'date' => $today,
            'next_reset' => date('Y-m-d', strtotime('tomorrow'))
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
