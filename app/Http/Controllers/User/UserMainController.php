<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\UpdateProfileRequest;
use App\Jobs\DeletePreviousJob;
use App\Jobs\VerifyUserProfile;
use App\Services\User\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Redis;

class UserMainController extends Controller
{
    public function __construct(protected UserService $service) {}

    public function index(Request $request){
        return $this->service->show($request->user()->id);
    }

    public function show(Request $request): object|array
    {
        return $this->service->show($request->user()->id);
    }

    public function store(UpdateProfileRequest $request)
    {
        $user = $request->user();

        // if($user->level == 0){
        //     VerifyUserProfile::dispatch($user->id);
        // }

        return $this->service->update($user->id, $request->all());
    }

    public function update(Request $request)
    {
        // Validate the national_id if it's the only field being updated
        if ($request->has('national_id') && count($request->all()) === 1) {
            $request->validate([
                'national_id' => ['required', 'digits:10', 'numeric'],
            ]);
        }

        return $this->service->update($request->user()->id, $request->all());
    }
}
