<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Referral;
use App\Models\ReferralReward;

class UserReferralController extends Controller
{
    public function link(Request $request)
    {
        $user = $request->user();
        return response()->json([
            'referral_link' => url('/register?ref=' . $user->referral_code),
            'referral_code' => $user->referral_code,
        ]);
    }

    public function list(Request $request)
    {
        $user = $request->user();
        $referrals = Referral::with('invitedUser')
            ->where('inviter_user_id', $user->id)
            ->get();

        $data = $referrals->map(function ($ref) {
            return [
                'user' => $ref->invitedUser ? $ref->invitedUser->only(['id', 'firstname', 'lastname', 'email', 'phone']) : null,
                'joined_at' => $ref->created_at,
                'transactions_count' => $ref->invitedUser ? $ref->invitedUser->transactions()->count() : 0,
                'total_reward' => $ref->rewards()->sum('amount_toman'),
            ];
        });

        return response()->json([
            'count' => $referrals->count(),
            'referrals' => $data,
        ]);
    }

    public function rewards(Request $request)
    {
        $user = $request->user();
        $referrals = Referral::where('inviter_user_id', $user->id)->pluck('id');
        $rewards = ReferralReward::whereIn('referral_id', $referrals)->get();

        return response()->json([
            'total' => $rewards->sum('amount_toman'),
            'rewards' => $rewards,
        ]);
    }
} 