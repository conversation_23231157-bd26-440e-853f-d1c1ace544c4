<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\User\ListCurrency;
use App\Models\Coin;
use App\Models\Currency;
use App\Models\UsdPrice;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    public function index(Request $request)
    {
        $currencies = Coin::with(['coin_network' => function($query) {
            $query->select('id', 'network_id', 'currency_id')
                ->where('status', 1)
                ->with(['network' => function($q) {
                    $q->select('id', 'name')
                      ->where('status', 1);
                }]);
        }])
        ->leftJoin('wallets', function($join) {
            $join->on('coins.id', '=', 'wallets.coin_id')
                 ->where('wallets.user_id', '=', auth()->id());
        })
        ->select(
            'coins.id',
            'coins.name',
            'coins.coin_type',
            'coins.coin_icon',
            'coins.coin_price',
            'coins.minimum_withdrawal',
            'coins.maximum_withdrawal',
            'coins.withdrawal_fees',
            'coins.withdrawal_fees_type',
            'wallets.balance',
            'wallets.id as wallet_id'
        )
        ->where('coins.status', 1)
        ->where(function($query) {
            $query->where('is_withdrawal', 1)
                  ->orWhere('is_deposit', 1);
        });

        $result = $currencies->get();
        return $result->map(function($currency) {
            $usdPrice = UsdPrice::getActive();
            
            // Ensure balance is numeric and handle null values
            $balance = $currency->balance ?? 0;
            $balance = is_numeric($balance) ? $balance : 0;
            
            // Calculate USD value before formatting
            $balance_usd = get_coin_usd_value($balance, $currency->coin_type);
            
            // Format the balance for display
            $currency->balance = number_format($balance, 8);
            $currency->balance_usd = number_format($balance_usd, 2);
            $currency->toman_buy_price = (int)UsdPrice::usdToToman($currency->coin_price, 'buy');
            $currency->toman_sell_price = (int)UsdPrice::usdToToman($currency->coin_price, 'sell');
            $currency->balance_toman = (int)($balance_usd * $usdPrice->sell_price);
            return $currency;
        });
    }

    public function show(Currency $currency)
    {
        return $currency;
    }
}
