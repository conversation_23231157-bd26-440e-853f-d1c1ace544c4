<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use App\Models\JibitTransaction;
class JibitWebhookController extends Controller
{
    private $appSecret = 'AeXLhDjTLyFt'; 

    public function handle(Request $request)
    {
        $rawBody = $request->getContent(); 
        Log::info('Jibit Webhook received', ['body' => $rawBody]);

        $signatureHeader = $request->header('X-Signature');

        if ($signatureHeader) {
            $calculatedSignature = hash('sha256', $this->appSecret . '#' . $rawBody);

            Log::debug('Signature check', [
                'expected' => $calculatedSignature,
                'received' => $signatureHeader
            ]);

            if (!hash_equals($calculatedSignature, $signatureHeader)) {
                Log::warning('Webhook signature mismatch', [
                    'expected' => $calculatedSignature,
                    'received' => $signatureHeader
                ]);
                return Response::json(['message' => 'Invalid signature'], 401);
            }
        }

        // داده‌ها را تجزیه کنیم
        $data = json_decode($rawBody, true);

        if (!isset($data['events'])) {
            Log::error('Invalid payload: events key missing', ['payload' => $data]);
            return response()->json(['message' => 'Invalid payload'], 400);
        }

        foreach ($data['events'] as $event) {
            $varizData = $event['varizData'] ?? null;
            if ($varizData) {
                $iban = $varizData['accountIban'] ?? null;
                $refNumber = $varizData['referenceNumber'] ?? null;

                JibitTransaction::updateOrCreate(
                    ['reference_number' => $refNumber],
                    ['iban' => $iban]
                );

                Log::info('Transaction processed', [
                    'reference_number' => $refNumber,
                    'iban' => $iban
                ]);
            }
        }

        return response()->json(['message' => 'Webhook processed'], 200);
    }
}