<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class EnsureHasRole
{
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user();
        if (!$user || $user->roles()->count() == 0) {
            auth()->logout();
            return redirect()->route('admin.login')->withErrors(['role' => 'شما دسترسی لازم را ندارید.']);
        }
        return $next($request);
    }
} 