<?php

namespace App\Http\Services;

use App\User;
use Carbon\Carbon;
use App\Models\Coin;
use App\Models\Wallet;
use App\Jobs\MailSend;
use App\Jobs\Withdrawal;
use App\Models\CoinNetwork;
use App\Models\TempWithdraw;
use App\Models\WalletCoUser;
use Illuminate\Support\Str;
use App\Models\WalletNetwork;
use App\Models\WithdrawHistory;
use PragmaRX\Google2FA\Google2FA;
use App\Models\DepositeTransaction;
use Illuminate\Support\Facades\DB;
use App\Models\WalletAddressHistory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Http\Services\MyCommonService;
use function PHPUnit\Framework\isNull;

use App\Models\CoWalletWithdrawApproval;
use App\Services\Evm\EvmWalletService;
use App\Http\Repositories\AffiliateRepository;
use App\Jobs\DistributeWithdrawalReferralBonus;

class TransService
{
    protected $logger;

    public function __construct()
    {
        $this->logger = new Logger();
    }

    private function generate_email_verification_key()
    {
        do {
            $key = Str::random(60);
        } While (User::where('email_verified', $key)->count() > 0);

        return $key;
    }

    // make withdrawal data
    private function make_withdrawal_data($data,$address_type,$fees,$trans_id,$receiverWallet = null)
    {
       return [
            'wallet_id' => $data['wallet']->id,
            'address' => $data['address'],
            'amount' => $data['amount'],
            'address_type' => $address_type,
            'fees' => $fees,
            'coin_type' => $data['wallet']->coin_type,
            'transaction_hash' => $trans_id,
            'confirmations' => 0,
            'status' => STATUS_PENDING,
            'message' => $data['note'],
            'receiver_wallet_id' => is_null($receiverWallet) ? 0 : $receiverWallet->id,
            'user_id' => $data['user']->id,
           'network_type' => $data['network_type'] ?? '',
           'memo' => $data['memo'] ? $data['memo'] : '',
           'network_id' => $data['network_id'] ?? ''
        ];
    }
    // withdrawal process from job
    public function send($data)
    {
        $responseWithdrawal = responseData(false,__('Invalid request'));
        try {
            $wallet = Wallet::join('coins', 'coins.id', '=', 'wallets.coin_id')
                ->where(['wallets.id'=>$data['wallet_id'], 'wallets.user_id'=> $data['user']->id])
                ->select('wallets.*', 'coins.name as coin_name', 'coins.status as coin_status', 'coins.status as coin_status', 'coins.is_withdrawal', 'coins.minimum_withdrawal',
                    'coins.maximum_withdrawal', 'coins.withdrawal_fees', 'coins.max_send_limit', 'coins.withdrawal_fees_type', 'coins.network')
                ->first();
            $user = $wallet->user;

            $coin_name = strtolower($wallet->coin_name);
//            $sender_wallet_address = WalletAddressHistory::where('wallet_id',$wallet->id)->first()->address;
            if ($wallet) {
                if(isset($request->network_id)){
                    if(!$coinNetwork = CoinNetwork::where(["network_id" => $data->network_id, "currency_id" => $wallet->coin_id])->first()){
                        return responseData(false,__('Coin network not found'));
                    }
                    $wallet->withdrawal_fees      = $coinNetwork->withdrawal_fees ?? 0;
                    $wallet->withdrawal_fees_type = $coinNetwork->withdrawal_fees_type ?? 2;
                }
                if ($wallet->coin_type == COIN_USDT && $wallet->network == COIN_PAYMENT) {
                    $checkNetwork = WalletNetwork::where(['wallet_id' => $wallet->id, 'network_type' => $data['network_type']])->first();
                    if(empty($checkNetwork)) {
                        storeException('wallet ',$wallet);
                        storeException('withdrawal usdt network not found',$data['network_type']);
                        return responseData(false,__('Selected Coin API is not found'));
                    }
                }
                $checkValidate = $this->checkWithdrawalValidation( $data['address'], $data['amount'], $data['user'], $wallet);
                if ($checkValidate['success'] ==  false) {
                    $response = [
                        'success' => false,
                        'message' => $checkValidate['message'],
                        'data' => ''
                    ];
                    storeBotException('send job', json_encode($response));
                    return $response;
                }
                $checkCoinNetwork = CoinNetwork::where(['currency_id' => $wallet->coin_id])->first();
                if (!$checkCoinNetwork) {
                    $response = responseData(false,__('Coin network not found'));
                }
                $data['network_id'] = $checkCoinNetwork->network_id;
            } else {
                $response = [
                    'success' => false,
                    'message' => __('Wallet not found'),
                    'data' => ''
                ];
                storeBotException('send job', json_encode($response));
                return $response;
            }
            $trans_id = Str::random(32);// we make this same for deposit and withdrawl
            DB::beginTransaction();
            try {
                $walletAddress = $this->isInternalAddress($data['address'], $data['coin_type']);
                if ( empty($walletAddress) ) {
                    $receiverWallet = null;
                    $receiverUser = null;
                    $address_type = ADDRESS_TYPE_EXTERNAL;
                    $fees = $checkValidate['data']['fees'];
                } else {
                    $fees = 0;
                    $receiverWallet = $walletAddress->wallet;
                    $receiverUser = $walletAddress->wallet->user;
                    $address_type = ADDRESS_TYPE_INTERNAL;
                    if ( $user->id == $receiverUser->id ) {
                        storeBotException('send ','You can not send to your own wallet!');
                        return ['success' => false, 'message' => __('You can not send to your own wallet!')];
                    }
                    if ($wallet->coin_type != $walletAddress->wallet->coin_type) {
                        storeBotException('send','You can not make withdrawal, because wallet coin type is mismatched. Your wallet coin type and withdrawal address coin type should be same.');
                        return ['success' => false, 'message' => __('You can not make withdrawal, because wallet coin type is mismatched. Your wallet coin type and withdrawal address coin type should be same.')];
                    }
                }
                if ( ($data['amount'] + $fees) > $wallet->balance) {

                    return ['success' => false, 'message' => 'Insufficient Balance!'];
                }
                $sendAmount = $data['amount'] + $fees;
                $wallet->decrement('balance', $sendAmount);
                $data['wallet'] = $wallet;

                $transaction = WithdrawHistory::create($this->make_withdrawal_data($data,$address_type,$fees,$trans_id,$receiverWallet));
                storeBotException('send job withdrawal data', json_encode($transaction));

                if ($address_type == ADDRESS_TYPE_INTERNAL) {

                    if (checkCryptoAdminApproval($data['amount'],$wallet->coin_id)) {
                    } else{
                        $transaction->status = STATUS_SUCCESS;
                        $transaction->save();
                    }

                    if ( !empty($receiverWallet) ) {
                        $receive_tr =  DepositeTransaction::create($this->makeDepositData($data,$address_type,$fees,$trans_id,$receiverWallet->id));

                        if (checkCryptoAdminApproval($data['amount'],$wallet->coin_id)) {
                            storeBotException('internal withdrawal process ', 'goes to admin approval');
                            $responseWithdrawal = responseData(true,__('Internal withdrawal process goes to admin approval'));

                        } else {
                            $receive_tr->status = STATUS_SUCCESS;
                            $receive_tr->save();
                            $receiverWallet->increment('balance', $data['amount']);

                            $responseWithdrawal = responseData(true,__('Internal withdrawal process success'));
                        }
                    }
                } else {
                    storeBotException('withdrawal process','external withdrawal');
                    if (checkCryptoAdminApproval($data['amount'],$wallet->coin_id) || defined("IS_PUBLIC_API")) {
                        storeBotException('external withdrawal process ', 'goes to admin approval');
                            $responseWithdrawal = responseData(true,__('External withdrawal process goes to admin approval'));
                    } else {

                        $externalProcess = $this->acceptPendingExternalWithdrawal($transaction,"");
                        if($externalProcess['success'] == false) {
                            storeException('external withdrawal process failed',json_encode($externalProcess));
                            storeBotException(' external withdrawal','so its goes to admin approval automatically');
                            $transaction->update(['automatic_withdrawal' => 'failed']);
                        } else {
                            storeBotException('external withdrawal process ', 'end. withdrawal successfully');
                        }
                        $responseWithdrawal = $externalProcess;
                    }
                }
            } catch (\Exception $e) {
                DB::rollBack();
                storeException('coin send exception ', $e->getMessage());
                return [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
            }
        } catch (\Exception $e) {
            storeException('coin send exception ',$e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
        DB::commit();
        return $responseWithdrawal;
    }


    //make deposit data
    public function makeDepositData($data,$address_type,$fees,$trans_id,$receiverWalletId)
    {
        return [
            'address' => $data['address'],
            'address_type' => $address_type,
            'amount' => $data['amount'],
            'fees' => $fees,
            'coin_type' => $data['wallet']->coin_type,
            'transaction_id' => $trans_id,
            'confirmations' => 0,
            'status' => STATUS_PENDING,
            'sender_wallet_id' => $data['wallet']->id,
            'receiver_wallet_id' => $receiverWalletId,
            'network_type' => $data['network_type'] ?? '',
            'network_id' => $data['network_id'] ?? ''
        ];
    }
    // check internal address
    private function isInternalAddress($address, $coin)
    {
        $checkAddress = WalletAddressHistory::where(['address' => $address, "coin_type" => $coin])->with('wallet')->first();
        if($checkAddress) {
            return $checkAddress;
        } else {
            return WalletNetwork::where('address',$address)->with('wallet')->first();
        }
    }

    // cancel transaction
    private function _cancelTransaction($user, $wallet, $address, $amount, $pendingTransaction)
    {
        if ( !empty($pendingTransaction) ) {
            $pendingTransaction->status = STATUS_REJECTED;
            $pendingTransaction->update();
        }
        //  $mailService = app(MailService::class);
        $userName = $user->first_name . ' ' . $user->last_name;
        $userEmail = $user->email;
        $companyName = isset($default['company']) && !empty($default['company']) ? $default['company'] : __('Coin Wallet');
        $subject = __(':emailSubject | :companyName', ['emailSubject' => __('Send coin failure'), 'companyName' => $companyName]);
        $data['user'] = $user;
        $data['amount'] = $amount;
        $data['address'] = $address;
        $data['wallet'] = $wallet;
        //  $mailService->send('email.send_coin_failure', $data, $userEmail, $userName, $subject);
    }





    private function calculate_fees($amount)
    {
        return $amount;
    }


    private function sendTransactionMail($sender_user, $mailTemplet, $receiver_user, $amount, $emailSubject)
    {
        $mailService = app(MailService::class);
        $userName = $sender_user->first_name . ' ' . $sender_user->last_name;
        $userEmail = $sender_user->email;
        $companyName = isset($default['company']) && !empty($default['company']) ? $default['company'] : __('Coin Wallet');
        $subject = __(':emailSubject | :companyName', ['emailSubject' => $emailSubject, 'companyName' => $companyName]);
        $data['data'] = $sender_user;
        $data['anotherUser'] = $receiver_user;
        $data['amount'] = $amount;
        $mailService->send($mailTemplet, $data, $userEmail, $userName, $subject);
    }

    private function sendExternalTransactionMail($sender_user, $mailTemplet, $address, $amount, $emailSubject)
    {
        $mailService = app(MailService::class);
        $userName = $sender_user->first_name . ' ' . $sender_user->last_name;
        $userEmail = $sender_user->email;
        $companyName = isset($default['company']) && !empty($default['company']) ? $default['company'] : __('Coin Wallet');
        $subject = __(':emailSubject | :companyName', ['emailSubject' => $emailSubject, 'companyName' => $companyName]);
        $data['data'] = $sender_user;
        $data['address'] = $address;
        $data['amount'] = $amount;
        $mailService->send($mailTemplet, $data, $userEmail, $userName, $subject);
    }

    private function sendVerificationSms($phone, $randno)
    {
        $smsText = 'Your ' . allsetting()['app_title'] . ' verification code is here ' . $randno;
        app(SmsService::class)->send($phone, $smsText);
    }



    // user deposit history
    public function depositTransactionHistories($user_id = null, $status = null, $wallet_id = null, $address_type = null, $transaction_id = null, $order_data=null)
    {
        $histories = DepositeTransaction::join('wallets', 'wallets.id', 'deposite_transactions.receiver_wallet_id')
            ->select('wallets.*','deposite_transactions.*');
        if ( !empty($status) )
            $histories = $histories->where('deposite_transactions.status', $status);
        if ( !empty($wallet_id) )
            $histories = $histories->where('wallets.id', $wallet_id);
        if ( !empty($address_type) )
            $histories = $histories->where('deposite_transactions.address_type', $address_type);
        if ( !empty($transaction_id) )
            $histories = $histories->where('deposite_transactions.transaction_id', $transaction_id);
        if ( !empty($user_id) )
            $histories = $histories->where('wallets.user_id', $user_id);
        if(!empty($order_data['column_name']) && !empty($order_data['order_by'])){
            $withdraw_columns = ['created_at','address','amount','fees','coin_type'];
            if(in_array($order_data['column_name'],$withdraw_columns)){
                $histories->orderBy("deposite_transactions.$order_data[column_name]", $order_data['order_by']);
            }else{
                $histories->orderBy("wallets.$order_data[column_name]", $order_data['order_by']);
            }
        }
        return $histories;
    }

    // user withdrawal history
    public function withdrawTransactionHistories($user_id = null, $status = null, $wallet_id = null, $address_type = null, $transaction_id = null, $order_data=null)
    {
        $histories = WithdrawHistory::join('wallets', 'wallets.id', 'withdraw_histories.wallet_id')
            ->select('wallets.*','withdraw_histories.*');
        if ( !empty($status) )
            $histories = $histories->where('withdraw_histories.status', $status);
        if ( !empty($wallet_id) )
            $histories = $histories->where('wallets.id', $wallet_id);
        if ( !empty($address_type) )
            $histories = $histories->where('withdraw_histories.address_type', $address_type);
        if ( !empty($transaction_id) )
            $histories = $histories->where('withdraw_histories.transaction_hash', $transaction_id);
        if ( !empty($user_id) )
            $histories = $histories->where('wallets.user_id', $user_id);
        if(!empty($order_data['column_name']) && !empty($order_data['order_by'])){
            $withdraw_columns = ['created_at','address','amount','fees','coin_type'];
            if(in_array($order_data['column_name'],$withdraw_columns)){
                $histories->orderBy("withdraw_histories.$order_data[column_name]", $order_data['order_by']);
            }else{
                $histories->orderBy("wallets.$order_data[column_name]", $order_data['order_by']);
            }
        }
        return $histories;
    }

    public function isAllApprovalDoneForCoWalletWithdraw($tempWithdraw) {
        if(empty($tempWithdraw)) {
            Log::warning('Empty temp withdrawal.');
            return ['success' => false, 'message' => __('Invalid withdrawal.')];
        }
        $response = $this->approvalCounts($tempWithdraw);
        if($response['alreadyApprovedUserCount'] >= $response['requiredUserApprovalCount']) {
            $tempWithdraw->status = STATUS_ACCEPTED;
            try {
                if(!$tempWithdraw->save()) throw new \Exception(__('Temp withdraw status success save failed'));
                return ['success' => true, 'message' => ''];
            } catch (\Exception $e) {
                Log::warning($e->getMessage());
                return ['success' => false, 'message' => $e->getMessage()];
            }
        }
        else return ['success' => false, 'message' => __('Not enough approval done yet.')];
    }

    public function approvalCounts($tempWithdraw) {
        $userPercentageForApproval = settings(CO_WALLET_WITHDRAWAL_USER_APPROVAL_PERCENTAGE_SLUG);
        $userPercentageForApproval = !empty($userPercentageForApproval) ? $userPercentageForApproval : 60;
        $coUserCount = WalletCoUser::where(['wallet_id'=> $tempWithdraw->wallet_id])->count();
        $requiredUserApprovalCount = ceil($coUserCount * ($userPercentageForApproval / 100.0));
        $alreadyApprovedUserCount = CoWalletWithdrawApproval::where(['temp_withdraw_id'=> $tempWithdraw->id])->count();
        return ['requiredUserApprovalCount' => $requiredUserApprovalCount, 'alreadyApprovedUserCount' => $alreadyApprovedUserCount];
    }

    // check withdrawal validation
    public function checkWithdrawalValidation($address,$amount, $user, $wallet)
    {
      
        $fees = check_withdrawal_fees($amount, $wallet->withdrawal_fees,$wallet->withdrawal_fees_type);
        $data = [
            'data' => [
                'fees' => $fees,
                'amount' => $amount,
                'fees_percentage' => $wallet->withdrawal_fees,
                'fees_type' => $wallet->withdrawal_fees_type,
            ],
            'success' => true,
            'message' => __('Success')
        ];
        $walletAddress = $this->isInternalAddress($address, $wallet->coin_type);
        if ($walletAddress) {
            if ($walletAddress->wallet->user_id == $wallet->user_id) {
                $data = [
                    'data' => [],
                    'success' => false,
                    'message' => __('You can not send to your own wallet!')
                ];
                return $data;
            }
            if ($walletAddress->wallet->coin_type != $wallet->coin_type) {
                $data = [
                    'data' => [],
                    'success' => false,
                    'message' => __('Both wallet coin type should be same')
                ];
                return $data;
            }
        }
        //  dd($wallet);
        $checkStatus = check_coin_status($wallet,$user->id,$amount,$fees);
        if ($checkStatus['success'] == false) {
            return $checkStatus;
        }
        return $data;
    }

    // admin pending withdrawal accept process

    public function acceptPendingExternalWithdrawal($transaction,$adminId=null)
    {
        if (!empty($adminId)) {
            storeException('acceptPendingExternalWithdrawal', 'accept process started from admin end');
        } else {
            storeException('acceptPendingExternalWithdrawal', 'withdrawal process started from user end');
        }
        try {
            $coin = $transaction->coin;
            $currency =  !empty($transaction->network_type) ? $transaction->network_type : $transaction->coin_type;

            if ($transaction?->network?->base_type == COIN_PAYMENT) {

                $coinPayment = new CoinPaymentsAPI();
                $auto_confirm = FALSE;
                if (isset(allsetting()['coin_payment_withdrawal_email']) && allsetting()['coin_payment_withdrawal_email'] == STATUS_ACTIVE) {
                    $auto_confirm = TRUE;
                }
                $networkFees = network_fees_coinPayment($currency);
                $amountWithNetworkFees = bcadd($transaction->amount,$networkFees,8);
                storeException('CreateWithdrawal amount -> ',$transaction->amount);
                storeException('CreateWithdrawal network fees -> ',$networkFees);
                storeException('CreateWithdrawal amount with network fees -> ',$amountWithNetworkFees);
                $response = $coinPayment->CreateWithdrawal($amountWithNetworkFees,$currency,$transaction->address,$auto_confirm,'',$transaction->memo);

                if (is_array($response) && isset($response['error']) && ($response['error'] == 'ok') ) {
                    $transaction->transaction_hash = $response['result']['id'];
                    $transaction->status = STATUS_SUCCESS;
                    $transaction->updated_by = $adminId;
                    if (empty($adminId)) {
                        $transaction->automatic_withdrawal = 'success';
                    }
                    $transaction->update();
                    dispatch(new DistributeWithdrawalReferralBonus($transaction))->onQueue('referral');

                    return ['success' => true, 'message' => __('Pending withdrawal accepted Successfully.')];
                } else {
                    return ['success' => false, 'message' => $response['error']];
                }
            } elseif ($transaction?->network?->base_type == BITCOIN_API) {

                $result = $this->external_transfer_using_coin_api($currency,$transaction->address, $transaction->amount, Auth::id(), true, $transaction->user_id);

                if ($result['success'] == true) {
                    $transaction->transaction_hash = $result['transaction_id'];
                    $transaction->status = STATUS_SUCCESS;
                    $transaction->updated_by = $adminId;
                    if (empty($adminId)) {
                        $transaction->automatic_withdrawal = 'success';
                    }
                    $transaction->update();
                    dispatch(new DistributeWithdrawalReferralBonus($transaction))->onQueue('referral');

                    return ['success' => true, 'message' => __('Pending withdrawal accepted Successfully.')];
                } else {
                    return ['success' => false, 'message' => $result['message']];
                }
            } elseif ($transaction?->network?->base_type == BITGO_API) {
                $result = $this->sendCoinWithBitgo($transaction);
                if ($result['success'] == true) {
                    $transaction->transaction_hash = $result['data'];
                    $transaction->status = STATUS_SUCCESS;
                    $transaction->updated_by = $adminId;
                    if (empty($adminId)) {
                        $transaction->automatic_withdrawal = 'success';
                    }
                    $transaction->update();
                    dispatch(new DistributeWithdrawalReferralBonus($transaction))->onQueue('referral');

                    return ['success' => true, 'message' => __('Pending withdrawal accepted Successfully.')];
                } else {
                    return ['success' => false, 'message' => $result['message']];
                }
            } elseif ($transaction?->network?->base_type == EVM_BASE_COIN || $transaction?->network?->base_type == TRC20_TOKEN || $transaction?->network?->base_type == SOLONA_BASE_COIN || $transaction?->network?->base_type == TON_BASE_COIN) {
                $result = $this->sendTokenWithEvm($transaction);

                if ($result['success'] == true) {
                    if($withdrawHistory = WithdrawHistory::find($transaction->id)){
                        if($user  = \App\Models\User::find($withdrawHistory->user_id ?? 0)){
                            $title = __("Withdraw request approved by system");
                            $body  = __("Your withdrawal request is approved by System. \nWithdrawal transaction hash is $withdrawHistory->transaction_hash.");
                          //  $this->sendEmailAndNotification($title, $body, $user);
                        }
                    }
                    // $transaction->transaction_hash = $result['data']['transaction_id'];
                    // $transaction->used_gas = $result['data']['used_gas'];
                    // $transaction->status = STATUS_SUCCESS;
                    // $transaction->updated_by = $adminId;
                    // if (empty($adminId)) {
                    //     $transaction->automatic_withdrawal = 'success';
                    // }
                    // $transaction->update();
                    // dispatch(new DistributeWithdrawalReferralBonus($transaction))->onQueue('referral');
                    if (empty($adminId)) {
                        return ['success' => true, 'message' => __('User withdrawal processed successfully.')];
                    } else {
                        return ['success' => true, 'message' => __('Pending withdrawal accepted Successfully.')];
                    }
                } else {
                    return ['success' => false, 'message' => $result['message']];
                }
            } else {
                return ['success' => false, 'message' => __('No Api found')];
            }
        } catch (\Exception $e) {
            storeException("acceptPendingExternalWithdrawal",$e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


    // external transfer by using bit coin api
    public function external_transfer_using_coin_api($coinType,$address, $amount, $authId, $isAdmin, $user_id)
    {
        try {
            $coin = Coin::join('coin_settings','coin_settings.coin_id', '=', 'coins.id')
                ->where(['coins.coin_type' => $coinType])
                ->select('coins.*', 'coin_settings.*')
                ->first();
            if ($coin) {
                $api =  new BitCoinApiService($coin->coin_api_user,decryptId($coin->coin_api_pass),$coin->coin_api_host,$coin->coin_api_port);
                $response = $api->verifyAddress($address);
                storeException('address validation check',$response);
                if (!$response) {
                    return ['success' => false, 'message' => __('Not a valid address!')];
                }

                $adminId = null;
                $userId = $user_id;
                if ($isAdmin) {
                    $adminId = $authId;
                } else {
                    $userId = $authId;
                }

                $transaction_id = $api->sendToAddress($address, $amount, $userId, $adminId);
                storeBotException("transaction_id ",$transaction_id);
                if ($transaction_id) {
                    return [
                        'success' => true,
                        'message' => __('Transfer successfully!'),
                        'transaction_id' => $transaction_id
                    ];
                }
                return [
                    'success' => false,
                    'message' => __('Failed to send coin!'),
                    'nodeMessage' => $api
                ];
            } else {
                return [
                    'success' => false,
                    'message' => __('Failed to send coin,  coin not found'),
                    'nodeMessage' => ''
                ];
            }
        } catch (\Exception $e) {
            storeException('external_transfer_using_coin_api ', $e->getMessage());
            return [
                'success' => false,
                'message' => __('Failed to send coin!'),
            ];
        }

    }

    // withdrawal process
    public function withdrawalProcess($request)
    {
        try {
          
            $user = $request->user();
            $wallet = Wallet::join('coins', 'coins.id', '=', 'wallets.coin_id')
                ->where(['wallets.id'=>$request->wallet_id, 'wallets.user_id'=> $user->id])
                ->select('wallets.*', 'coins.status as coin_status', 'coins.is_withdrawal', 'coins.minimum_withdrawal',
                    'coins.maximum_withdrawal', 'coins.withdrawal_fees', 'coins.max_send_limit','coins.withdrawal_fees_type','coins.network')
                ->first();
                // return $wallet;
            if ($wallet) {
                $checkCoinNetwork = null;
                if(isset($request->network_id)){
                    if(!$checkCoinNetwork = CoinNetwork::where(["network_id" => $request->network_id, "currency_id" => $wallet->coin_id])->first()){
                        return responseData(false,__('Coin network not found'));
                    }
                    $wallet->withdrawal_fees      = $checkCoinNetwork->withdrawal_fees ?? 0;
                    $wallet->withdrawal_fees_type = $checkCoinNetwork->withdrawal_fees_type ?? 2;
                }else{
                    $checkCoinNetwork = CoinNetwork::where(['currency_id' => $wallet->coin_id])->first();
                }

                if ($wallet->coin_type == COIN_USDT && $request->base_type == COIN_PAYMENT) {
                    $checkNetwork = WalletNetwork::where(['wallet_id' => $wallet->id, 'network_type' => $request->network_type])->first();
                    if(empty($checkNetwork)) {
                        return responseData(false,__('Selected network not found'),$wallet->coin_type);
                    }
                }
                $checkValidate = $this->checkWithdrawalValidation($request->address, $request->amount, $user, $wallet);
                if ($checkValidate['success'] ==  false) {
                    $response = [
                        'success' => false,
                        'message' => $checkValidate['message'],
                        'data' => ''
                    ];
                } else {
                    $data = [
                        'wallet_id' => $wallet->id,
                        'coin_type' => $wallet->coin_type,
                        'amount' => $request->amount,
                        'address' => $request->address,
                        'note' => $request->note ?? '',
                        'user' => $user,
                        'network_id' => $request->network_id,
                        'network_type' => '',
                        'base_type' => $request->base_type,
                        'memo' => $request->memo ? $request->memo : ''
                    ];
                    if (!$checkCoinNetwork) {
                        $response = responseData(false,__('Coin network not found'));
                        return $response;
                    }
                    $data['network_id'] = $checkCoinNetwork->network_id;

                    if ($wallet->coin_type == COIN_USDT && $wallet->network == COIN_PAYMENT) {
                        $data['network_type'] = $request->network_type;
                    }
                    if(isset($request->code) && !defined("IS_PUBLIC_API")){
                        $request->merge(['code_type' => GOOGLE_AUTH]);
                       $response = checkTwoFactor("two_factor_withdraw",$request);
                       if(!$response["success"]){
                           return $response;
                       }
                    }
                    
                    dispatch_sync(new Withdrawal($data));
                    if (checkCryptoAdminApproval($request->amount,$wallet->coin_id)) {
                        $message = __('Withdrawal process started successfully. Please wait for admin approval');
                    } else {
                        $message = __('روند برداشت با موفقیت آغاز شد. به زودی نتیجه را به اطلاع شما خواهیم رساند');
                    }
                    $response = [
                        'success' => true,
                        'message' => $message,
                        'data' => ''
                    ];
                }
            } else {
                $response = [
                    'success' => false,
                    'message' => __('Wallet not found'),
                    'data' => ''
                ];
            }
        } catch (\Exception $e) {
            storeException('withdrawalProcess ', $e->getMessage());
            $response = [
                'success' => false,
                'message' => __('Something went wrong'),
                'data' => ''
            ];
        }

        return $response;
    }

    // kyc validation check
    public function kycValidationCheck($userId)
    {
        $response = [
            'success' => true,
            'message' => __('success ')
        ];
        if (settings('kyc_enable_for_withdrawal') == STATUS_ACTIVE) {
            if (settings('kyc_nid_enable_for_withdrawal') == STATUS_ACTIVE) {
                $checkNid = checkUserKyc($userId, KYC_NID_REQUIRED, __('withdrawal '));
                if ($checkNid['success'] == false) {
                    $response = [
                        'success' => false,
                        'message' => $checkNid['message']
                    ];
                    return $response;
                } else {
                    $response = [
                        'success' => true,
                        'message' => __('success ')
                    ];
                }
            }
            if(settings('kyc_passport_enable_for_withdrawal') ==  STATUS_ACTIVE) {
                $checkPass = checkUserKyc($userId, KYC_PASSPORT_REQUIRED, __('withdrawal '));
                if ($checkPass['success'] == false) {
                    $response = [
                        'success' => false,
                        'message' => $checkPass['message']
                    ];
                    return $response;
                } else {
                    $response = [
                        'success' => true,
                        'message' => __('success ')
                    ];
                }
            }
            if(settings('kyc_driving_enable_for_withdrawal') ==  STATUS_ACTIVE) {
                $checkDrive = checkUserKyc($userId, KYC_DRIVING_REQUIRED, __('withdrawal '));
                if ($checkDrive['success'] == false) {
                    $response = [
                        'success' => false,
                        'message' => $checkDrive['message']
                    ];
                    return $response;
                } else {
                    $response = [
                        'success' => true,
                        'message' => __('success ')
                    ];
                }
            }
        } else {
            $response = [
                'success' => true,
                'message' => __('success ')
            ];
        }

        return $response;
    }

    // send coin with bitgo
    public function sendCoinWithBitgo($transaction)
    {
        try {
            $coin = Coin::join('coin_settings','coin_settings.coin_id', '=', 'coins.id')
                ->where(['coins.coin_type' => $transaction->coin_type])
                ->select('coins.*', 'coin_settings.*')
                ->first();
            if ($coin) {
                $currency =  !empty($transaction->network_type) ? $transaction->network_type : $transaction->coin_type;
                $response = $this->sendBitgoCoin($currency,$coin->bitgo_wallet_id,$transaction->amount,$transaction->address,decryptId($coin->bitgo_wallet));
            } else {
                $response = [
                    'success' => false,
                    'message' => __('Coin not found'),
                    'data' => ''
                ];
            }
        } catch (\Exception $e) {
            storeException('sendCoinWithBitgo', $e->getMessage());
            $response = [
                'success' => false,
                'message' => __('Something went wrong'),
                'data' => ''
            ];
        }
        return $response;
    }

    // send bitgo coins
    public function sendBitgoCoin($coinType,$walletId,$amount,$address,$walletPassphrase)
    {
        try {
            $bitgoService = new BitgoWalletService();
            $bitgoResponse = $bitgoService->sendCoinsWithBitgo($coinType,$walletId,$amount,$address,$walletPassphrase);
            storeBotException('send coin api response', json_encode($bitgoResponse));

            if ($bitgoResponse['success'] == true) {
                $response = [
                    'success' => true,
                    'message' => __('Coin send successful'),
                    'data' => $bitgoResponse['data']['txid'],
                ];
            } else {
                storeBotException('Bitgo sendCoin', $bitgoResponse['message']);
                $response = [
                    'success' => false,
                    'message' => $bitgoResponse['message'],
                    'data' => ""
                ];
            }

        } catch (\Exception $e) {
            storeException('sendBitgoCoin', $e->getMessage());
            $response = [
                'success' => false,
                'message' => __('Something went wrong'),
                'data' => ""
            ];
        }
        return $response;
    }

    // send coin with erc20 api
    public function sendTokenWithEvm($transaction)
    {
        try {
            $coinData = Coin::join('coin_networks','coin_networks.currency_id', '=', 'coins.id')
                ->where(['coins.coin_type' => $transaction->coin_type])
                ->select('coins.*', 'coin_networks.*')
                ->first();

            if ($coinData) {

                $evm_service = new EvmWalletService();
                $requestData = [
                    "id" => $transaction->id,
                ];
                $result = $evm_service->withdrawalApproveProcess($requestData);
                storeException('sendTokenWithEvm', json_encode($result));
                if ($result['success'] ==  true) {

                    $response = [
                        'success' => true,
                        'message' => __('Coin sent successfully'),
                        'data' => []
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => $result['message'] ?? __('Coin send failed'),
                        'data' => []
                    ];
                }
            } else {
                $response = [
                    'success' => false,
                    'message' => __('Coin not found'),
                    'data' => []
                ];
            }
        } catch (\Exception $e) {
            storeException('sendTokenWithEvm', $e->getMessage());
            $response = [
                'success' => false,
                'message' => __('Something went wrongg'),
                'data' => []
            ];
        }
        return $response;
    }

    // pre withdrawal process
    public function preWithdrawalProcess($request)
    {
        try {
            $user = Auth::user();
            $wallet = Wallet::join('coins', 'coins.id', '=', 'wallets.coin_id')
                ->where(['wallets.id'=>$request->wallet_id, 'wallets.user_id'=> $user->id])
                ->select('wallets.*', 'coins.status as coin_status', 'coins.is_withdrawal', 'coins.minimum_withdrawal',
                    'coins.maximum_withdrawal', 'coins.withdrawal_fees', 'coins.max_send_limit','coins.withdrawal_fees_type','coins.network')
                ->first();
            if ($wallet) {
                if(isset($request->network_id)){
                    if(!$coinNetwork = CoinNetwork::where(["network_id" => $request->network_id, "currency_id" => $wallet->coin_id])->first()){
                        return responseData(false,__('Coin network not found'));
                    }
                    $wallet->withdrawal_fees      = $coinNetwork->withdrawal_fees ?? 0;
                    $wallet->withdrawal_fees_type = $coinNetwork->withdrawal_fees_type ?? 2;
                }
                $amount = $request->amount ? $request->amount : 0;
                $address = $request->address ? $request->address : 'address';
                $fees = check_withdrawal_fees($amount, $wallet->withdrawal_fees,$wallet->withdrawal_fees_type);
                $response = [
                    'data' => [
                        'coin_type' => $wallet->coin_type,
                        'fees' => $fees,
                        'amount' => $amount,
                        'fees_percentage' => $wallet->withdrawal_fees,
                        'fees_type' => $wallet->withdrawal_fees_type,
                    ],
                    'success' => true,
                    'message' => __('Success')
                ];
                $walletAddress = $this->isInternalAddress($address, $wallet->coin_type);
                if ($walletAddress) {
                    $response = [
                    'data' => [
                        'coin_type' => $wallet->coin_type,
                        'fees' => 0,
                        'amount' => $amount,
                        'fees_percentage' => $wallet->withdrawal_fees,
                        'fees_type' => $wallet->withdrawal_fees_type,
                    ],
                    'success' => true,
                    'message' => __('Success')
                ];
                }
            } else {
                $response = [
                    'success' => false,
                    'message' => __('Wallet not found'),
                    'data' => []
                ];
            }
        } catch (\Exception $e) {
            storeException('pre withdrawalProcess ', $e->getMessage());
            $response = [
                'success' => false,
                'message' => __('Something went wrong'),
                'data' => []
            ];
        }

        return $response;
    }

    private function sendEmailAndNotification($title, $message, $user)
    {
        (new MyCommonService())->sendNotificationToUserUsingSocket(
            $user->id,
            $title,
            $message
        );
        $emailData = [
            'to' => $user->email,
            'name' => $user->first_name.' '.$user->last_name,
            'subject' => $title,
            'email_header' => $title,
            'email_message' => $message,
            'mailTemplate' => emailTemplateName('genericemail')
        ];
        dispatch(new MailSend($emailData))->onQueue('send-mail');
    }
}
