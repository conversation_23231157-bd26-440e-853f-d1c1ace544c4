<?php

use App\Http\Controllers\Api\NotifyTransactionController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\JibitCallbackController;
use App\Http\Controllers\ByBit\UserController as ByBitUserController;
use App\Http\Controllers\DepositWalletController;
use App\Http\Controllers\Kucoin\AccountApiController;
use App\Http\Controllers\Kucoin\AccountController;
// use App\Http\Controllers\PublicController;
use GuzzleHttp\Client;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use App\Http\Controllers\JibitWebhookController;
use App\Http\Controllers\UserLevelController;

Route::post('/deploy-hook', function (\Illuminate\Http\Request $request) {
    $secret = 'Sw@Mn@912912';

    $signature = 'sha256=' . hash_hmac('sha256', $request->getContent(), $secret);
    if (!hash_equals($signature, $request->header('X-Hub-Signature-256'))) {
        abort(403, 'Invalid signature');
    }

    // اجرای deploy.sh
    exec('/var/www/api.exchangim.com/html/laracoin-ex/deploy.sh >> /var/log/deploy.log 2>&1 &');

    return response()->json(['status' => 'ok']);
});
// Route::post('test',function(){
//     $client = new Client();
//     $headers = [
//         'Content-Type' => 'application/json'
//     ];
//     $body = '{
//         "jsonrpc": "2.0",
//         "method": "createwallet",
//         "id": "getblock.io"
//     }';
//     $url = "https://go.getblock.io/5df709031ca24cb0a02ceba17b9b34ea";
//     $response = $client->post($url, [
//         'headers' => $headers,
//         'json' => $body,
//     ]);
//     return json_decode($response->getBody(), true);
// });

Route::get('test', function () {
    $client = new Client();
    $url = "https://eth.getblock.io/mainnet/";

    $response = $client->post($url, [
        'headers' => [
            'x-api-key' => '5df709031ca24cb0a02ceba17b9b34ea',
            'Content-Type' => 'application/json',
        ],
        'json' => [
            'jsonrpc' => '2.0',
            'method' => 'eth_getBalance',
            'params' => [Str::random(60), 'latest'],
            'id' => 1
        ],
    ]);

    return json_decode($response->getBody(), true);

});
// Route::get('test', function () {
//     $curl = curl_init();

//     curl_setopt_array($curl, array(
//         CURLOPT_URL => 'https://go.getblock.io/5df709031ca24cb0a02ceba17b9b34ea/jsonrpc',
//         CURLOPT_RETURNTRANSFER => true,
//         CURLOPT_ENCODING => '',
//         CURLOPT_MAXREDIRS => 10,
//         CURLOPT_TIMEOUT => 0,
//         CURLOPT_FOLLOWLOCATION => true,
//         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
//         CURLOPT_CUSTOMREQUEST => 'POST',
//         CURLOPT_POSTFIELDS =>'{
//             "jsonrpc": "2.0",
//             "method": "eth_getBlockByNumber",
//             "params": ["latest", false],
//             "id": "getblock.io"
//         }',
//         CURLOPT_HTTPHEADER => array(
//             'Content-Type: application/json'
//         ),
//     ));

//     $response = curl_exec($curl);

//     curl_close($curl);
//     return [$response];
// });


Route::prefix('auth')->controller(AuthController::class)->group(function () {
    Route::post('/login', 'login')->middleware('decrypt.rsa');
    Route::post('/sendOtp', 'sendOtp')->middleware('decrypt.rsa');
    Route::post('/verifyOtp', 'verifyOtp')->middleware('decrypt.rsa');
});

// Jibit Payment Callback Routes (no authentication required)
Route::prefix('jibit')->controller(JibitCallbackController::class)->group(function () {
    Route::post('callback', 'handleCallback')->name('jibit.callback');
    Route::post('webhook', 'handleWebhook')->name('jibit.webhook');
});
Route::post('/webhook/jibit', [JibitWebhookController::class, 'handle']);

// User API Routes

// Route::middleware('auth:api')->group(function () {
//     Route::get('support/levels', [PublicController::class, 'support_levels']);
//     Route::get('support/units', [PublicController::class, 'support_units']);
//     Route::apiResource('sub-account', AccountController::class);
//     Route::apiResource('sub-account/api', AccountApiController::class);
//     Route::prefix(prefix: 'bybit')->group(callback: function (): void {
//         Route::apiResource(name: 'user', controller: ByBitUserController::class);
//     });
    // Route::post('deposit/wallet-address', [DepositWalletController::class, 'getWalletAddress']);
// });
// Route::get(uri: '/create', action: [PublicController::class, 'test_create']);
Route::post('/notify/receive/transaction', [NotifyTransactionController::class, 'receive']);
Route::fallback(function () {
    abort(404, 'API resource not found');
});

// User Level Management API Routes
Route::middleware('auth:sanctum')->prefix('user/levels')->name('user.levels.')->group(function () {
    Route::get('/', [\App\Http\Controllers\UserLevelController::class, 'index'])->name('index');
    Route::get('/current', [\App\Http\Controllers\UserLevelController::class, 'showCurrentLevel'])->name('current');
    Route::get('/next', [\App\Http\Controllers\UserLevelController::class, 'showNextLevel'])->name('next');
    Route::post('/request-upgrade', [\App\Http\Controllers\UserLevelController::class, 'requestUpgrade'])->name('request-upgrade');
    Route::get('/check-status', [\App\Http\Controllers\UserLevelController::class, 'checkUpgradeStatus'])->name('check-status');
    Route::get('/history', [\App\Http\Controllers\UserLevelController::class, 'upgradeHistory'])->name('history');
    Route::get('/documents', [\App\Http\Controllers\UserLevelController::class, 'documentStatus'])->name('documents');
    Route::get('/details', [\App\Http\Controllers\UserLevelController::class, 'levelDetails'])->name('details');
    
    // Daily transaction limits
    Route::get('/daily-limits', [\App\Http\Controllers\UserLevelController::class, 'dailyTransactionLimits'])->name('daily-limits');
    Route::get('/currency-limits', [\App\Http\Controllers\UserLevelController::class, 'currencyTransactionLimits'])->name('currency-limits');
});

Route::prefix('blog')->group(function () {
    Route::get('posts', [\App\Http\Controllers\Api\BlogController::class, 'posts']);
    Route::get('posts/{id}', [\App\Http\Controllers\Api\BlogController::class, 'show']);
    Route::get('categories', [\App\Http\Controllers\Api\BlogController::class, 'categories']);
    Route::get('tags', [\App\Http\Controllers\Api\BlogController::class, 'tags']);
});
